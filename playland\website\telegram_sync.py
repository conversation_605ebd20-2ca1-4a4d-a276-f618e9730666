#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Сервис синхронизации данных между веб-приложением и Telegram ботом.
"""

import sqlite3
import json
import logging
from datetime import datetime
from typing import Optional, Dict, Any, List
from .models import db, User, Application

logger = logging.getLogger(__name__)

class TelegramSyncService:
    """Сервис синхронизации данных с Telegram ботом"""
    
    def __init__(self, db_path: str = None):
        self.db_path = db_path or "website/instance/playland_unified.db"
    
    def sync_user_from_telegram(self, telegram_data: Dict[str, Any]) -> Optional[User]:
        """Синхронизирует пользователя из Telegram бота"""
        try:
            telegram_id = telegram_data.get('telegram_id')
            if not telegram_id:
                logger.error("Отсутствует telegram_id в данных")
                return None
            
            # Ищем существующего пользователя
            user = User.find_by_telegram_id(telegram_id)
            
            if user:
                # Обновляем существующего пользователя
                user.sync_with_telegram_data(telegram_data)
                logger.info(f"Обновлен пользователь {user.id} из Telegram {telegram_id}")
            else:
                # Создаем нового пользователя
                user = User()
                user.telegram_id = telegram_id
                user.username = telegram_data.get('username') or f"tg_user_{telegram_id}"
                user.first_name = telegram_data.get('first_name')
                user.last_name = telegram_data.get('last_name')
                user.telegram = telegram_data.get('username')
                user.is_activated = True  # Пользователи из Telegram автоматически активированы
                user.registration_date = datetime.now().isoformat()
                user.last_activity = datetime.now().isoformat()
                
                db.session.add(user)
                logger.info(f"Создан новый пользователь из Telegram {telegram_id}")
            
            db.session.commit()
            return user
            
        except Exception as e:
            logger.error(f"Ошибка при синхронизации пользователя из Telegram: {e}")
            db.session.rollback()
            return None
    
    def sync_application_from_telegram(self, application_data: Dict[str, Any]) -> Optional[Application]:
        """Синхронизирует заявку из Telegram бота"""
        try:
            telegram_user_id = application_data.get('telegram_user_id')
            minecraft_nickname = application_data.get('minecraft_nickname')
            
            if not telegram_user_id or not minecraft_nickname:
                logger.error("Отсутствуют обязательные поля в данных заявки")
                return None
            
            # Ищем пользователя
            user = User.find_by_telegram_id(telegram_user_id)
            
            # Создаем заявку
            application = Application.create_from_telegram(
                telegram_user_id=telegram_user_id,
                minecraft_nickname=minecraft_nickname,
                discord_tag=application_data.get('discord_tag'),
                reason=application_data.get('reason')
            )
            
            # Связываем с пользователем если найден
            if user:
                application.link_to_user(user)
            
            # Заполняем дополнительные поля
            application.first_name = application_data.get('first_name')
            application.last_name = application_data.get('last_name')
            application.telegram_username = application_data.get('telegram_username')
            application.status = application_data.get('status', 'pending')
            
            if application_data.get('timestamp'):
                application.timestamp = application_data['timestamp']
            
            db.session.add(application)
            db.session.commit()
            
            logger.info(f"Синхронизирована заявка {application.id} из Telegram")
            return application
            
        except Exception as e:
            logger.error(f"Ошибка при синхронизации заявки из Telegram: {e}")
            db.session.rollback()
            return None
    
    def link_telegram_to_web_user(self, telegram_id: int, web_user_id: int) -> bool:
        """Связывает Telegram аккаунт с веб-пользователем"""
        try:
            web_user = User.query.get(web_user_id)
            if not web_user:
                logger.error(f"Веб-пользователь {web_user_id} не найден")
                return False
            
            # Проверяем, не связан ли уже этот Telegram с другим пользователем
            existing_user = User.find_by_telegram_id(telegram_id)
            if existing_user and existing_user.id != web_user_id:
                logger.error(f"Telegram {telegram_id} уже связан с пользователем {existing_user.id}")
                return False
            
            # Связываем аккаунты
            web_user.telegram_id = telegram_id
            
            # Переносим заявки из Telegram
            telegram_applications = Application.query.filter_by(
                telegram_user_id=telegram_id,
                user_id=None
            ).all()
            
            for app in telegram_applications:
                app.link_to_user(web_user)
            
            db.session.commit()
            logger.info(f"Связан Telegram {telegram_id} с веб-пользователем {web_user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка при связывании аккаунтов: {e}")
            db.session.rollback()
            return False
    
    def get_user_applications(self, telegram_id: int) -> List[Dict[str, Any]]:
        """Получает заявки пользователя по Telegram ID"""
        try:
            # Ищем заявки по telegram_user_id или через связанного пользователя
            user = User.find_by_telegram_id(telegram_id)
            
            applications = []
            
            # Заявки напрямую по telegram_user_id
            telegram_apps = Application.query.filter_by(telegram_user_id=telegram_id).all()
            applications.extend(telegram_apps)
            
            # Заявки через связанного пользователя
            if user:
                user_apps = Application.query.filter_by(user_id=user.id).all()
                applications.extend(user_apps)
            
            # Убираем дубликаты
            unique_apps = {}
            for app in applications:
                unique_apps[app.id] = app
            
            return [app.to_dict() for app in unique_apps.values()]
            
        except Exception as e:
            logger.error(f"Ошибка при получении заявок пользователя {telegram_id}: {e}")
            return []
    
    def update_application_status(self, application_id: int, status: str, 
                                processed_by: int = None, rejection_reason: str = None) -> bool:
        """Обновляет статус заявки"""
        try:
            application = Application.query.get(application_id)
            if not application:
                logger.error(f"Заявка {application_id} не найдена")
                return False
            
            application.status = status
            application.processed_at = datetime.now()
            
            if processed_by:
                application.processed_by = processed_by
                application.reviewed_by = processed_by
            
            if rejection_reason:
                application.rejection_reason = rejection_reason
            
            if status == 'approved':
                application.approved_at = datetime.now()
                application.approved_by = processed_by
            elif status == 'rejected':
                application.rejected_at = datetime.now()
                application.rejected_by = processed_by
            
            db.session.commit()
            logger.info(f"Обновлен статус заявки {application_id} на {status}")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка при обновлении статуса заявки {application_id}: {e}")
            db.session.rollback()
            return False
    
    def get_pending_applications(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Получает ожидающие рассмотрения заявки"""
        try:
            applications = Application.query.filter_by(status='pending').limit(limit).all()
            return [app.to_dict() for app in applications]
        except Exception as e:
            logger.error(f"Ошибка при получении ожидающих заявок: {e}")
            return []
    
    def get_application_statistics(self) -> Dict[str, int]:
        """Получает статистику заявок"""
        try:
            stats = {
                'total': Application.query.count(),
                'pending': Application.query.filter_by(status='pending').count(),
                'approved': Application.query.filter_by(status='approved').count(),
                'rejected': Application.query.filter_by(status='rejected').count(),
                'telegram_source': Application.query.filter_by(source='telegram').count(),
                'web_source': Application.query.filter_by(source='web').count()
            }
            return stats
        except Exception as e:
            logger.error(f"Ошибка при получении статистики: {e}")
            return {}
    
    def cleanup_orphaned_data(self) -> Dict[str, int]:
        """Очищает потерянные данные"""
        try:
            cleanup_stats = {
                'orphaned_applications': 0,
                'duplicate_users': 0
            }
            
            # Находим заявки без связанного пользователя (только с telegram_user_id)
            orphaned_apps = Application.query.filter(
                Application.user_id.is_(None),
                Application.telegram_user_id.isnot(None)
            ).all()
            
            for app in orphaned_apps:
                # Пытаемся найти пользователя по telegram_id
                user = User.find_by_telegram_id(app.telegram_user_id)
                if user:
                    app.link_to_user(user)
                    cleanup_stats['orphaned_applications'] += 1
            
            db.session.commit()
            logger.info(f"Очистка завершена: {cleanup_stats}")
            return cleanup_stats
            
        except Exception as e:
            logger.error(f"Ошибка при очистке данных: {e}")
            db.session.rollback()
            return {}

# Глобальный экземпляр сервиса
telegram_sync = TelegramSyncService()

def init_telegram_sync(app):
    """Инициализирует сервис синхронизации с Flask приложением"""
    with app.app_context():
        telegram_sync.cleanup_orphaned_data()
        logger.info("Сервис синхронизации с Telegram инициализирован")
