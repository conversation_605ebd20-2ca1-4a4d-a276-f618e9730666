#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Скрипт анализа структуры баз данных для объединения веб-приложения и Telegram бота.
"""

import sqlite3
import os
import json
from datetime import datetime
from pathlib import Path

class DatabaseAnalyzer:
    """Анализатор структуры баз данных"""
    
    def __init__(self):
        self.web_db_path = "website/instance/playland.db"
        self.telegram_db_path = None
        self.analysis_result = {}
        
        # Поиск базы данных Telegram бота
        possible_paths = [
            "tg_bot/bot.db",
            "tg_bot/telegram.db", 
            "tg_bot/database.db",
            "telegram_bot.db",
            "bot.db"
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                self.telegram_db_path = path
                break
    
    def analyze_database_structure(self, db_path, db_name):
        """Анализирует структуру базы данных"""
        if not os.path.exists(db_path):
            return {
                "exists": False,
                "error": f"База данных не найдена: {db_path}"
            }
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Получаем список таблиц
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            structure = {
                "exists": True,
                "path": db_path,
                "tables": {},
                "indexes": [],
                "size_mb": round(os.path.getsize(db_path) / (1024 * 1024), 2)
            }
            
            # Анализируем каждую таблицу
            for table in tables:
                # Получаем структуру таблицы
                cursor.execute(f"PRAGMA table_info({table})")
                columns = cursor.fetchall()
                
                # Получаем количество записей
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                row_count = cursor.fetchone()[0]
                
                # Получаем примеры данных (первые 3 записи)
                cursor.execute(f"SELECT * FROM {table} LIMIT 3")
                sample_data = cursor.fetchall()
                
                structure["tables"][table] = {
                    "columns": [
                        {
                            "name": col[1],
                            "type": col[2],
                            "not_null": bool(col[3]),
                            "default": col[4],
                            "primary_key": bool(col[5])
                        }
                        for col in columns
                    ],
                    "row_count": row_count,
                    "sample_data": sample_data[:3] if sample_data else []
                }
            
            # Получаем индексы
            cursor.execute("SELECT name, sql FROM sqlite_master WHERE type='index' AND name NOT LIKE 'sqlite_%'")
            indexes = cursor.fetchall()
            structure["indexes"] = [{"name": idx[0], "sql": idx[1]} for idx in indexes]
            
            conn.close()
            return structure
            
        except Exception as e:
            return {
                "exists": True,
                "error": f"Ошибка при анализе {db_path}: {str(e)}"
            }
    
    def find_user_tables(self, structure):
        """Находит таблицы пользователей"""
        user_tables = []
        
        if not structure.get("exists"):
            return user_tables
        
        for table_name, table_info in structure.get("tables", {}).items():
            columns = [col["name"].lower() for col in table_info["columns"]]
            
            # Ищем таблицы, которые могут содержать пользователей
            user_indicators = ["user", "username", "email", "telegram", "discord"]
            
            if any(indicator in table_name.lower() for indicator in user_indicators):
                user_tables.append({
                    "table": table_name,
                    "columns": table_info["columns"],
                    "row_count": table_info["row_count"],
                    "confidence": "high"
                })
            elif any(indicator in " ".join(columns) for indicator in user_indicators):
                user_tables.append({
                    "table": table_name,
                    "columns": table_info["columns"],
                    "row_count": table_info["row_count"],
                    "confidence": "medium"
                })
        
        return user_tables
    
    def compare_user_schemas(self, web_users, telegram_users):
        """Сравнивает схемы пользователей"""
        comparison = {
            "web_tables": web_users,
            "telegram_tables": telegram_users,
            "common_fields": [],
            "web_only_fields": [],
            "telegram_only_fields": [],
            "conflicts": []
        }
        
        # Получаем все поля из обеих схем
        web_fields = set()
        telegram_fields = set()
        
        for table in web_users:
            for col in table["columns"]:
                web_fields.add(col["name"].lower())
        
        for table in telegram_users:
            for col in table["columns"]:
                telegram_fields.add(col["name"].lower())
        
        comparison["common_fields"] = list(web_fields & telegram_fields)
        comparison["web_only_fields"] = list(web_fields - telegram_fields)
        comparison["telegram_only_fields"] = list(telegram_fields - web_fields)
        
        return comparison
    
    def generate_migration_plan(self, web_structure, telegram_structure):
        """Генерирует план миграции"""
        plan = {
            "strategy": "merge_into_web_db",
            "steps": [],
            "risks": [],
            "backup_required": True
        }
        
        # Анализируем пользовательские таблицы
        web_users = self.find_user_tables(web_structure)
        telegram_users = self.find_user_tables(telegram_structure)
        
        if not web_users:
            plan["risks"].append("Не найдены пользовательские таблицы в веб-БД")
        
        if not telegram_users:
            plan["risks"].append("Не найдены пользовательские таблицы в Telegram БД")
            plan["steps"].append("Создать таблицы для Telegram данных в веб-БД")
        else:
            plan["steps"].append("Мигрировать данные пользователей из Telegram БД в веб-БД")
        
        # Проверяем наличие таблиц безопасности
        security_tables = ["trusted_devices", "login_history", "audit_logs"]
        for table in security_tables:
            if table not in web_structure.get("tables", {}):
                plan["steps"].append(f"Создать таблицу {table} для системы безопасности")
        
        # Добавляем шаги для интеграции
        plan["steps"].extend([
            "Добавить поля telegram_id, telegram_username в таблицу users",
            "Создать индексы для быстрого поиска по telegram_id",
            "Обновить модели данных в коде",
            "Создать процедуры синхронизации данных",
            "Протестировать интеграцию"
        ])
        
        return plan
    
    def run_analysis(self):
        """Запускает полный анализ"""
        print("=== Анализ баз данных PlayLand ===\n")
        
        # Анализируем веб-базу данных
        print("1. Анализ базы данных веб-приложения...")
        web_structure = self.analyze_database_structure(self.web_db_path, "web")
        
        if web_structure["exists"]:
            print(f"✓ Найдена: {self.web_db_path}")
            print(f"  Размер: {web_structure['size_mb']} MB")
            print(f"  Таблиц: {len(web_structure['tables'])}")
            print(f"  Таблицы: {', '.join(web_structure['tables'].keys())}")
        else:
            print(f"✗ {web_structure.get('error', 'Не найдена')}")
        
        # Анализируем Telegram базу данных
        print("\n2. Анализ базы данных Telegram бота...")
        if self.telegram_db_path:
            telegram_structure = self.analyze_database_structure(self.telegram_db_path, "telegram")
            if telegram_structure["exists"]:
                print(f"✓ Найдена: {self.telegram_db_path}")
                print(f"  Размер: {telegram_structure['size_mb']} MB")
                print(f"  Таблиц: {len(telegram_structure['tables'])}")
                print(f"  Таблицы: {', '.join(telegram_structure['tables'].keys())}")
            else:
                print(f"✗ {telegram_structure.get('error', 'Ошибка анализа')}")
        else:
            print("✗ База данных Telegram бота не найдена")
            telegram_structure = {"exists": False}
        
        # Анализируем пользовательские таблицы
        print("\n3. Анализ пользовательских данных...")
        web_users = self.find_user_tables(web_structure) if web_structure["exists"] else []
        telegram_users = self.find_user_tables(telegram_structure) if telegram_structure["exists"] else []
        
        print(f"  Веб-приложение: {len(web_users)} пользовательских таблиц")
        for table in web_users:
            print(f"    - {table['table']} ({table['row_count']} записей)")
        
        print(f"  Telegram бот: {len(telegram_users)} пользовательских таблиц")
        for table in telegram_users:
            print(f"    - {table['table']} ({table['row_count']} записей)")
        
        # Сравниваем схемы
        if web_users and telegram_users:
            print("\n4. Сравнение схем данных...")
            comparison = self.compare_user_schemas(web_users, telegram_users)
            print(f"  Общие поля: {len(comparison['common_fields'])}")
            print(f"  Только в веб: {len(comparison['web_only_fields'])}")
            print(f"  Только в Telegram: {len(comparison['telegram_only_fields'])}")
        
        # Генерируем план миграции
        print("\n5. План миграции...")
        migration_plan = self.generate_migration_plan(web_structure, telegram_structure)
        print(f"  Стратегия: {migration_plan['strategy']}")
        print(f"  Шагов: {len(migration_plan['steps'])}")
        print(f"  Рисков: {len(migration_plan['risks'])}")
        
        # Сохраняем результаты
        self.analysis_result = {
            "timestamp": datetime.now().isoformat(),
            "web_database": web_structure,
            "telegram_database": telegram_structure,
            "web_user_tables": web_users,
            "telegram_user_tables": telegram_users,
            "migration_plan": migration_plan
        }
        
        # Сохраняем в файл
        with open("database/analysis_result.json", "w", encoding="utf-8") as f:
            json.dump(self.analysis_result, f, indent=2, ensure_ascii=False)
        
        print(f"\n✓ Результаты сохранены в database/analysis_result.json")
        
        return self.analysis_result

def main():
    """Главная функция"""
    analyzer = DatabaseAnalyzer()
    result = analyzer.run_analysis()
    
    print("\n=== Рекомендации ===")
    
    if result["web_database"]["exists"] and result["telegram_database"]["exists"]:
        print("✓ Обе базы данных найдены - можно выполнить полную миграцию")
    elif result["web_database"]["exists"]:
        print("⚠ Найдена только веб-БД - создадим таблицы для Telegram интеграции")
    else:
        print("✗ Веб-БД не найдена - требуется инициализация")
    
    print("\nСледующие шаги:")
    for i, step in enumerate(result["migration_plan"]["steps"], 1):
        print(f"{i}. {step}")

if __name__ == "__main__":
    main()
