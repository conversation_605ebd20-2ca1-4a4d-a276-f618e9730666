#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Модуль интеграций PlayLand.
Содержит интеграции с различными платформами и сервисами.
"""

__version__ = "1.0.0"
__author__ = "PlayLand Team"

# Импорты основных интеграций
try:
    from .cross_platform import init_integrations
    from .telegram import init_telegram_integration
    from .discord import init_discord_integration
    from .email_service import EmailService
    
    __all__ = [
        'init_integrations',
        'init_telegram_integration', 
        'init_discord_integration',
        'EmailService'
    ]
except ImportError as e:
    # Если какие-то интеграции недоступны, создаем заглушки
    print(f"⚠️ Некоторые интеграции недоступны: {e}")
    
    def init_integrations(app):
        """Заглушка для инициализации интеграций"""
        pass
    
    def init_telegram_integration(app):
        """Заглушка для Telegram интеграции"""
        pass
    
    def init_discord_integration(app):
        """Заглушка для Discord интеграции"""
        pass
    
    class EmailService:
        """Заглушка для email сервиса"""
        def __init__(self, app=None):
            pass
        
        def send_email(self, *args, **kwargs):
            return False
    
    __all__ = [
        'init_integrations',
        'init_telegram_integration',
        'init_discord_integration', 
        'EmailService'
    ]
