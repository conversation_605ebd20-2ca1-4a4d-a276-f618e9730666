{% extends "base.html" %}

{% block title %}Тикет #{{ ticket.id }} - {{ ticket.title }}{% endblock %}

{% block content %}
<div class="ticket-chat-container">
    <div class="ticket-header">
        <div class="ticket-info">
            <h1 class="ticket-title">
                <i class="fas fa-ticket-alt"></i>
                Тикет #{{ ticket.id }}: {{ ticket.title }}
            </h1>
            <div class="ticket-meta">
                <span class="ticket-status status-{{ ticket.status }}">
                    {% if ticket.status == 'open' %}
                        <i class="fas fa-circle"></i> Открыт
                    {% elif ticket.status == 'answered' %}
                        <i class="fas fa-reply"></i> Отвечен
                    {% elif ticket.status == 'closed' %}
                        <i class="fas fa-check-circle"></i> Закрыт
                    {% endif %}
                </span>
                <span class="ticket-date">
                    <i class="fas fa-calendar"></i>
                    Создан: {{ ticket.created_at.strftime('%d.%m.%Y %H:%M') }}
                </span>
                {% if ticket.application_type %}
                <span class="ticket-type">
                    <i class="fas fa-tag"></i>
                    {{ ticket.application_type }}
                </span>
                {% endif %}
            </div>
        </div>
        
        {% if current_user.is_admin %}
        <div class="ticket-actions">
            <button class="btn btn-primary" onclick="changeTicketStatus('{{ ticket.id }}', 'answered')">
                <i class="fas fa-reply"></i> Отметить отвеченным
            </button>
            <button class="btn btn-secondary" onclick="changeTicketStatus('{{ ticket.id }}', 'closed')">
                <i class="fas fa-times"></i> Закрыть тикет
            </button>
        </div>
        {% endif %}
    </div>

    <div class="chat-container">
        <div class="chat-messages" id="chatMessages">
            <!-- Первое сообщение - содержимое тикета -->
            <div class="message message-user">
                <div class="message-avatar">
                    <img src="{{ url_for('static', filename='images/default-avatar.png') }}" alt="Avatar">
                </div>
                <div class="message-content">
                    <div class="message-header">
                        <span class="message-author">{{ ticket.user.get_display_name() }}</span>
                        <span class="message-time">{{ ticket.created_at.strftime('%d.%m.%Y %H:%M') }}</span>
                        <span class="message-platform">
                            <i class="fas fa-globe"></i> Сайт
                        </span>
                    </div>
                    <div class="message-text">{{ ticket.content }}</div>
                </div>
            </div>

            <!-- Сообщения чата -->
            {% for message in messages %}
            <div class="message {% if message.is_admin_reply %}message-admin{% else %}message-user{% endif %}">
                <div class="message-avatar">
                    <img src="{{ url_for('static', filename='images/default-avatar.png') }}" alt="Avatar">
                </div>
                <div class="message-content">
                    <div class="message-header">
                        <span class="message-author">{{ message.user.get_display_name() }}</span>
                        <span class="message-time">{{ message.created_at.strftime('%d.%m.%Y %H:%M') }}</span>
                        <span class="message-platform">
                            {% if message.source_platform == 'telegram' %}
                                <i class="fab fa-telegram"></i> Telegram
                            {% else %}
                                <i class="fas fa-globe"></i> Сайт
                            {% endif %}
                        </span>
                        {% if message.is_admin_reply %}
                        <span class="admin-badge">
                            <i class="fas fa-shield-alt"></i> Администратор
                        </span>
                        {% endif %}
                    </div>
                    <div class="message-text">{{ message.content }}</div>
                </div>
            </div>
            {% endfor %}
        </div>

        {% if ticket.status != 'closed' %}
        <div class="chat-input-container">
            <form id="messageForm" class="message-form">
                <input type="hidden" name="ticket_id" value="{{ ticket.id }}">
                <div class="input-group">
                    <textarea 
                        name="content" 
                        id="messageInput" 
                        placeholder="Введите ваше сообщение..." 
                        rows="3"
                        required
                    ></textarea>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i>
                        Отправить
                    </button>
                </div>
            </form>
        </div>
        {% else %}
        <div class="chat-closed-notice">
            <i class="fas fa-lock"></i>
            Тикет закрыт. Новые сообщения недоступны.
        </div>
        {% endif %}
    </div>
</div>

<style>
.ticket-chat-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
}

.ticket-header {
    background: rgba(0, 0, 0, 0.8);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid rgba(0, 255, 0, 0.3);
}

.ticket-title {
    color: #00ff00;
    margin: 0 0 10px 0;
    font-size: 1.5rem;
}

.ticket-meta {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.ticket-meta span {
    color: #ccc;
    font-size: 0.9rem;
}

.ticket-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: bold;
}

.status-open { background: rgba(255, 193, 7, 0.2); color: #ffc107; }
.status-answered { background: rgba(40, 167, 69, 0.2); color: #28a745; }
.status-closed { background: rgba(108, 117, 125, 0.2); color: #6c757d; }

.chat-container {
    background: rgba(0, 0, 0, 0.8);
    border-radius: 10px;
    border: 1px solid rgba(0, 255, 0, 0.3);
    overflow: hidden;
}

.chat-messages {
    max-height: 600px;
    overflow-y: auto;
    padding: 20px;
}

.message {
    display: flex;
    margin-bottom: 20px;
    animation: fadeInUp 0.3s ease;
}

.message-avatar img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 15px;
}

.message-content {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 15px;
}

.message-admin .message-content {
    background: rgba(0, 255, 0, 0.1);
    border-left: 3px solid #00ff00;
}

.message-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.message-author {
    font-weight: bold;
    color: #00ff00;
}

.message-time {
    color: #999;
}

.message-platform {
    color: #666;
}

.admin-badge {
    background: rgba(0, 255, 0, 0.2);
    color: #00ff00;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.8rem;
}

.message-text {
    color: #fff;
    line-height: 1.5;
    white-space: pre-wrap;
}

.chat-input-container {
    border-top: 1px solid rgba(0, 255, 0, 0.3);
    padding: 20px;
}

.input-group {
    display: flex;
    gap: 10px;
    align-items: flex-end;
}

.input-group textarea {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(0, 255, 0, 0.3);
    border-radius: 8px;
    padding: 12px;
    color: #fff;
    resize: vertical;
    min-height: 60px;
}

.input-group textarea:focus {
    outline: none;
    border-color: #00ff00;
    box-shadow: 0 0 10px rgba(0, 255, 0, 0.3);
}

.chat-closed-notice {
    text-align: center;
    padding: 20px;
    color: #999;
    font-style: italic;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@media (max-width: 768px) {
    .ticket-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .ticket-meta {
        flex-direction: column;
        gap: 5px;
    }
    
    .input-group {
        flex-direction: column;
    }
}
</style>

<script>
// Отправка сообщения
document.getElementById('messageForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const messageInput = document.getElementById('messageInput');
    
    try {
        const response = await fetch('/api/ticket/message', {
            method: 'POST',
            body: formData
        });
        
        if (response.ok) {
            messageInput.value = '';
            // Перезагружаем страницу для отображения нового сообщения
            location.reload();
        } else {
            alert('Ошибка отправки сообщения');
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Ошибка отправки сообщения');
    }
});

// Изменение статуса тикета
async function changeTicketStatus(ticketId, status) {
    try {
        const response = await fetch(`/api/ticket/${ticketId}/status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ status: status })
        });
        
        if (response.ok) {
            location.reload();
        } else {
            alert('Ошибка изменения статуса');
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Ошибка изменения статуса');
    }
}

// Автопрокрутка к последнему сообщению
function scrollToBottom() {
    const chatMessages = document.getElementById('chatMessages');
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// Прокручиваем при загрузке
document.addEventListener('DOMContentLoaded', scrollToBottom);

// Автообновление сообщений каждые 30 секунд
setInterval(() => {
    // Можно добавить AJAX обновление без перезагрузки страницы
}, 30000);
</script>
{% endblock %}
