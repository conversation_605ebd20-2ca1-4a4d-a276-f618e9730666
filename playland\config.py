#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Конфигурация проекта PlayLand.
"""

import os
import logging
import logging.config
from pathlib import Path
from dotenv import load_dotenv

# Загрузка переменных окружения из файла .env
load_dotenv()

# Базовые пути
BASE_DIR = Path(__file__).resolve().parent.parent
LOGS_DIR = BASE_DIR / 'logs'
DATA_DIR = BASE_DIR / 'data'

# Создание директорий, если они не существуют
LOGS_DIR.mkdir(exist_ok=True)
DATA_DIR.mkdir(exist_ok=True)

# Настройки базы данных
DATABASE_URL = os.environ.get('DATABASE_URL', f'sqlite:///{BASE_DIR}/playland/database/playland.db')

# Настройки веб-сайта
WEBSITE_HOST = os.environ.get('WEBSITE_HOST', '0.0.0.0')
WEBSITE_PORT = int(os.environ.get('WEBSITE_PORT', 5000))
SECRET_KEY = os.environ.get('SECRET_KEY', 'dev_key')
DEBUG = os.environ.get('DEBUG', 'False').lower() == 'true'

# Настройки Telegram-бота
TELEGRAM_BOT_TOKEN = os.environ.get('TELEGRAM_BOT_TOKEN', '')
TELEGRAM_WEBHOOK_URL = os.environ.get('TELEGRAM_WEBHOOK_URL', '')
TELEGRAM_ADMIN_IDS = [int(id) for id in os.environ.get('TELEGRAM_ADMIN_IDS', '').split(',') if id]

# Настройки Discord-бота
DISCORD_BOT_TOKEN = os.environ.get('DISCORD_BOT_TOKEN', '')
DISCORD_GUILD_ID = os.environ.get('DISCORD_GUILD_ID', '')
DISCORD_LOG_CHANNEL_ID = os.environ.get('DISCORD_LOG_CHANNEL_ID', '')

# Настройки Minecraft-сервера
MINECRAFT_SERVER_HOST = os.environ.get('MINECRAFT_SERVER_HOST', 'localhost')
MINECRAFT_SERVER_PORT = int(os.environ.get('MINECRAFT_SERVER_PORT', 25565))
MINECRAFT_RCON_PASSWORD = os.environ.get('MINECRAFT_RCON_PASSWORD', '')

# Настройки API
API_SECRET_KEY = os.environ.get('API_SECRET_KEY', 'api_secret_key')
API_BASE_URL = os.environ.get('API_BASE_URL', 'http://localhost:5000/api')

# Настройки логирования
LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO').upper()
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# Настройка логирования для разных компонентов
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': LOG_FORMAT,
        },
    },
    'handlers': {
        'console': {
            'level': LOG_LEVEL,
            'class': 'logging.StreamHandler',
            'formatter': 'standard',
        },
        'website': {
            'level': LOG_LEVEL,
            'class': 'logging.FileHandler',
            'filename': LOGS_DIR / 'website.log',
            'formatter': 'standard',
        },
        'tg_bot': {
            'level': LOG_LEVEL,
            'class': 'logging.FileHandler',
            'filename': LOGS_DIR / 'tg_bot.log',
            'formatter': 'standard',
        },
        'discord_bot': {
            'level': LOG_LEVEL,
            'class': 'logging.FileHandler',
            'filename': LOGS_DIR / 'discord_bot.log',
            'formatter': 'standard',
        },
        'error': {
            'level': 'ERROR',
            'class': 'logging.FileHandler',
            'filename': LOGS_DIR / 'error.log',
            'formatter': 'standard',
        },
    },
    'loggers': {
        'playland': {
            'handlers': ['console', 'error'],
            'level': LOG_LEVEL,
            'propagate': True,
        },
        'playland.website': {
            'handlers': ['console', 'website', 'error'],
            'level': LOG_LEVEL,
            'propagate': False,
        },
        'playland.tg_bot': {
            'handlers': ['console', 'tg_bot', 'error'],
            'level': LOG_LEVEL,
            'propagate': False,
        },
        'playland.discord_bot': {
            'handlers': ['console', 'discord_bot', 'error'],
            'level': LOG_LEVEL,
            'propagate': False,
        },
    },
}

# Инициализация логирования
logging.config.dictConfig(LOGGING_CONFIG)

# Получение логгеров для разных компонентов
logger = logging.getLogger('playland')
website_logger = logging.getLogger('playland.website')
tg_bot_logger = logging.getLogger('playland.tg_bot')
discord_bot_logger = logging.getLogger('playland.discord_bot')
