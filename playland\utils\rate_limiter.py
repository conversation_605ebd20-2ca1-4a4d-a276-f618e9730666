#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Модуль для ограничения частоты запросов к API и веб-сервису.
Предоставляет функциональность для защиты от DoS-атак и злоупотребления API.
"""

import time
import logging
import threading
from functools import wraps
from flask import request, jsonify, current_app, g
from collections import defaultdict

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('rate_limiter')

class RateLimiter:
    """
    Класс для ограничения частоты запросов.
    Отслеживает количество запросов по IP-адресу и пути.
    """
    
    def __init__(self, window_size=60):
        """
        Инициализация лимитера.
        
        Args:
            window_size (int): Размер временного окна в секундах (по умолчанию 60 секунд)
        """
        self.window_size = window_size
        self.ip_limits = {}  # Лимиты по IP-адресу
        self.path_limits = {}  # Лимиты по пути запроса
        self.ip_requests = defaultdict(list)  # Запросы по IP
        self.path_requests = defaultdict(list)  # Запросы по пути
        self.lock = threading.RLock()  # Блокировка для потокобезопасности
    
    def is_rate_limited(self, ip, path, ip_limit=None, path_limit=None):
        """
        Проверяет, превышен ли лимит запросов.
        
        Args:
            ip (str): IP-адрес клиента
            path (str): Путь запроса
            ip_limit (int, optional): Лимит запросов с одного IP в минуту
            path_limit (int, optional): Лимит запросов на один путь в минуту
            
        Returns:
            bool: True, если лимит превышен, иначе False
        """
        with self.lock:
            current_time = time.time()
            
            # Обновляем лимиты, если они предоставлены
            if ip_limit is not None:
                self.ip_limits[ip] = ip_limit
            
            if path_limit is not None:
                self.path_limits[path] = path_limit
            
            # Получаем текущие лимиты
            current_ip_limit = self.ip_limits.get(ip)
            current_path_limit = self.path_limits.get(path)
            
            # Очищаем устаревшие запросы
            self._clean_old_requests(current_time)
            
            # Проверяем лимит по IP
            if current_ip_limit:
                ip_requests = self.ip_requests[ip]
                if len(ip_requests) >= current_ip_limit:
                    logger.warning(f"Превышен лимит запросов по IP {ip}: {len(ip_requests)}/{current_ip_limit}")
                    return True
            
            # Проверяем лимит по пути
            if current_path_limit:
                path_requests = self.path_requests[path]
                if len(path_requests) >= current_path_limit:
                    logger.warning(f"Превышен лимит запросов по пути {path}: {len(path_requests)}/{current_path_limit}")
                    return True
            
            # Добавляем текущий запрос
            self.ip_requests[ip].append(current_time)
            self.path_requests[path].append(current_time)
            
            return False
    
    def _clean_old_requests(self, current_time):
        """
        Очищает устаревшие запросы из истории.
        
        Args:
            current_time (float): Текущее время в секундах
        """
        cutoff_time = current_time - self.window_size
        
        # Очищаем запросы по IP
        for ip in list(self.ip_requests.keys()):
            self.ip_requests[ip] = [t for t in self.ip_requests[ip] if t > cutoff_time]
            if not self.ip_requests[ip]:
                del self.ip_requests[ip]
        
        # Очищаем запросы по пути
        for path in list(self.path_requests.keys()):
            self.path_requests[path] = [t for t in self.path_requests[path] if t > cutoff_time]
            if not self.path_requests[path]:
                del self.path_requests[path]

# Создаем глобальный экземпляр лимитера
limiter = RateLimiter()

def limit_requests(ip_limit=None, path_limit=None):
    """
    Декоратор для ограничения частоты запросов к API.
    
    Args:
        ip_limit (int, optional): Лимит запросов с одного IP в минуту
        path_limit (int, optional): Лимит запросов на один путь в минуту
        
    Returns:
        function: Декорированная функция
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Получаем IP-адрес клиента
            ip = request.remote_addr
            
            # Получаем путь запроса
            path = request.path
            
            # Проверяем, превышен ли лимит
            if limiter.is_rate_limited(ip, path, ip_limit, path_limit):
                # Записываем информацию о превышении лимита
                logger.warning(f"Превышен лимит запросов: IP={ip}, путь={path}")
                
                # Возвращаем ошибку 429 Too Many Requests
                response = jsonify({
                    'error': 'Слишком много запросов. Пожалуйста, повторите попытку позже.',
                    'status': 429
                })
                response.status_code = 429
                return response
            
            # Если лимит не превышен, выполняем исходную функцию
            return f(*args, **kwargs)
        
        return decorated_function
    
    return decorator

def init_rate_limiter(app):
    """
    Инициализирует лимитер для Flask-приложения.
    
    Args:
        app: Экземпляр Flask-приложения
    """
    # Устанавливаем глобальные настройки лимитера
    app.config.setdefault('RATE_LIMIT_WINDOW', 60)  # 60 секунд (1 минута)
    app.config.setdefault('RATE_LIMIT_DEFAULT_IP', 100)  # 100 запросов в минуту с одного IP
    app.config.setdefault('RATE_LIMIT_DEFAULT_PATH', 200)  # 200 запросов в минуту на один путь
    
    # Настраиваем глобальный лимитер
    global limiter
    limiter.window_size = app.config['RATE_LIMIT_WINDOW']
    
    # Регистрируем обработчик before_request для глобального ограничения
    @app.before_request
    def check_rate_limit():
        # Пропускаем статические файлы
        if request.path.startswith('/static/'):
            return None
        
        # Получаем IP-адрес клиента
        ip = request.remote_addr
        
        # Получаем путь запроса
        path = request.path
        
        # Проверяем, превышен ли глобальный лимит
        if limiter.is_rate_limited(
            ip, 
            path, 
            app.config['RATE_LIMIT_DEFAULT_IP'], 
            app.config['RATE_LIMIT_DEFAULT_PATH']
        ):
            # Записываем информацию о превышении лимита
            logger.warning(f"Превышен глобальный лимит запросов: IP={ip}, путь={path}")
            
            # Возвращаем ошибку 429 Too Many Requests
            response = jsonify({
                'error': 'Слишком много запросов. Пожалуйста, повторите попытку позже.',
                'status': 429
            })
            response.status_code = 429
            return response
        
        # Сохраняем время начала запроса для логирования
        g.start_time = time.time()
    
    # Регистрируем обработчик after_request для логирования
    @app.after_request
    def log_request(response):
        # Пропускаем статические файлы
        if request.path.startswith('/static/'):
            return response
        
        # Вычисляем время выполнения запроса
        if hasattr(g, 'start_time'):
            duration = time.time() - g.start_time
            
            # Логируем информацию о запросе
            logger.info(
                f"Запрос: {request.method} {request.path} - "
                f"Статус: {response.status_code} - "
                f"Время: {duration:.4f}s - "
                f"IP: {request.remote_addr}"
            )
        
        return response
    
    logger.info("Инициализирован модуль ограничения частоты запросов")
