#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Скрипт объединения баз данных веб-приложения и Telegram бота в единую систему.
"""

import sqlite3
import os
import json
import shutil
from datetime import datetime
import logging

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('database_unification.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DatabaseUnifier:
    """Класс для объединения баз данных"""
    
    def __init__(self):
        self.web_db_path = "website/instance/playland.db"
        self.telegram_db_path = None
        self.unified_db_path = "website/instance/playland_unified.db"
        self.backup_dir = "database/backups"
        
        # Поиск базы данных Telegram бота
        possible_paths = [
            "tg_bot/bot.db",
            "tg_bot/telegram.db", 
            "tg_bot/database.db",
            "telegram_bot.db",
            "bot.db",
            "instance/playland_unified.db"
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                self.telegram_db_path = path
                logger.info(f"Найдена база данных Telegram: {path}")
                break
    
    def create_backup(self):
        """Создает резервные копии баз данных"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Создаем директорию для бэкапов
        os.makedirs(self.backup_dir, exist_ok=True)
        
        # Бэкап веб-БД
        if os.path.exists(self.web_db_path):
            backup_web = f"{self.backup_dir}/web_db_backup_{timestamp}.db"
            shutil.copy2(self.web_db_path, backup_web)
            logger.info(f"Создан бэкап веб-БД: {backup_web}")
        
        # Бэкап Telegram БД
        if self.telegram_db_path and os.path.exists(self.telegram_db_path):
            backup_telegram = f"{self.backup_dir}/telegram_db_backup_{timestamp}.db"
            shutil.copy2(self.telegram_db_path, backup_telegram)
            logger.info(f"Создан бэкап Telegram БД: {backup_telegram}")
        
        return timestamp
    
    def create_unified_schema(self):
        """Создает единую схему базы данных"""
        try:
            # Создаем директорию если её нет
            os.makedirs(os.path.dirname(self.unified_db_path), exist_ok=True)
            
            conn = sqlite3.connect(self.unified_db_path)
            cursor = conn.cursor()
            
            # Создаем объединенную таблицу пользователей
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                -- Основные поля
                username TEXT UNIQUE,
                email TEXT UNIQUE,
                password_hash TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
                is_admin BOOLEAN DEFAULT FALSE,
                is_activated BOOLEAN DEFAULT FALSE,
                activation_token TEXT,
                
                -- Профиль
                nickname TEXT,
                first_name TEXT,
                last_name TEXT,
                about TEXT,
                bio TEXT,
                minecraft_experience TEXT,
                interests TEXT,
                last_seen_on_server DATETIME,
                achievements TEXT,
                social_links TEXT,
                avatar_url TEXT,
                
                -- Социальные сети и интеграции
                discord TEXT,
                discord_id TEXT UNIQUE,
                discord_tag TEXT,
                telegram TEXT,
                telegram_id INTEGER UNIQUE,
                
                -- Игровые данные
                status TEXT DEFAULT 'pending',
                balance REAL DEFAULT 0.0,
                is_whitelisted BOOLEAN DEFAULT FALSE,
                whitelist_status TEXT DEFAULT 'pending',
                premium_until DATETIME,
                
                -- Безопасность
                failed_login_attempts INTEGER DEFAULT 0,
                last_failed_login DATETIME,
                account_locked_until DATETIME,
                password_reset_token TEXT,
                password_reset_expires DATETIME,
                email_verification_token TEXT,
                email_verified BOOLEAN DEFAULT FALSE,
                
                -- 2FA
                two_factor_enabled BOOLEAN DEFAULT FALSE,
                two_factor_secret TEXT,
                backup_codes TEXT,
                
                -- Сессии
                session_token TEXT,
                session_expires DATETIME,
                
                -- Дополнительные поля для совместимости с Telegram ботом
                registration_date TEXT,
                last_activity TEXT,
                is_banned BOOLEAN DEFAULT FALSE,
                ban_reason TEXT
            )
            ''')
            
            # Создаем объединенную таблицу заявок
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS applications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                telegram_user_id INTEGER,
                minecraft_nickname TEXT NOT NULL,
                discord_tag TEXT,
                reason TEXT,
                status TEXT DEFAULT 'pending',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                timestamp TEXT,
                processed_by INTEGER,
                processed_at DATETIME,
                rejection_reason TEXT,
                source TEXT DEFAULT 'web',
                reviewed_by INTEGER,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (telegram_user_id) REFERENCES users (telegram_id),
                FOREIGN KEY (processed_by) REFERENCES users (id),
                FOREIGN KEY (reviewed_by) REFERENCES users (id)
            )
            ''')
            
            # Создаем таблицы безопасности
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS trusted_devices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                device_fingerprint TEXT NOT NULL,
                device_name TEXT,
                ip_address TEXT,
                user_agent TEXT,
                location TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_used DATETIME DEFAULT CURRENT_TIMESTAMP,
                expires_at DATETIME,
                is_active BOOLEAN DEFAULT TRUE,
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
            )
            ''')
            
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS login_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                ip_address TEXT,
                user_agent TEXT,
                location TEXT,
                device_fingerprint TEXT,
                login_method TEXT DEFAULT 'password',
                success BOOLEAN DEFAULT TRUE,
                failure_reason TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                session_duration INTEGER,
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
            )
            ''')
            
            # Создаем таблицу аудита
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS audit_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                action TEXT NOT NULL,
                description TEXT,
                ip_address TEXT,
                user_agent TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                severity TEXT DEFAULT 'INFO',
                details TEXT,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
            ''')
            
            # Создаем дополнительные таблицы
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                amount REAL NOT NULL,
                description TEXT,
                transaction_type TEXT,
                status TEXT DEFAULT 'pending',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
            ''')
            
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS whitelist_requests (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                minecraft_nickname TEXT NOT NULL,
                reason TEXT,
                status TEXT DEFAULT 'pending',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                processed_by INTEGER,
                processed_at DATETIME,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (processed_by) REFERENCES users (id)
            )
            ''')
            
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                total_applications INTEGER DEFAULT 0,
                approved_applications INTEGER DEFAULT 0,
                rejected_applications INTEGER DEFAULT 0,
                pending_applications INTEGER DEFAULT 0,
                last_updated TEXT
            )
            ''')
            
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_states (
                user_id INTEGER PRIMARY KEY,
                state TEXT,
                data TEXT,
                timestamp TEXT
            )
            ''')
            
            # Создаем индексы
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)",
                "CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)",
                "CREATE INDEX IF NOT EXISTS idx_users_telegram_id ON users(telegram_id)",
                "CREATE INDEX IF NOT EXISTS idx_users_discord_id ON users(discord_id)",
                "CREATE INDEX IF NOT EXISTS idx_applications_user_id ON applications(user_id)",
                "CREATE INDEX IF NOT EXISTS idx_applications_telegram_user_id ON applications(telegram_user_id)",
                "CREATE INDEX IF NOT EXISTS idx_applications_status ON applications(status)",
                "CREATE INDEX IF NOT EXISTS idx_trusted_devices_user_id ON trusted_devices(user_id)",
                "CREATE INDEX IF NOT EXISTS idx_trusted_devices_fingerprint ON trusted_devices(device_fingerprint)",
                "CREATE INDEX IF NOT EXISTS idx_login_history_user_id ON login_history(user_id)",
                "CREATE INDEX IF NOT EXISTS idx_login_history_timestamp ON login_history(timestamp)",
                "CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id)",
                "CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs(timestamp)"
            ]
            
            for index_sql in indexes:
                cursor.execute(index_sql)
            
            conn.commit()
            logger.info("Создана единая схема базы данных")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка при создании единой схемы: {e}")
            return False
        finally:
            if conn:
                conn.close()
    
    def migrate_web_data(self):
        """Мигрирует данные из веб-БД в объединенную БД"""
        if not os.path.exists(self.web_db_path):
            logger.warning("Веб-база данных не найдена, пропускаем миграцию")
            return True
        
        try:
            # Подключаемся к обеим БД
            web_conn = sqlite3.connect(self.web_db_path)
            web_conn.row_factory = sqlite3.Row
            unified_conn = sqlite3.connect(self.unified_db_path)
            
            web_cursor = web_conn.cursor()
            unified_cursor = unified_conn.cursor()
            
            # Мигрируем пользователей
            web_cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
            if web_cursor.fetchone():
                web_cursor.execute("SELECT * FROM users")
                users = web_cursor.fetchall()
                
                for user in users:
                    user_dict = dict(user)
                    
                    # Подготавливаем данные для вставки
                    columns = list(user_dict.keys())
                    values = list(user_dict.values())
                    placeholders = ', '.join(['?' for _ in values])
                    columns_str = ', '.join(columns)
                    
                    try:
                        unified_cursor.execute(
                            f"INSERT OR REPLACE INTO users ({columns_str}) VALUES ({placeholders})",
                            values
                        )
                    except sqlite3.Error as e:
                        logger.warning(f"Ошибка при миграции пользователя {user_dict.get('id', 'unknown')}: {e}")
                
                logger.info(f"Мигрировано {len(users)} пользователей из веб-БД")
            
            # Мигрируем другие таблицы аналогично
            tables_to_migrate = ['applications', 'transactions', 'whitelist_requests', 'trusted_devices', 'login_history', 'audit_logs']
            
            for table in tables_to_migrate:
                web_cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
                if web_cursor.fetchone():
                    web_cursor.execute(f"SELECT * FROM {table}")
                    rows = web_cursor.fetchall()
                    
                    for row in rows:
                        row_dict = dict(row)
                        columns = list(row_dict.keys())
                        values = list(row_dict.values())
                        placeholders = ', '.join(['?' for _ in values])
                        columns_str = ', '.join(columns)
                        
                        try:
                            unified_cursor.execute(
                                f"INSERT OR REPLACE INTO {table} ({columns_str}) VALUES ({placeholders})",
                                values
                            )
                        except sqlite3.Error as e:
                            logger.warning(f"Ошибка при миграции записи из таблицы {table}: {e}")
                    
                    logger.info(f"Мигрировано {len(rows)} записей из таблицы {table}")
            
            unified_conn.commit()
            logger.info("Миграция данных из веб-БД завершена")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка при миграции данных из веб-БД: {e}")
            return False
        finally:
            if web_conn:
                web_conn.close()
            if unified_conn:
                unified_conn.close()
    
    def migrate_telegram_data(self):
        """Мигрирует данные из Telegram БД в объединенную БД"""
        if not self.telegram_db_path or not os.path.exists(self.telegram_db_path):
            logger.warning("Telegram база данных не найдена, пропускаем миграцию")
            return True
        
        try:
            # Подключаемся к обеим БД
            telegram_conn = sqlite3.connect(self.telegram_db_path)
            telegram_conn.row_factory = sqlite3.Row
            unified_conn = sqlite3.connect(self.unified_db_path)
            
            telegram_cursor = telegram_conn.cursor()
            unified_cursor = unified_conn.cursor()
            
            # Мигрируем пользователей Telegram
            telegram_cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
            if telegram_cursor.fetchone():
                telegram_cursor.execute("SELECT * FROM users")
                telegram_users = telegram_cursor.fetchall()
                
                for user in telegram_users:
                    user_dict = dict(user)
                    telegram_id = user_dict.get('telegram_id')
                    
                    if telegram_id:
                        # Проверяем, есть ли уже пользователь с таким telegram_id
                        unified_cursor.execute("SELECT id FROM users WHERE telegram_id = ?", (telegram_id,))
                        existing_user = unified_cursor.fetchone()
                        
                        if existing_user:
                            # Обновляем существующего пользователя
                            update_fields = []
                            update_values = []
                            
                            for key, value in user_dict.items():
                                if key != 'id' and value is not None:
                                    update_fields.append(f"{key} = ?")
                                    update_values.append(value)
                            
                            if update_fields:
                                update_values.append(telegram_id)
                                unified_cursor.execute(
                                    f"UPDATE users SET {', '.join(update_fields)} WHERE telegram_id = ?",
                                    update_values
                                )
                        else:
                            # Создаем нового пользователя
                            columns = list(user_dict.keys())
                            values = list(user_dict.values())
                            placeholders = ', '.join(['?' for _ in values])
                            columns_str = ', '.join(columns)
                            
                            try:
                                unified_cursor.execute(
                                    f"INSERT INTO users ({columns_str}) VALUES ({placeholders})",
                                    values
                                )
                            except sqlite3.Error as e:
                                logger.warning(f"Ошибка при создании пользователя Telegram {telegram_id}: {e}")
                
                logger.info(f"Обработано {len(telegram_users)} пользователей из Telegram БД")
            
            # Мигрируем заявки из Telegram
            telegram_cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='applications'")
            if telegram_cursor.fetchone():
                telegram_cursor.execute("SELECT * FROM applications")
                applications = telegram_cursor.fetchall()
                
                for app in applications:
                    app_dict = dict(app)
                    app_dict['source'] = 'telegram'  # Помечаем источник
                    
                    columns = list(app_dict.keys())
                    values = list(app_dict.values())
                    placeholders = ', '.join(['?' for _ in values])
                    columns_str = ', '.join(columns)
                    
                    try:
                        unified_cursor.execute(
                            f"INSERT OR REPLACE INTO applications ({columns_str}) VALUES ({placeholders})",
                            values
                        )
                    except sqlite3.Error as e:
                        logger.warning(f"Ошибка при миграции заявки {app_dict.get('id', 'unknown')}: {e}")
                
                logger.info(f"Мигрировано {len(applications)} заявок из Telegram БД")
            
            # Мигрируем другие таблицы
            other_tables = ['stats', 'user_states']
            for table in other_tables:
                telegram_cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
                if telegram_cursor.fetchone():
                    telegram_cursor.execute(f"SELECT * FROM {table}")
                    rows = telegram_cursor.fetchall()
                    
                    for row in rows:
                        row_dict = dict(row)
                        columns = list(row_dict.keys())
                        values = list(row_dict.values())
                        placeholders = ', '.join(['?' for _ in values])
                        columns_str = ', '.join(columns)
                        
                        try:
                            unified_cursor.execute(
                                f"INSERT OR REPLACE INTO {table} ({columns_str}) VALUES ({placeholders})",
                                values
                            )
                        except sqlite3.Error as e:
                            logger.warning(f"Ошибка при миграции записи из таблицы {table}: {e}")
                    
                    logger.info(f"Мигрировано {len(rows)} записей из таблицы {table}")
            
            unified_conn.commit()
            logger.info("Миграция данных из Telegram БД завершена")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка при миграции данных из Telegram БД: {e}")
            return False
        finally:
            if telegram_conn:
                telegram_conn.close()
            if unified_conn:
                unified_conn.close()
    
    def verify_migration(self):
        """Проверяет результаты миграции"""
        try:
            conn = sqlite3.connect(self.unified_db_path)
            cursor = conn.cursor()
            
            # Проверяем количество записей в основных таблицах
            tables_to_check = ['users', 'applications', 'trusted_devices', 'login_history']
            
            logger.info("=== Результаты миграции ===")
            for table in tables_to_check:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                logger.info(f"Таблица {table}: {count} записей")
            
            # Проверяем связи Telegram
            cursor.execute("SELECT COUNT(*) FROM users WHERE telegram_id IS NOT NULL")
            telegram_users = cursor.fetchone()[0]
            logger.info(f"Пользователей с Telegram ID: {telegram_users}")
            
            # Проверяем заявки из разных источников
            cursor.execute("SELECT source, COUNT(*) FROM applications GROUP BY source")
            sources = cursor.fetchall()
            for source, count in sources:
                logger.info(f"Заявки из {source}: {count}")
            
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"Ошибка при проверке миграции: {e}")
            return False
    
    def run_unification(self):
        """Запускает полный процесс объединения"""
        logger.info("=== Начало объединения баз данных ===")
        
        # 1. Создаем бэкапы
        logger.info("1. Создание резервных копий...")
        backup_timestamp = self.create_backup()
        
        # 2. Создаем единую схему
        logger.info("2. Создание единой схемы...")
        if not self.create_unified_schema():
            logger.error("Не удалось создать единую схему")
            return False
        
        # 3. Мигрируем данные из веб-БД
        logger.info("3. Миграция данных из веб-приложения...")
        if not self.migrate_web_data():
            logger.error("Не удалось мигрировать данные из веб-БД")
            return False
        
        # 4. Мигрируем данные из Telegram БД
        logger.info("4. Миграция данных из Telegram бота...")
        if not self.migrate_telegram_data():
            logger.error("Не удалось мигрировать данные из Telegram БД")
            return False
        
        # 5. Проверяем результаты
        logger.info("5. Проверка результатов миграции...")
        if not self.verify_migration():
            logger.error("Проверка миграции не пройдена")
            return False
        
        logger.info("=== Объединение баз данных завершено успешно ===")
        logger.info(f"Объединенная БД: {self.unified_db_path}")
        logger.info(f"Бэкапы созданы с меткой времени: {backup_timestamp}")
        
        return True

def main():
    """Главная функция"""
    unifier = DatabaseUnifier()
    success = unifier.run_unification()
    
    if success:
        print("✅ Объединение баз данных завершено успешно!")
        print(f"📁 Объединенная БД: {unifier.unified_db_path}")
        print("📋 Проверьте лог файл database_unification.log для деталей")
    else:
        print("❌ Ошибка при объединении баз данных!")
        print("📋 Проверьте лог файл database_unification.log для деталей")
    
    return success

if __name__ == "__main__":
    main()
