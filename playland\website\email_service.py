#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Сервис для отправки email уведомлений PlayLand.
"""

import os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
import logging
from jinja2 import Environment, FileSystemLoader, select_autoescape
from flask import current_app, url_for, render_template_string

# Настройка логирования
logger = logging.getLogger(__name__)

class EmailService:
    """Сервис для отправки email уведомлений"""
    
    def __init__(self, app=None):
        self.app = app
        self.smtp_server = None
        self.smtp_port = None
        self.smtp_username = None
        self.smtp_password = None
        self.smtp_use_tls = True
        self.sender_email = None
        self.sender_name = "PlayLand Server"
        
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """Инициализация с Flask приложением"""
        self.app = app
        
        # Получаем настройки из конфигурации
        self.smtp_server = app.config.get('MAIL_SERVER', os.getenv('MAIL_SERVER'))
        self.smtp_port = app.config.get('MAIL_PORT', int(os.getenv('MAIL_PORT', 587)))
        self.smtp_username = app.config.get('MAIL_USERNAME', os.getenv('MAIL_USERNAME'))
        self.smtp_password = app.config.get('MAIL_PASSWORD', os.getenv('MAIL_PASSWORD'))
        self.smtp_use_tls = app.config.get('MAIL_USE_TLS', os.getenv('MAIL_USE_TLS', 'True').lower() == 'true')
        self.sender_email = self.smtp_username or app.config.get('MAIL_DEFAULT_SENDER', os.getenv('MAIL_DEFAULT_SENDER'))
        self.sender_name = app.config.get('MAIL_SENDER_NAME', os.getenv('MAIL_SENDER_NAME', 'PlayLand Server'))
        
        # Проверяем наличие обязательных настроек
        if not all([self.smtp_server, self.smtp_username, self.smtp_password, self.sender_email]):
            logger.warning("Email service not fully configured. Some email features may not work.")
    
    def is_configured(self) -> bool:
        """Проверяет, настроен ли email сервис"""
        return all([self.smtp_server, self.smtp_username, self.smtp_password, self.sender_email])
    
    def send_email(self, 
                   to_email: str, 
                   subject: str, 
                   html_body: str, 
                   text_body: Optional[str] = None,
                   attachments: Optional[List[Dict[str, Any]]] = None) -> bool:
        """
        Отправляет email
        
        Args:
            to_email: Email получателя
            subject: Тема письма
            html_body: HTML содержимое
            text_body: Текстовое содержимое (опционально)
            attachments: Список вложений (опционально)
            
        Returns:
            bool: True если отправка успешна, False иначе
        """
        if not self.is_configured():
            logger.error("Email service is not configured")
            return False
        
        try:
            # Создаем сообщение
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = f"{self.sender_name} <{self.sender_email}>"
            msg['To'] = to_email
            
            # Добавляем текстовую версию
            if text_body:
                text_part = MIMEText(text_body, 'plain', 'utf-8')
                msg.attach(text_part)
            
            # Добавляем HTML версию
            html_part = MIMEText(html_body, 'html', 'utf-8')
            msg.attach(html_part)
            
            # Добавляем вложения
            if attachments:
                for attachment in attachments:
                    self._add_attachment(msg, attachment)
            
            # Отправляем email
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                if self.smtp_use_tls:
                    server.starttls()
                server.login(self.smtp_username, self.smtp_password)
                server.send_message(msg)
            
            logger.info(f"Email sent successfully to {to_email}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email to {to_email}: {str(e)}")
            return False
    
    def _add_attachment(self, msg: MIMEMultipart, attachment: Dict[str, Any]):
        """Добавляет вложение к сообщению"""
        try:
            with open(attachment['path'], 'rb') as f:
                part = MIMEBase('application', 'octet-stream')
                part.set_payload(f.read())
                encoders.encode_base64(part)
                part.add_header(
                    'Content-Disposition',
                    f'attachment; filename= {attachment["filename"]}'
                )
                msg.attach(part)
        except Exception as e:
            logger.error(f"Failed to add attachment {attachment.get('filename', 'unknown')}: {str(e)}")
    
    def send_verification_email(self, user, verification_token: str) -> bool:
        """Отправляет email для подтверждения регистрации"""
        if not user.email:
            logger.error("User has no email address")
            return False
        
        verification_url = url_for('auth.verify_email', token=verification_token, _external=True)
        
        subject = "Подтверждение регистрации на PlayLand"
        
        html_body = self._render_email_template('verification_email.html', {
            'user': user,
            'verification_url': verification_url,
            'server_name': 'PlayLand',
            'expires_hours': 24
        })
        
        text_body = f"""
Добро пожаловать на PlayLand, {user.username}!

Для завершения регистрации перейдите по ссылке:
{verification_url}

Ссылка действительна в течение 24 часов.

Если вы не регистрировались на нашем сервере, просто проигнорируйте это письмо.

С уважением,
Команда PlayLand
        """.strip()
        
        return self.send_email(user.email, subject, html_body, text_body)
    
    def send_password_reset_email(self, user, reset_token: str) -> bool:
        """Отправляет email для сброса пароля"""
        if not user.email:
            logger.error("User has no email address")
            return False
        
        reset_url = url_for('auth.reset_password', token=reset_token, _external=True)
        
        subject = "Сброс пароля на PlayLand"
        
        html_body = self._render_email_template('password_reset_email.html', {
            'user': user,
            'reset_url': reset_url,
            'server_name': 'PlayLand',
            'expires_hours': 1
        })
        
        text_body = f"""
Здравствуйте, {user.username}!

Вы запросили сброс пароля для вашего аккаунта на PlayLand.

Для установки нового пароля перейдите по ссылке:
{reset_url}

Ссылка действительна в течение 1 часа.

Если вы не запрашивали сброс пароля, просто проигнорируйте это письмо.

С уважением,
Команда PlayLand
        """.strip()
        
        return self.send_email(user.email, subject, html_body, text_body)
    
    def send_security_alert_email(self, user, alert_type: str, details: Dict[str, Any]) -> bool:
        """Отправляет уведомление о событии безопасности"""
        if not user.email:
            return False
        
        alert_messages = {
            'login_from_new_device': 'Вход с нового устройства',
            'password_changed': 'Пароль был изменен',
            'two_factor_enabled': 'Двухфакторная аутентификация включена',
            'two_factor_disabled': 'Двухфакторная аутентификация отключена',
            'account_locked': 'Аккаунт заблокирован',
            'suspicious_activity': 'Подозрительная активность'
        }
        
        alert_title = alert_messages.get(alert_type, 'Событие безопасности')
        subject = f"PlayLand: {alert_title}"
        
        html_body = self._render_email_template('security_alert_email.html', {
            'user': user,
            'alert_type': alert_type,
            'alert_title': alert_title,
            'details': details,
            'server_name': 'PlayLand',
            'timestamp': datetime.now()
        })
        
        text_body = f"""
Здравствуйте, {user.username}!

Обнаружено событие безопасности в вашем аккаунте: {alert_title}

Детали:
{self._format_details_text(details)}

Если это были не вы, немедленно смените пароль и обратитесь к администрации.

С уважением,
Команда PlayLand
        """.strip()
        
        return self.send_email(user.email, subject, html_body, text_body)
    
    def send_account_deletion_email(self, user, deletion_date: datetime) -> bool:
        """Отправляет уведомление об удалении аккаунта"""
        if not user.email:
            return False
        
        cancel_url = url_for('profile.cancel_deletion', _external=True)
        
        subject = "PlayLand: Запрос на удаление аккаунта"
        
        html_body = self._render_email_template('account_deletion_email.html', {
            'user': user,
            'deletion_date': deletion_date,
            'cancel_url': cancel_url,
            'server_name': 'PlayLand'
        })
        
        text_body = f"""
Здравствуйте, {user.username}!

Вы запросили удаление вашего аккаунта на PlayLand.

Аккаунт будет удален: {deletion_date.strftime('%d.%m.%Y в %H:%M')}

Для отмены удаления перейдите по ссылке:
{cancel_url}

Если вы не запрашивали удаление аккаунта, немедленно отмените операцию и смените пароль.

С уважением,
Команда PlayLand
        """.strip()
        
        return self.send_email(user.email, subject, html_body, text_body)
    
    def _render_email_template(self, template_name: str, context: Dict[str, Any]) -> str:
        """Рендерит email шаблон"""
        try:
            # Пытаемся использовать Flask шаблоны
            if self.app:
                with self.app.app_context():
                    return render_template_string(self._get_email_template(template_name), **context)
            else:
                # Fallback к простому шаблону
                return self._get_simple_template(template_name, context)
        except Exception as e:
            logger.error(f"Failed to render email template {template_name}: {str(e)}")
            return self._get_simple_template('fallback.html', context)
    
    def _get_email_template(self, template_name: str) -> str:
        """Возвращает HTML шаблон для email"""
        templates = {
            'verification_email.html': """
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Подтверждение регистрации</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #007bff; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f8f9fa; }
        .button { display: inline-block; padding: 12px 24px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; }
        .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ server_name }}</h1>
            <h2>Подтверждение регистрации</h2>
        </div>
        <div class="content">
            <p>Добро пожаловать, <strong>{{ user.username }}</strong>!</p>
            <p>Спасибо за регистрацию на нашем сервере. Для завершения регистрации необходимо подтвердить ваш email адрес.</p>
            <p style="text-align: center; margin: 30px 0;">
                <a href="{{ verification_url }}" class="button">Подтвердить Email</a>
            </p>
            <p>Или скопируйте и вставьте эту ссылку в браузер:</p>
            <p style="word-break: break-all; background: #e9ecef; padding: 10px; border-radius: 3px;">{{ verification_url }}</p>
            <p><strong>Важно:</strong> Ссылка действительна в течение {{ expires_hours }} часов.</p>
        </div>
        <div class="footer">
            <p>Если вы не регистрировались на нашем сервере, просто проигнорируйте это письмо.</p>
            <p>&copy; {{ server_name }} - Minecraft сервер</p>
        </div>
    </div>
</body>
</html>
            """,
            
            'password_reset_email.html': """
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Сброс пароля</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #dc3545; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f8f9fa; }
        .button { display: inline-block; padding: 12px 24px; background: #dc3545; color: white; text-decoration: none; border-radius: 5px; }
        .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ server_name }}</h1>
            <h2>Сброс пароля</h2>
        </div>
        <div class="content">
            <p>Здравствуйте, <strong>{{ user.username }}</strong>!</p>
            <p>Вы запросили сброс пароля для вашего аккаунта.</p>
            <p style="text-align: center; margin: 30px 0;">
                <a href="{{ reset_url }}" class="button">Сбросить пароль</a>
            </p>
            <p>Или скопируйте и вставьте эту ссылку в браузер:</p>
            <p style="word-break: break-all; background: #e9ecef; padding: 10px; border-radius: 3px;">{{ reset_url }}</p>
            <p><strong>Важно:</strong> Ссылка действительна в течение {{ expires_hours }} часа.</p>
        </div>
        <div class="footer">
            <p>Если вы не запрашивали сброс пароля, просто проигнорируйте это письмо.</p>
            <p>&copy; {{ server_name }} - Minecraft сервер</p>
        </div>
    </div>
</body>
</html>
            """
        }
        
        return templates.get(template_name, templates.get('fallback.html', '<p>{{ message }}</p>'))
    
    def _get_simple_template(self, template_name: str, context: Dict[str, Any]) -> str:
        """Простой fallback шаблон"""
        return f"""
        <html>
        <body>
            <h2>PlayLand</h2>
            <p>Уведомление для пользователя: {context.get('user', {}).get('username', 'Unknown')}</p>
            <p>{context.get('message', 'Системное уведомление')}</p>
        </body>
        </html>
        """
    
    def _format_details_text(self, details: Dict[str, Any]) -> str:
        """Форматирует детали для текстового email"""
        formatted = []
        for key, value in details.items():
            formatted.append(f"- {key}: {value}")
        return '\n'.join(formatted)

# Глобальный экземпляр сервиса
email_service = EmailService()
