{% extends "base.html" %}

{% block title %}Новый пароль - PlayLand{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">Установка нового пароля</h4>
                </div>
                <div class="card-body">
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    <div class="text-center mb-4">
                        <i class="fas fa-lock fa-3x text-success mb-3"></i>
                        <p class="text-muted">Введите новый пароль для вашего аккаунта</p>
                    </div>

                    <form method="POST" action="{{ url_for('auth.reset_password', token=request.view_args.token) }}">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-3">
                            {{ form.password.label(class="form-label") }}
                            {{ form.password(class="form-control", id="password") }}
                            {% if form.password.errors %}
                                <div class="text-danger">
                                    {% for error in form.password.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="password-strength mt-2">
                                <div class="progress" style="height: 5px;">
                                    <div class="progress-bar" id="strengthBar" role="progressbar" style="width: 0%"></div>
                                </div>
                                <small id="strengthText" class="text-muted">Введите пароль</small>
                            </div>
                        </div>

                        <div class="mb-3">
                            {{ form.confirm_password.label(class="form-label") }}
                            {{ form.confirm_password(class="form-control", id="confirmPassword") }}
                            {% if form.confirm_password.errors %}
                                <div class="text-danger">
                                    {% for error in form.confirm_password.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div id="passwordMatch" class="mt-1"></div>
                        </div>

                        <div class="d-grid">
                            {{ form.submit(class="btn btn-success") }}
                        </div>
                    </form>

                    <hr>

                    <div class="text-center">
                        <a href="{{ url_for('auth.enhanced_login') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Вернуться к входу
                        </a>
                    </div>

                    <div class="mt-4">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> Требования к паролю:</h6>
                            <ul class="mb-0">
                                <li>Минимум 8 символов</li>
                                <li>Содержит заглавные и строчные буквы</li>
                                <li>Содержит цифры</li>
                                <li>Содержит специальные символы</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirmPassword');
    const strengthBar = document.getElementById('strengthBar');
    const strengthText = document.getElementById('strengthText');
    const passwordMatch = document.getElementById('passwordMatch');

    // Проверка силы пароля
    passwordInput.addEventListener('input', function() {
        const password = this.value;
        const strength = calculatePasswordStrength(password);
        
        updateStrengthIndicator(strength);
        checkPasswordMatch();
    });

    // Проверка совпадения паролей
    confirmPasswordInput.addEventListener('input', checkPasswordMatch);

    function calculatePasswordStrength(password) {
        let score = 0;
        let feedback = [];

        if (password.length >= 8) {
            score += 25;
        } else {
            feedback.push('минимум 8 символов');
        }

        if (/[a-z]/.test(password) && /[A-Z]/.test(password)) {
            score += 25;
        } else {
            feedback.push('заглавные и строчные буквы');
        }

        if (/\d/.test(password)) {
            score += 25;
        } else {
            feedback.push('цифры');
        }

        if (/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password)) {
            score += 25;
        } else {
            feedback.push('специальные символы');
        }

        return { score, feedback };
    }

    function updateStrengthIndicator(strength) {
        const { score, feedback } = strength;
        
        strengthBar.style.width = score + '%';
        
        if (score < 25) {
            strengthBar.className = 'progress-bar bg-danger';
            strengthText.textContent = 'Слабый пароль';
            strengthText.className = 'text-danger';
        } else if (score < 50) {
            strengthBar.className = 'progress-bar bg-warning';
            strengthText.textContent = 'Средний пароль';
            strengthText.className = 'text-warning';
        } else if (score < 75) {
            strengthBar.className = 'progress-bar bg-info';
            strengthText.textContent = 'Хороший пароль';
            strengthText.className = 'text-info';
        } else {
            strengthBar.className = 'progress-bar bg-success';
            strengthText.textContent = 'Отличный пароль';
            strengthText.className = 'text-success';
        }

        if (feedback.length > 0) {
            strengthText.textContent += ' (нужно: ' + feedback.join(', ') + ')';
        }
    }

    function checkPasswordMatch() {
        const password = passwordInput.value;
        const confirmPassword = confirmPasswordInput.value;

        if (confirmPassword === '') {
            passwordMatch.innerHTML = '';
            return;
        }

        if (password === confirmPassword) {
            passwordMatch.innerHTML = '<small class="text-success"><i class="fas fa-check"></i> Пароли совпадают</small>';
        } else {
            passwordMatch.innerHTML = '<small class="text-danger"><i class="fas fa-times"></i> Пароли не совпадают</small>';
        }
    }
});
</script>

<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.btn-success {
    background-color: #28a745;
    border-color: #28a745;
}

.btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

.btn-outline-secondary {
    color: #6c757d;
    border-color: #6c757d;
}

.btn-outline-secondary:hover {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

.alert {
    border-radius: 0.375rem;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.text-success {
    color: #28a745 !important;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

.progress {
    background-color: #e9ecef;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    color: #0056b3;
    text-decoration: underline;
}
</style>
{% endblock %}
