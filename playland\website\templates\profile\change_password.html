{% extends "base.html" %}

{% block title %}Изменение пароля - PlayLand{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-3">
            <!-- Боковое меню профиля -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Профиль</h6>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('profile.profile') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-user"></i> Основная информация
                    </a>
                    <a href="{{ url_for('profile.security_settings') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-shield-alt"></i> Безопасность
                    </a>
                    <a href="{{ url_for('profile.change_password') }}" class="list-group-item list-group-item-action active">
                        <i class="fas fa-key"></i> Изменение пароля
                    </a>
                    <a href="{{ url_for('profile.view_tickets') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-ticket-alt"></i> Тикеты
                    </a>
                </div>
            </div>
        </div>

        <div class="col-md-9">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-key"></i> Изменение пароля</h5>
                </div>
                <div class="card-body">
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    <div class="row">
                        <div class="col-md-8">
                            <form method="POST" action="{{ url_for('profile.change_password') }}" id="changePasswordForm">
                                {{ form.hidden_tag() }}
                                
                                <div class="mb-3">
                                    {{ form.current_password.label(class="form-label") }}
                                    {{ form.current_password(class="form-control", id="currentPassword") }}
                                    {% if form.current_password.errors %}
                                        <div class="text-danger">
                                            {% for error in form.current_password.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="mb-3">
                                    {{ form.new_password.label(class="form-label") }}
                                    {{ form.new_password(class="form-control", id="newPassword") }}
                                    {% if form.new_password.errors %}
                                        <div class="text-danger">
                                            {% for error in form.new_password.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <div class="password-strength mt-2">
                                        <div class="progress" style="height: 5px;">
                                            <div class="progress-bar" id="strengthBar" role="progressbar" style="width: 0%"></div>
                                        </div>
                                        <small id="strengthText" class="text-muted">Введите новый пароль</small>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    {{ form.confirm_password.label(class="form-label") }}
                                    {{ form.confirm_password(class="form-control", id="confirmPassword") }}
                                    {% if form.confirm_password.errors %}
                                        <div class="text-danger">
                                            {% for error in form.confirm_password.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <div id="passwordMatch" class="mt-1"></div>
                                </div>

                                <div class="d-flex gap-2">
                                    {{ form.submit(class="btn btn-primary", id="submitBtn") }}
                                    <a href="{{ url_for('profile.security_settings') }}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left"></i> Назад
                                    </a>
                                </div>
                            </form>
                        </div>

                        <div class="col-md-4">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> Требования к паролю:</h6>
                                <ul class="mb-0">
                                    <li>Минимум 8 символов</li>
                                    <li>Заглавные и строчные буквы</li>
                                    <li>Цифры</li>
                                    <li>Специальные символы</li>
                                </ul>
                            </div>

                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle"></i> Безопасность:</h6>
                                <ul class="mb-0">
                                    <li>Используйте уникальный пароль</li>
                                    <li>Не используйте личную информацию</li>
                                    <li>Регулярно меняйте пароль</li>
                                    <li>Не сообщайте пароль другим</li>
                                </ul>
                            </div>

                            <div class="alert alert-success">
                                <h6><i class="fas fa-shield-alt"></i> Дополнительная защита:</h6>
                                <p class="mb-2">Для максимальной безопасности рекомендуем включить двухфакторную аутентификацию.</p>
                                {% if not current_user.two_factor_enabled %}
                                    <a href="{{ url_for('profile.two_factor_setup') }}" class="btn btn-sm btn-success">
                                        <i class="fas fa-plus"></i> Включить 2FA
                                    </a>
                                {% else %}
                                    <span class="text-success">
                                        <i class="fas fa-check"></i> 2FA включена
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const currentPasswordInput = document.getElementById('currentPassword');
    const newPasswordInput = document.getElementById('newPassword');
    const confirmPasswordInput = document.getElementById('confirmPassword');
    const strengthBar = document.getElementById('strengthBar');
    const strengthText = document.getElementById('strengthText');
    const passwordMatch = document.getElementById('passwordMatch');
    const submitBtn = document.getElementById('submitBtn');

    // Проверка силы нового пароля
    newPasswordInput.addEventListener('input', function() {
        const password = this.value;
        const strength = calculatePasswordStrength(password);
        
        updateStrengthIndicator(strength);
        checkPasswordMatch();
        checkFormValidity();
    });

    // Проверка совпадения паролей
    confirmPasswordInput.addEventListener('input', function() {
        checkPasswordMatch();
        checkFormValidity();
    });

    // Проверка текущего пароля
    currentPasswordInput.addEventListener('input', checkFormValidity);

    function calculatePasswordStrength(password) {
        let score = 0;
        let feedback = [];

        if (password.length >= 8) {
            score += 25;
        } else {
            feedback.push('минимум 8 символов');
        }

        if (/[a-z]/.test(password) && /[A-Z]/.test(password)) {
            score += 25;
        } else {
            feedback.push('заглавные и строчные буквы');
        }

        if (/\d/.test(password)) {
            score += 25;
        } else {
            feedback.push('цифры');
        }

        if (/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password)) {
            score += 25;
        } else {
            feedback.push('специальные символы');
        }

        return { score, feedback };
    }

    function updateStrengthIndicator(strength) {
        const { score, feedback } = strength;
        
        strengthBar.style.width = score + '%';
        
        if (score < 25) {
            strengthBar.className = 'progress-bar bg-danger';
            strengthText.textContent = 'Слабый пароль';
            strengthText.className = 'text-danger';
        } else if (score < 50) {
            strengthBar.className = 'progress-bar bg-warning';
            strengthText.textContent = 'Средний пароль';
            strengthText.className = 'text-warning';
        } else if (score < 75) {
            strengthBar.className = 'progress-bar bg-info';
            strengthText.textContent = 'Хороший пароль';
            strengthText.className = 'text-info';
        } else {
            strengthBar.className = 'progress-bar bg-success';
            strengthText.textContent = 'Отличный пароль';
            strengthText.className = 'text-success';
        }

        if (feedback.length > 0) {
            strengthText.textContent += ' (нужно: ' + feedback.join(', ') + ')';
        }
    }

    function checkPasswordMatch() {
        const newPassword = newPasswordInput.value;
        const confirmPassword = confirmPasswordInput.value;

        if (confirmPassword === '') {
            passwordMatch.innerHTML = '';
            return false;
        }

        if (newPassword === confirmPassword) {
            passwordMatch.innerHTML = '<small class="text-success"><i class="fas fa-check"></i> Пароли совпадают</small>';
            return true;
        } else {
            passwordMatch.innerHTML = '<small class="text-danger"><i class="fas fa-times"></i> Пароли не совпадают</small>';
            return false;
        }
    }

    function checkFormValidity() {
        const currentPassword = currentPasswordInput.value;
        const newPassword = newPasswordInput.value;
        const confirmPassword = confirmPasswordInput.value;

        // Проверяем все условия
        const currentPasswordValid = currentPassword.length > 0;
        const newPasswordValid = calculatePasswordStrength(newPassword).score >= 75;
        const passwordsMatch = newPassword === confirmPassword && confirmPassword !== '';
        const passwordsDifferent = currentPassword !== newPassword;
        
        const formValid = currentPasswordValid && newPasswordValid && passwordsMatch && passwordsDifferent;
        
        submitBtn.disabled = !formValid;
        
        // Показываем предупреждение, если новый пароль совпадает с текущим
        if (currentPassword === newPassword && newPassword !== '') {
            if (!document.getElementById('samePasswordWarning')) {
                const warning = document.createElement('div');
                warning.id = 'samePasswordWarning';
                warning.className = 'alert alert-warning mt-2';
                warning.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Новый пароль должен отличаться от текущего';
                newPasswordInput.parentNode.appendChild(warning);
            }
        } else {
            const warning = document.getElementById('samePasswordWarning');
            if (warning) {
                warning.remove();
            }
        }
    }

    // Показать/скрыть пароль
    function togglePasswordVisibility(inputId, buttonId) {
        const input = document.getElementById(inputId);
        const button = document.getElementById(buttonId);
        
        if (input.type === 'password') {
            input.type = 'text';
            button.innerHTML = '<i class="fas fa-eye-slash"></i>';
        } else {
            input.type = 'password';
            button.innerHTML = '<i class="fas fa-eye"></i>';
        }
    }

    // Добавляем кнопки показать/скрыть пароль
    const passwordInputs = ['currentPassword', 'newPassword', 'confirmPassword'];
    passwordInputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        const wrapper = document.createElement('div');
        wrapper.className = 'input-group';
        
        input.parentNode.insertBefore(wrapper, input);
        wrapper.appendChild(input);
        
        const button = document.createElement('button');
        button.type = 'button';
        button.className = 'btn btn-outline-secondary';
        button.innerHTML = '<i class="fas fa-eye"></i>';
        button.onclick = () => togglePasswordVisibility(inputId, button.id);
        button.id = inputId + 'ToggleBtn';
        
        wrapper.appendChild(button);
    });
});
</script>

<style>
.list-group-item.active {
    background-color: #007bff;
    border-color: #007bff;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.alert {
    border-radius: 0.375rem;
}

.btn {
    border-radius: 0.375rem;
}

.progress {
    background-color: #e9ecef;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.input-group .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.input-group .form-control {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}
</style>
{% endblock %}
