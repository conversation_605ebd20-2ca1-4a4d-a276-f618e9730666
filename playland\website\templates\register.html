{% extends "base.html" %}

{% block title %}PLAYLAND - Регистрация{% endblock %}

{% block namespace %}register{% endblock %}

{% block extra_head %}
<style>
    .hero {
        background-image: url("{{ url_for('static', filename='images/grass_background.jpg') }}");
        background-size: cover;
        background-position: center center;
        background-attachment: fixed;
        height: 50vh;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        padding: 20px;
        padding-top: 100px;
        box-sizing: border-box;
        position: relative;
        overflow: hidden;
    }

    .hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.6);
        backdrop-filter: blur(2px);
        z-index: 1;
    }

    .hero-content {
        position: relative;
        z-index: 2;
        max-width: 900px;
        width: 100%;
    }

    .hero h1 {
        font-family: 'Press Start 2P', cursive;
        font-size: 3.5em;
        color: #00ff00;
        text-shadow: 2px 2px 0 #000000, 0 0 15px #00ff00;
        margin-bottom: 20px;
        line-height: 1.3;
        animation: textPopIn 0.8s cubic-bezier(0.68, -0.55, 0.27, 1.55) 0.5s forwards, heroGlow 2.5s infinite alternate 1s;
        opacity: 0;
        transform: scale(0.5);
    }

    @keyframes textPopIn {
        to {
            opacity: 1;
            transform: scale(1);
        }
    }

    @keyframes heroGlow {
        0% {
            text-shadow: 2px 2px 0 #000000, 0 0 5px rgba(0, 255, 0, 0.5);
        }
        100% {
            text-shadow: 2px 2px 0 #000000, 0 0 15px #00ff00, 0 0 30px #00ff00, 0 0 40px #00ff00;
        }
    }

    .content-section {
        padding: 80px 20px;
        background-color: rgba(0, 0, 0, 0.8);
    }

    .register-container {
        max-width: 500px;
        margin: 0 auto;
        background-color: rgba(0, 0, 0, 0.5);
        border: 2px solid #00ff00;
        border-radius: 10px;
        padding: 40px;
        box-shadow: 0 0 20px rgba(0, 255, 0, 0.2);
    }

    .form-group {
        margin-bottom: 25px;
    }

    .form-group label {
        display: block;
        margin-bottom: 10px;
        font-family: 'Press Start 2P', cursive;
        font-size: 0.9em;
        color: #00ff00;
    }

    .form-control {
        width: 100%;
        padding: 12px 15px;
        background-color: rgba(0, 0, 0, 0.7);
        border: 2px solid rgba(0, 255, 0, 0.5);
        border-radius: 5px;
        color: #ffffff;
        font-family: 'Roboto', sans-serif;
        font-size: 1em;
        transition: all 0.3s ease;
        box-sizing: border-box;
    }

    .form-control:focus {
        border-color: #00ff00;
        outline: none;
        box-shadow: 0 0 15px rgba(0, 255, 0, 0.3);
    }

    .form-help {
        display: block;
        color: #aaa;
        font-size: 0.8em;
        margin-top: 5px;
    }

    .error-message {
        color: #ff6b6b;
        margin-top: 5px;
        font-size: 0.9em;
    }

    .btn-minecraft {
        display: inline-block;
        padding: 15px 30px;
        background-color: #00ff00;
        color: #000;
        text-decoration: none;
        font-family: 'Press Start 2P', cursive;
        font-size: 0.9em;
        text-transform: uppercase;
        border: none;
        position: relative;
        overflow: hidden;
        transition: all 0.3s;
        box-shadow: 0 4px 0 #006400, 0 0 20px rgba(0, 255, 0, 0.4);
        border-radius: 3px;
        cursor: pointer;
        width: 100%;
    }

    .btn-minecraft:hover {
        transform: translateY(-5px);
        box-shadow: 0 9px 0 #006400, 0 0 30px rgba(0, 255, 0, 0.6);
    }

    .form-links {
        text-align: center;
        margin-top: 25px;
    }

    .form-links a {
        color: #f0f0f0;
        text-decoration: none;
        font-size: 0.9em;
        transition: color 0.3s;
    }

    .form-links a:hover {
        color: #00ff00;
        text-decoration: underline;
    }

    .social-login {
        margin-top: 40px;
        padding-top: 20px;
        border-top: 1px dashed rgba(0, 255, 0, 0.3);
        text-align: center;
    }

    .social-login-title {
        font-family: 'Press Start 2P', cursive;
        font-size: 0.9em;
        color: #00ff00;
        margin-bottom: 20px;
    }

    .social-buttons {
        display: flex;
        justify-content: center;
        gap: 15px;
    }

    .social-button {
        padding: 12px 20px;
        background-color: rgba(0, 0, 0, 0.7);
        border: 2px solid;
        border-radius: 5px;
        color: #fff;
        text-decoration: none;
        display: flex;
        align-items: center;
        transition: all 0.3s;
    }

    .social-button i {
        margin-right: 10px;
    }

    .discord-button {
        border-color: #5865F2;
    }

    .discord-button:hover {
        background-color: #5865F2;
        box-shadow: 0 0 15px rgba(88, 101, 242, 0.6);
    }

    .telegram-button {
        border-color: #0088cc;
    }

    .telegram-button:hover {
        background-color: #0088cc;
        box-shadow: 0 0 15px rgba(0, 136, 204, 0.6);
    }

    @media (max-width: 768px) {
        .hero h1 {
            font-size: 2.2em;
        }

        .register-container {
            padding: 20px;
        }

        .social-buttons {
            flex-direction: column;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="hero">
    <div class="particles-container" id="particles"></div>
    <div class="hero-content">
        <h1 class="animate-fade-in-up">РЕГИСТРАЦИЯ</h1>
            </div>
</section>

<section class="content-section">
    <div class="register-container animate-fade-in-up">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">{{ message }}</div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <form method="POST" action="{{ url_for('register') }}">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                <div class="form-group">
                <label for="username">Имя пользователя</label>
                <input type="text" class="form-control" id="username" name="username" required>
                <span class="form-help">Используйте только буквы, цифры и символы _ -</span>
                    {% if form.username.errors %}
                    <div class="error-message">{{ form.username.errors[0] }}</div>
                    {% endif %}
                </div>

                <div class="form-group">
                <label for="email">Email</label>
                <input type="email" class="form-control" id="email" name="email" required>
                    {% if form.email.errors %}
                    <div class="error-message">{{ form.email.errors[0] }}</div>
                    {% endif %}
                </div>

                <div class="form-group">
                <label for="password">Пароль</label>
                <input type="password" class="form-control" id="password" name="password" required>
                <span class="form-help">Минимум 8 символов, включая цифры и буквы</span>
                    {% if form.password.errors %}
                    <div class="error-message">{{ form.password.errors[0] }}</div>
                    {% endif %}
                </div>

                <div class="form-group">
                <label for="confirm_password">Подтверждение пароля</label>
                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    {% if form.confirm_password.errors %}
                    <div class="error-message">{{ form.confirm_password.errors[0] }}</div>
                    {% endif %}
                </div>

            <div class="form-group">
                <button type="submit" class="btn-minecraft">Зарегистрироваться</button>
                </div>

            <div class="form-links">
                <a href="{{ url_for('login') }}">Уже есть аккаунт? Войти</a>
            </div>
        </form>

                <div class="social-login">
            <div class="social-login-title">Или зарегистрироваться через</div>
                    <div class="social-buttons">
                <a href="#" class="social-button discord-button" onclick="openDiscordAuthPopup(); return false;">
                            <i class="fab fa-discord"></i> Discord
                        </a>
                <!-- Встраиваемый виджет Telegram -->
                <div id="telegram-login-container">
                    <script async src="https://telegram.org/js/telegram-widget.js?22"
                        data-telegram-login="{{ config.get('TELEGRAM_BOT_USERNAME', 'your_bot_username') }}"
                        data-size="medium"
                        data-radius="8"
                        data-auth-url="{{ url_for('auth_telegram_callback', _external=True) }}"
                        data-request-access="write"
                        data-userpic="true"
                        data-lang="ru"
                        ></script>
                </div>
                </div>
        </div>
    </div>
</section>

<script>
    // Функция для открытия всплывающего окна Discord OAuth
    function openDiscordAuthPopup() {
        // Получаем ID приложения Discord
        let clientId = "{{ config.get('DISCORD_CLIENT_ID', '') }}";

        // Если ID не установлен через конфигурацию, используем значение по умолчанию
        // ВАЖНО: замените на реальный ID вашего приложения Discord!
        if (!clientId || clientId === '') {
            clientId = "1234567890123456789"; // Замените на реальный ID вашего приложения Discord
        }

        const redirectUri = encodeURIComponent("{{ url_for('auth.discord_callback', _external=True) }}");

        // Получаем state для CSRF защиты
        let state = "{{ session.get('discord_oauth_state', '') }}";

        // Если state не установлен, запрашиваем его с сервера через AJAX
        if (!state || state === '') {
            // Сначала получаем state с сервера
            fetch('{{ url_for("auth.auth_discord_state") }}')
                .then(response => response.json())
                .then(data => {
                    // Затем открываем Discord OAuth окно с полученным state
                    openDiscordPopupWithState(clientId, redirectUri, data.state);
                })
                .catch(error => {
                    console.error('Ошибка при получении state:', error);
                    // Если не удалось получить state, генерируем его на клиенте
                    const fallbackState = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
                    openDiscordPopupWithState(clientId, redirectUri, fallbackState);
                });
        } else {
            // Если state уже установлен, используем его
            openDiscordPopupWithState(clientId, redirectUri, state);
        }
    }

    // Вспомогательная функция для открытия окна Discord с установленным state
    function openDiscordPopupWithState(clientId, redirectUri, state) {
        const popupURL = `https://discord.com/api/oauth2/authorize?client_id=${clientId}&redirect_uri=${redirectUri}&response_type=code&scope=identify%20email&state=${state}`;

        const width = 600;
        const height = 800;
        const left = (window.innerWidth - width) / 2;
        const top = (window.innerHeight - height) / 2;

        window.open(popupURL, "discord-oauth", `width=${width},height=${height},top=${top},left=${left}`);
        return false;
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Создаем частицы
        const particlesContainer = document.getElementById('particles');
        if (!particlesContainer) return;

        const colors = [
            'rgba(0, 255, 0, 0.4)',
            'rgba(50, 205, 50, 0.4)',
            'rgba(173, 255, 47, 0.4)',
            'rgba(152, 251, 152, 0.4)'
        ];

        for (let i = 0; i < 50; i++) {
            const size = Math.random() * 6 + 3;
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.width = `${size}px`;
            particle.style.height = `${size}px`;
            particle.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
            particle.style.left = `${Math.random() * 100}%`;
            particle.style.setProperty('--rand-x', Math.random());
            particle.style.boxShadow = `0 0 ${size}px ${particle.style.backgroundColor}`;
            particle.style.animationDuration = `${Math.random() * 20 + 10}s`;
            particle.style.animationDelay = `${Math.random() * 5}s`;

            particlesContainer.appendChild(particle);
        }
    });
</script>
{% endblock %}