{% extends "base.html" %}

{% block title %}PLAYLAND - Галерея{% endblock %}

{% block namespace %}gallery{% endblock %}

{% block extra_head %}
<style>
    .hero {
        background-image: url("{{ url_for('static', filename='images/grass_background.jpg') }}");
        background-size: cover;
        background-position: center center;
        background-attachment: fixed;
        height: 50vh;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        padding: 20px;
        padding-top: 100px;
        box-sizing: border-box;
        position: relative;
        overflow: hidden;
    }
    
    .hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.6);
        backdrop-filter: blur(2px);
        z-index: 1;
    }
    
    .hero-content {
        position: relative;
        z-index: 2;
        max-width: 900px;
        width: 100%;
    }
    
    .hero h1 {
        font-family: 'Press Start 2P', cursive;
        font-size: 3.5em;
        color: #00ff00;
        text-shadow: 2px 2px 0 #000000, 0 0 15px #00ff00;
        margin-bottom: 20px;
        line-height: 1.3;
        animation: textPopIn 0.8s cubic-bezier(0.68, -0.55, 0.27, 1.55) 0.5s forwards, heroGlow 2.5s infinite alternate 1s;
        opacity: 0;
        transform: scale(0.5);
    }
    
    @keyframes textPopIn {
        to {
            opacity: 1;
            transform: scale(1);
        }
    }
    
    @keyframes heroGlow {
        0% {
            text-shadow: 2px 2px 0 #000000, 0 0 5px rgba(0, 255, 0, 0.5); 
        }
        100% {
            text-shadow: 2px 2px 0 #000000, 0 0 15px #00ff00, 0 0 30px #00ff00, 0 0 40px #00ff00;
        }
    }

    .content-section {
        padding: 80px 20px;
        background-color: rgba(0, 0, 0, 0.8);
    }
    
    .content-section h2 {
        font-family: 'Press Start 2P', cursive;
        font-size: 2.2em;
        color: #00ff00;
        text-align: center;
        margin-bottom: 50px;
        text-shadow: 0 0 10px #00ff00;
    }
    
    .gallery-container {
        max-width: 1200px;
        margin: 0 auto;
    }
    
    .gallery-filters {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        margin-bottom: 40px;
    }
    
    .filter-button {
        background-color: transparent;
        border: 2px solid #00ff00;
        color: #00ff00;
        padding: 10px 20px;
        margin: 0 10px 10px 0;
        font-family: 'Press Start 2P', cursive;
        font-size: 0.8em;
        cursor: pointer;
        transition: all 0.3s;
        border-radius: 5px;
        text-shadow: 0 0 5px rgba(0, 255, 0, 0.5);
    }
    
    .filter-button:hover, .filter-button.active {
        background-color: #00ff00;
        color: #000;
        box-shadow: 0 0 15px rgba(0, 255, 0, 0.6);
    }
    
    .gallery-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
    }
    
    .gallery-item {
        height: 250px;
        overflow: hidden;
        position: relative;
        border-radius: 5px;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
        cursor: pointer;
        transition: all 0.3s ease;
        transform: translateY(50px);
        opacity: 0;
        animation: fadeInUp 0.5s ease forwards;
        animation-delay: calc(var(--delay) * 0.1s);
    }
    
    @keyframes fadeInUp {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .gallery-item:hover {
        transform: scale(1.05);
        box-shadow: 0 0 25px rgba(0, 255, 0, 0.3);
    }
    
    .gallery-item img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
    }
    
    .gallery-item:hover img {
        transform: scale(1.1);
    }
    
    .gallery-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(to bottom, rgba(0,0,0,0) 60%, rgba(0,0,0,0.8) 100%);
        z-index: 1;
        opacity: 0.7;
        transition: opacity 0.3s ease;
    }
    
    .gallery-item:hover::before {
        opacity: 1;
    }
    
    .gallery-item-info {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 15px;
        color: #fff;
        z-index: 2;
        transform: translateY(10px);
        opacity: 0.8;
        transition: all 0.3s ease;
    }
    
    .gallery-item:hover .gallery-item-info {
        transform: translateY(0);
        opacity: 1;
    }
    
    .gallery-item-title {
        font-family: 'Press Start 2P', cursive;
        font-size: 0.9em;
        margin-bottom: 5px;
        color: #00ff00;
        text-shadow: 0 0 5px rgba(0, 255, 0, 0.5);
    }
    
    .gallery-item-author {
        font-size: 0.8em;
        opacity: 0.8;
    }
    
    .lightbox {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.3s ease;
    }
    
    .lightbox.active {
        opacity: 1;
        pointer-events: auto;
    }
    
    .lightbox-content {
        position: relative;
        max-width: 90%;
        max-height: 80vh;
        margin: 0 auto;
    }
    
    .lightbox-image {
        max-width: 100%;
        max-height: 80vh;
        border: 3px solid #00ff00;
        box-shadow: 0 0 30px rgba(0, 255, 0, 0.3);
    }
    
    .lightbox-caption {
        position: absolute;
        left: 0;
        right: 0;
        bottom: -40px;
        text-align: center;
        color: #fff;
        font-family: 'Press Start 2P', cursive;
        font-size: 0.9em;
        padding: 10px;
    }
    
    .lightbox-close {
        position: absolute;
        top: 20px;
        right: 20px;
        font-size: 2em;
        color: #00ff00;
        cursor: pointer;
        background: none;
        border: none;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        text-shadow: 0 0 10px rgba(0, 255, 0, 0.7);
    }
    
    .lightbox-close:hover {
        transform: rotate(90deg);
        color: #fff;
    }
    
    .lightbox-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        color: #00ff00;
        background: none;
        border: none;
        font-size: 3em;
        cursor: pointer;
        transition: all 0.3s ease;
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        text-shadow: 0 0 10px rgba(0, 255, 0, 0.7);
    }
    
    .lightbox-nav:hover {
        color: #fff;
    }
    
    .lightbox-prev {
        left: 20px;
    }
    
    .lightbox-next {
        right: 20px;
    }
    
    .more-button {
        display: block;
        margin: 50px auto 0;
        padding: 15px 30px;
        background-color: #00ff00;
        color: #000;
        font-family: 'Press Start 2P', cursive;
        font-size: 0.9em;
        border: none;
        cursor: pointer;
        border-radius: 5px;
        transition: all 0.3s;
        box-shadow: 0 4px 0 #006400, 0 0 20px rgba(0, 255, 0, 0.3);
    }
    
    .more-button:hover {
        transform: translateY(-5px);
        box-shadow: 0 9px 0 #006400, 0 0 30px rgba(0, 255, 0, 0.5);
    }
    
    .upload-section {
        margin-top: 80px;
        padding: 40px;
        background-color: rgba(0, 20, 0, 0.5);
        border-radius: 10px;
        border: 2px solid #00ff00;
        text-align: center;
    }
    
    .upload-section h3 {
        font-family: 'Press Start 2P', cursive;
        font-size: 1.5em;
        color: #00ff00;
        margin-bottom: 20px;
    }
    
    .upload-section p {
        color: #f0f0f0;
        margin-bottom: 30px;
        max-width: 800px;
        margin-left: auto;
        margin-right: auto;
    }
    
    .upload-button {
        display: inline-block;
        padding: 15px 30px;
        background-color: #00ff00;
        color: #000;
        text-decoration: none;
        font-family: 'Press Start 2P', cursive;
        font-size: 0.9em;
        text-transform: uppercase;
        border: none;
        position: relative;
        overflow: hidden;
        transition: all 0.3s;
        box-shadow: 0 4px 0 #006400, 0 0 20px rgba(0, 255, 0, 0.4);
        border-radius: 3px;
    }
    
    .upload-button:hover {
        transform: translateY(-5px);
        box-shadow: 0 9px 0 #006400, 0 0 30px rgba(0, 255, 0, 0.6);
    }
    
    .no-results {
        text-align: center;
        padding: 50px;
        color: #f0f0f0;
        font-family: 'Press Start 2P', cursive;
        font-size: 1.2em;
        display: none;
    }

    .no-posts {
        grid-column: 1 / -1;
        text-align: center;
        padding: 80px 20px;
        color: #f0f0f0;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 10px;
        border: 2px dashed rgba(0, 255, 0, 0.3);
    }

    .no-posts h3 {
        font-family: 'Press Start 2P', cursive;
        font-size: 1.5em;
        color: #00ff00;
        margin-bottom: 20px;
        text-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
    }

    .no-posts p {
        font-size: 1rem;
        line-height: 1.6;
        max-width: 600px;
        margin: 0 auto;
    }
    
    @media (max-width: 768px) {
        .hero h1 {
            font-size: 2em;
        }
        
        .content-section h2 {
            font-size: 1.5em;
        }
        
        .filter-button {
            font-size: 0.7em;
            padding: 8px 15px;
        }
        
        .gallery-grid {
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        }
        
        .lightbox-content {
            max-width: 95%;
        }
        
        .lightbox-nav {
            font-size: 2em;
        }
        
        .upload-section {
            padding: 20px;
        }
        
        .upload-section h3 {
            font-size: 1.2em;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="hero">
    <div class="particles-container" id="particles"></div>
    <div class="hero-content">
        <h1 class="animate-fade-in-up">ГАЛЕРЕЯ</h1>
    </div>
</section>

<section class="content-section">
    <h2 class="animate-fade-in">Лучшие постройки и моменты с сервера</h2>
    
    <div class="gallery-container">
        <div class="gallery-filters animate-fade-in">
            <button class="filter-button active" data-filter="all">Все</button>
            <button class="filter-button" data-filter="builds">Постройки</button>
            <button class="filter-button" data-filter="events">События</button>
            <button class="filter-button" data-filter="landscape">Ландшафты</button>
            <button class="filter-button" data-filter="players">Игроки</button>
        </div>
        
        <div class="gallery-grid">
            {% if posts %}
                {% for post in posts %}
                <div class="gallery-item" style="--delay: {{ loop.index }}" data-category="{{ post.category }}">
                    <img src="{{ post.image_url }}" alt="{{ post.title }}" onerror="this.src='{{ url_for('static', filename='images/placeholder.jpg') }}'">
                    <div class="gallery-item-info">
                        <div class="gallery-item-title">{{ post.title }}</div>
                        <div class="gallery-item-author">Автор: {{ post.author }}</div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="no-posts">
                    <h3>Галерея пуста</h3>
                    <p>Пока что в галерее нет постов. Администраторы могут добавить их через админ-панель.</p>
                </div>
            {% endif %}
        </div>
        
        <div class="no-results" id="noResults">
            <p>По выбранной категории ничего не найдено</p>
        </div>
        
        <button class="more-button animate-fade-in">Загрузить ещё</button>
        
        <div class="upload-section animate-fade-in">
            <h3>Хотите поделиться своими творениями?</h3>
            <p>Если у вас есть интересные постройки, красивые виды или запоминающиеся моменты с сервера, которыми вы хотели бы поделиться - присылайте их нам! Лучшие скриншоты будут добавлены в нашу галерею.</p>
            <a href="#" class="upload-button">Отправить скриншот</a>
        </div>
    </div>
</section>

<div class="lightbox" id="lightbox">
    <div class="lightbox-content">
        <img src="" alt="" class="lightbox-image" id="lightboxImage">
        <div class="lightbox-caption" id="lightboxCaption"></div>
    </div>
    <button class="lightbox-close" id="lightboxClose">×</button>
    <button class="lightbox-nav lightbox-prev" id="lightboxPrev">‹</button>
    <button class="lightbox-nav lightbox-next" id="lightboxNext">›</button>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Частицы
        const particlesContainer = document.getElementById('particles');
        if (particlesContainer) {
            const colors = [
                'rgba(0, 255, 0, 0.4)',
                'rgba(50, 205, 50, 0.4)',
                'rgba(173, 255, 47, 0.4)',
                'rgba(152, 251, 152, 0.4)'
            ];
            
            for (let i = 0; i < 50; i++) {
                const size = Math.random() * 6 + 3;
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.width = `${size}px`;
                particle.style.height = `${size}px`;
                particle.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
                particle.style.left = `${Math.random() * 100}%`;
                particle.style.setProperty('--rand-x', Math.random());
                particle.style.boxShadow = `0 0 ${size}px ${particle.style.backgroundColor}`;
                particle.style.animationDuration = `${Math.random() * 20 + 10}s`;
                particle.style.animationDelay = `${Math.random() * 5}s`;
                
                particlesContainer.appendChild(particle);
            }
        }
        
        // Фильтрация галереи
        const filterButtons = document.querySelectorAll('.filter-button');
        const galleryItems = document.querySelectorAll('.gallery-item');
        const noResults = document.getElementById('noResults');
        
        filterButtons.forEach(button => {
            button.addEventListener('click', () => {
                // Сбросить активный класс со всех кнопок
                filterButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
                
                const filter = button.getAttribute('data-filter');
                let visibleItems = 0;
                
                galleryItems.forEach(item => {
                    if (filter === 'all' || item.getAttribute('data-category') === filter) {
                        item.style.display = 'block';
                        visibleItems++;
                    } else {
                        item.style.display = 'none';
                    }
                });
                
                // Показать "Ничего не найдено", если нет элементов
                if (visibleItems === 0) {
                    noResults.style.display = 'block';
                } else {
                    noResults.style.display = 'none';
                }
            });
        });
        
        // Лайтбокс
        const lightbox = document.getElementById('lightbox');
        const lightboxImage = document.getElementById('lightboxImage');
        const lightboxCaption = document.getElementById('lightboxCaption');
        const lightboxClose = document.getElementById('lightboxClose');
        const lightboxPrev = document.getElementById('lightboxPrev');
        const lightboxNext = document.getElementById('lightboxNext');
        
        let currentIndex = 0;
        const visibleItems = () => Array.from(galleryItems).filter(item => 
            item.style.display !== 'none'
        );
        
        galleryItems.forEach(item => {
            item.addEventListener('click', () => {
                const items = visibleItems();
                currentIndex = items.indexOf(item);
                
                const img = item.querySelector('img');
                const title = item.querySelector('.gallery-item-title').innerText;
                const author = item.querySelector('.gallery-item-author').innerText;
                
                lightboxImage.src = img.src;
                lightboxImage.alt = img.alt;
                lightboxCaption.innerText = `${title} - ${author}`;
                
                lightbox.classList.add('active');
                document.body.style.overflow = 'hidden';
            });
        });
        
        lightboxClose.addEventListener('click', () => {
            lightbox.classList.remove('active');
            document.body.style.overflow = '';
        });
        
        lightboxPrev.addEventListener('click', () => {
            const items = visibleItems();
            currentIndex = (currentIndex - 1 + items.length) % items.length;
            const item = items[currentIndex];
            
            const img = item.querySelector('img');
            const title = item.querySelector('.gallery-item-title').innerText;
            const author = item.querySelector('.gallery-item-author').innerText;
            
            lightboxImage.src = img.src;
            lightboxImage.alt = img.alt;
            lightboxCaption.innerText = `${title} - ${author}`;
        });
        
        lightboxNext.addEventListener('click', () => {
            const items = visibleItems();
            currentIndex = (currentIndex + 1) % items.length;
            const item = items[currentIndex];
            
            const img = item.querySelector('img');
            const title = item.querySelector('.gallery-item-title').innerText;
            const author = item.querySelector('.gallery-item-author').innerText;
            
            lightboxImage.src = img.src;
            lightboxImage.alt = img.alt;
            lightboxCaption.innerText = `${title} - ${author}`;
        });
        
        // Закрытие лайтбокса по клику вне изображения или Escape
        lightbox.addEventListener('click', function(e) {
            if (e.target === lightbox) {
                lightbox.classList.remove('active');
                document.body.style.overflow = '';
            }
        });
        
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && lightbox.classList.contains('active')) {
                lightbox.classList.remove('active');
                document.body.style.overflow = '';
            } else if (e.key === 'ArrowLeft' && lightbox.classList.contains('active')) {
                lightboxPrev.click();
            } else if (e.key === 'ArrowRight' && lightbox.classList.contains('active')) {
                lightboxNext.click();
            }
        });
        
        // Кнопка "Загрузить еще"
        const moreButton = document.querySelector('.more-button');
        moreButton.addEventListener('click', function() {
            // Здесь можно добавить логику загрузки дополнительных изображений
            // через AJAX или другим способом
            
            // Для демонстрации просто покажем уведомление
            alert('Функция загрузки дополнительных изображений будет доступна в ближайшее время!');
        });
    });
</script>
{% endblock %} 