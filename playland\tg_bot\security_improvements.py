#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Модуль улучшений безопасности для Telegram бота PlayLand
"""

import hashlib
import hmac
import time
import json
import re
import html
import logging
from functools import wraps
from collections import defaultdict, deque
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple, Any

logger = logging.getLogger(__name__)

class SecurityManager:
    """Менеджер безопасности для бота"""

    def __init__(self, bot_token: str):
        self.bot_token = bot_token
        self.rate_limiter = RateLimiter()
        self.input_validator = InputValidator()
        self.webapp_validator = WebAppValidator(bot_token)

    def validate_user_input(self, text: str, input_type: str = "general") -> Tuple[bool, str]:
        """Валидация пользовательского ввода"""
        return self.input_validator.validate(text, input_type)

    def check_rate_limit(self, user_id: int, action: str = "message") -> bool:
        """Проверка ограничений по частоте запросов"""
        return self.rate_limiter.check_limit(user_id, action)

    def validate_webapp_data(self, init_data: str) -> Tuple[bool, Optional[Dict]]:
        """Валидация данных веб-приложения"""
        return self.webapp_validator.validate(init_data)

    def sanitize_output(self, text: str) -> str:
        """Очистка вывода от потенциально опасного содержимого"""
        # HTML-экранирование
        text = html.escape(text)

        # Удаление потенциально опасных символов
        text = re.sub(r'[<>"\']', '', text)

        # Ограничение длины
        if len(text) > 4000:  # Telegram message limit
            text = text[:3997] + "..."

        return text

class RateLimiter:
    """Ограничитель частоты запросов"""

    def __init__(self):
        # Хранилище для отслеживания запросов пользователей
        self.user_requests: Dict[int, Dict[str, deque]] = defaultdict(lambda: defaultdict(deque))

        # Лимиты для разных типов действий
        self.limits = {
            "message": (10, 60),      # 10 сообщений в минуту
            "command": (5, 60),       # 5 команд в минуту
            "webapp": (3, 300),       # 3 заявки в 5 минут
            "admin": (20, 60),        # 20 админ-действий в минуту
        }

    def check_limit(self, user_id: int, action: str = "message") -> bool:
        """Проверка лимита для пользователя"""
        if action not in self.limits:
            action = "message"

        max_requests, time_window = self.limits[action]
        current_time = time.time()

        # Получаем очередь запросов для пользователя и действия
        requests_queue = self.user_requests[user_id][action]

        # Удаляем старые запросы
        while requests_queue and requests_queue[0] < current_time - time_window:
            requests_queue.popleft()

        # Проверяем лимит
        if len(requests_queue) >= max_requests:
            logger.warning(f"Rate limit exceeded for user {user_id}, action {action}")
            return False

        # Добавляем текущий запрос
        requests_queue.append(current_time)
        return True

    def get_remaining_time(self, user_id: int, action: str = "message") -> int:
        """Получение времени до сброса лимита"""
        if action not in self.limits:
            action = "message"

        _, time_window = self.limits[action]
        requests_queue = self.user_requests[user_id][action]

        if not requests_queue:
            return 0

        oldest_request = requests_queue[0]
        remaining = time_window - (time.time() - oldest_request)
        return max(0, int(remaining))

class InputValidator:
    """Валидатор пользовательского ввода"""

    def __init__(self):
        self.patterns = {
            "nickname": r'^[a-zA-Z0-9_]{3,16}$',
            "discord_old": r'^.{2,32}#\d{4}$',
            "discord_new": r'^[a-zA-Z0-9_.]{2,32}$',
            "reason": r'^.{10,500}$',
            "rejection_reason": r'^.{5,200}$',
        }

    def validate(self, text: str, input_type: str) -> Tuple[bool, str]:
        """Валидация текста по типу"""
        if not text:
            return False, "Пустое значение не допускается"

        # Базовая проверка на длину
        if len(text) > 1000:
            return False, "Слишком длинный текст"

        # Проверка на потенциально опасные символы
        if self._contains_dangerous_chars(text):
            return False, "Текст содержит недопустимые символы"

        # Специфичная валидация по типу
        if input_type == "nickname":
            return self._validate_nickname(text)
        elif input_type == "discord":
            return self._validate_discord(text)
        elif input_type == "reason":
            return self._validate_reason(text)
        elif input_type == "rejection_reason":
            return self._validate_rejection_reason(text)

        return True, "OK"

    def _contains_dangerous_chars(self, text: str) -> bool:
        """Проверка на опасные символы"""
        dangerous_patterns = [
            r'<script',
            r'javascript:',
            r'data:',
            r'vbscript:',
            r'onload=',
            r'onerror=',
            r'eval\(',
            r'exec\(',
        ]

        text_lower = text.lower()
        return any(re.search(pattern, text_lower) for pattern in dangerous_patterns)

    def _validate_nickname(self, nickname: str) -> Tuple[bool, str]:
        """Валидация никнейма Minecraft"""
        if not re.match(self.patterns["nickname"], nickname):
            return False, "Никнейм должен содержать только латинские буквы, цифры и подчеркивания (3-16 символов)"

        # Проверка на зарезервированные имена
        reserved_names = ["admin", "moderator", "owner", "console", "server"]
        if nickname.lower() in reserved_names:
            return False, "Данный никнейм зарезервирован"

        return True, "OK"

    def _validate_discord(self, discord_tag: str) -> Tuple[bool, str]:
        """Валидация Discord тега"""
        old_format = re.match(self.patterns["discord_old"], discord_tag)
        new_format = re.match(self.patterns["discord_new"], discord_tag)

        if not (old_format or new_format):
            return False, "Неверный формат Discord тега (username#1234 или username)"

        return True, "OK"

    def _validate_reason(self, reason: str) -> Tuple[bool, str]:
        """Валидация причины заявки"""
        if len(reason) < 10:
            return False, "Причина слишком короткая (минимум 10 символов)"

        if len(reason) > 500:
            return False, "Причина слишком длинная (максимум 500 символов)"

        # Проверка на спам
        if self._is_spam(reason):
            return False, "Текст похож на спам"

        return True, "OK"

    def _validate_rejection_reason(self, reason: str) -> Tuple[bool, str]:
        """Валидация причины отказа"""
        if len(reason) < 5:
            return False, "Причина отказа слишком короткая (минимум 5 символов)"

        if len(reason) > 200:
            return False, "Причина отказа слишком длинная (максимум 200 символов)"

        return True, "OK"

    def _is_spam(self, text: str) -> bool:
        """Простая проверка на спам"""
        # Проверка на повторяющиеся символы
        if re.search(r'(.)\1{10,}', text):
            return True

        # Проверка на слишком много заглавных букв
        if len(re.findall(r'[A-ZА-Я]', text)) > len(text) * 0.7:
            return True

        # Проверка на спам-слова
        spam_words = ["реклама", "скидка", "бесплатно", "выиграй", "приз"]
        text_lower = text.lower()
        spam_count = sum(1 for word in spam_words if word in text_lower)
        if spam_count > 2:
            return True

        return False

class WebAppValidator:
    """Валидатор данных веб-приложения Telegram"""

    def __init__(self, bot_token: str):
        self.bot_token = bot_token

    def validate(self, init_data: str) -> Tuple[bool, Optional[Dict]]:
        """Валидация данных веб-приложения"""
        try:
            # Парсим init_data
            data_dict = self._parse_init_data(init_data)

            # Проверяем время создания данных (критично для безопасности)
            if not self._verify_timestamp(data_dict):
                logger.warning("WebApp data too old or invalid auth_date")
                return False, None

            # Проверяем подпись
            if not self._verify_signature(data_dict):
                logger.warning("Invalid WebApp signature")
                return False, None

            # Парсим пользовательские данные
            user_data = self._parse_user_data(data_dict)

            return True, user_data

        except Exception as e:
            safe_log_error(e, "webapp_validation")
            return False, None

    def _parse_init_data(self, init_data: str) -> Dict[str, str]:
        """Парсинг init_data в словарь"""
        data_dict = {}
        for item in init_data.split('&'):
            if '=' in item:
                key, value = item.split('=', 1)
                data_dict[key] = value
        return data_dict

    def _verify_signature(self, data_dict: Dict[str, str]) -> bool:
        """Проверка подписи WebApp"""
        if 'hash' not in data_dict:
            return False

        received_hash = data_dict.pop('hash')

        # Создаем строку для проверки
        data_check_string = '\n'.join(
            f"{key}={value}" for key, value in sorted(data_dict.items())
        )

        # Вычисляем ожидаемый хеш
        secret_key = hmac.new(
            "WebAppData".encode(),
            self.bot_token.encode(),
            hashlib.sha256
        ).digest()

        expected_hash = hmac.new(
            secret_key,
            data_check_string.encode(),
            hashlib.sha256
        ).hexdigest()

        return hmac.compare_digest(received_hash, expected_hash)

    def _verify_timestamp(self, data_dict: Dict[str, str]) -> bool:
        """Проверка времени создания данных"""
        if 'auth_date' not in data_dict:
            logger.warning("WebApp data missing auth_date field")
            return False

        try:
            auth_date = int(data_dict['auth_date'])
            current_time = int(time.time())

            # Проверяем, что данные не из будущего (защита от подделки)
            if auth_date > current_time + 60:  # 1 минута допуска на расхождение часов
                logger.warning(f"WebApp auth_date is in the future: {auth_date} > {current_time}")
                return False

            # Данные действительны в течение 1 часа (стандарт Telegram)
            age_seconds = current_time - auth_date
            if age_seconds > 3600:
                logger.warning(f"WebApp data too old: {age_seconds} seconds")
                return False

            return True

        except (ValueError, TypeError) as e:
            logger.warning(f"Invalid auth_date format in WebApp data: {e}")
            return False

    def _parse_user_data(self, data_dict: Dict[str, str]) -> Dict:
        """Парсинг данных пользователя"""
        user_data = {}

        if 'user' in data_dict:
            try:
                user_info = json.loads(data_dict['user'])
                user_data.update(user_info)
            except json.JSONDecodeError:
                pass

        return user_data

def rate_limit(action: str = "message"):
    """Декоратор для ограничения частоты запросов"""
    def decorator(func):
        @wraps(func)
        async def wrapper(update, context, *args, **kwargs):
            user_id = update.effective_user.id

            # Получаем security_manager из контекста
            security_manager = getattr(context.bot_data, 'security_manager', None)
            if not security_manager:
                # Если нет менеджера безопасности, пропускаем проверку
                return await func(update, context, *args, **kwargs)

            if not security_manager.check_rate_limit(user_id, action):
                remaining_time = security_manager.rate_limiter.get_remaining_time(user_id, action)
                await update.message.reply_text(
                    f"⏰ Слишком много запросов. Попробуйте через {remaining_time} секунд."
                )
                return

            return await func(update, context, *args, **kwargs)
        return wrapper
    return decorator

def admin_required(func):
    """Декоратор для проверки прав администратора"""
    @wraps(func)
    async def wrapper(update, context, *args, **kwargs):
        user_id = update.effective_user.id

        # Получаем список админов из контекста
        admin_ids = getattr(context.bot_data, 'admin_ids', [])

        if user_id not in admin_ids:
            await update.message.reply_text("🚫 У вас нет доступа к этой команде.")
            return

        return await func(update, context, *args, **kwargs)
    return wrapper

def validate_input(input_type: str):
    """Декоратор для валидации пользовательского ввода"""
    def decorator(func):
        @wraps(func)
        async def wrapper(update, context, *args, **kwargs):
            # Получаем текст сообщения
            text = update.message.text if update.message else ""

            # Получаем security_manager из контекста
            security_manager = getattr(context.bot_data, 'security_manager', None)
            if security_manager:
                is_valid, error_message = security_manager.validate_user_input(text, input_type)
                if not is_valid:
                    await update.message.reply_text(f"❌ {error_message}")
                    return

            return await func(update, context, *args, **kwargs)
        return wrapper
    return decorator

# Функции для безопасного логирования
def safe_log_user_action(user_id: int, action: str, details: str = ""):
    """Безопасное логирование действий пользователя"""
    # Удаляем чувствительную информацию из деталей
    safe_details = re.sub(r'password|token|key|secret', '[REDACTED]', details, flags=re.IGNORECASE)

    logger.info(f"User {user_id} performed action: {action}. Details: {safe_details[:100]}")

def safe_log_error(error: Exception, context: str = ""):
    """Безопасное логирование ошибок"""
    # Логируем только тип ошибки и контекст, без чувствительных данных
    error_type = type(error).__name__
    logger.error(f"Error in {context}: {error_type}")

    # Детальную информацию логируем только в debug режиме
    logger.debug(f"Full error details: {str(error)}")
