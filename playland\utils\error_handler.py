#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Модуль для обработки ошибок в приложении.
Предоставляет централизованную систему логирования и обработки исключений.
"""

import os
import sys
import logging
import traceback
import json
import time
from datetime import datetime
from functools import wraps
from flask import request, jsonify, current_app, render_template

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('error.log')
    ]
)
logger = logging.getLogger('error_handler')

class ErrorHandler:
    """
    Класс для обработки ошибок и исключений в приложении.
    """

    def __init__(self, app=None, notification_service=None):
        """
        Инициализация обработчика ошибок.

        Args:
            app: Экземпляр Flask-приложения
            notification_service: Сервис для отправки уведомлений об ошибках
        """
        self.notification_service = notification_service
        self.error_log_path = 'logs/errors'

        # Создаем директорию для логов, если она не существует
        os.makedirs(self.error_log_path, exist_ok=True)

        if app:
            self.init_app(app)

    def init_app(self, app):
        """
        Инициализирует обработчик ошибок для Flask-приложения.

        Args:
            app: Экземпляр Flask-приложения
        """
        # Регистрируем обработчики ошибок
        app.register_error_handler(404, self.handle_404)
        app.register_error_handler(500, self.handle_500)
        app.register_error_handler(Exception, self.handle_exception)

        # Настраиваем логирование
        if not app.debug:
            # В продакшн-режиме настраиваем более детальное логирование
            file_handler = logging.FileHandler(os.path.join(self.error_log_path, 'app.log'))
            file_handler.setLevel(logging.INFO)
            file_handler.setFormatter(logging.Formatter(
                '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
            ))
            app.logger.addHandler(file_handler)
            app.logger.setLevel(logging.INFO)

        app.logger.info('Инициализирован обработчик ошибок')

    def handle_404(self, error):
        """
        Обрабатывает ошибку 404 (страница не найдена).

        Args:
            error: Объект ошибки

        Returns:
            tuple: Шаблон страницы ошибки и код состояния
        """
        # Логируем ошибку
        logger.info(f"404 ошибка: {request.path} - IP: {request.remote_addr}")

        # Если это API-запрос, возвращаем JSON
        if request.path.startswith('/api/'):
            return jsonify({
                'error': 'Ресурс не найден',
                'status': 404
            }), 404

        # Иначе возвращаем HTML-страницу
        return render_template('errors/404.html'), 404

    def handle_500(self, error):
        """
        Обрабатывает ошибку 500 (внутренняя ошибка сервера).

        Args:
            error: Объект ошибки

        Returns:
            tuple: Шаблон страницы ошибки и код состояния
        """
        # Логируем ошибку
        logger.error(f"500 ошибка: {str(error)} - Путь: {request.path} - IP: {request.remote_addr}")

        # Сохраняем детали ошибки
        self._save_error_details(error)

        # Отправляем уведомление администраторам
        self._notify_admins(error)

        # Если это API-запрос, возвращаем JSON
        if request.path.startswith('/api/'):
            return jsonify({
                'error': 'Внутренняя ошибка сервера',
                'status': 500
            }), 500

        # Иначе возвращаем HTML-страницу
        return render_template('errors/500.html'), 500

    def handle_exception(self, error):
        """
        Обрабатывает необработанные исключения.

        Args:
            error: Объект исключения

        Returns:
            tuple: Шаблон страницы ошибки и код состояния
        """
        # Логируем исключение
        logger.error(f"Необработанное исключение: {str(error)}")
        logger.error(traceback.format_exc())

        # Сохраняем детали ошибки
        self._save_error_details(error)

        # Отправляем уведомление администраторам
        self._notify_admins(error)

        # Если это API-запрос, возвращаем JSON
        if request.path.startswith('/api/'):
            return jsonify({
                'error': 'Внутренняя ошибка сервера',
                'status': 500
            }), 500

        # Иначе возвращаем HTML-страницу
        return render_template('errors/500.html'), 500

    def _save_error_details(self, error):
        """
        Сохраняет детали ошибки в файл.

        Args:
            error: Объект ошибки или исключения
        """
        try:
            # Создаем имя файла на основе временной метки
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"error_{timestamp}_{hash(str(error))}.json"
            filepath = os.path.join(self.error_log_path, filename)

            # Собираем информацию об ошибке
            error_data = {
                'timestamp': datetime.now().isoformat(),
                'error_type': error.__class__.__name__,
                'error_message': str(error),
                'traceback': traceback.format_exc(),
                'request': {
                    'path': request.path,
                    'method': request.method,
                    'remote_addr': request.remote_addr,
                    'user_agent': str(request.user_agent),
                    'headers': dict(request.headers),
                    'args': dict(request.args),
                    'form': dict(request.form) if request.form else None,
                    'json': request.json if request.is_json else None
                }
            }

            # Сохраняем данные в файл
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(error_data, f, indent=4, ensure_ascii=False)

            logger.info(f"Детали ошибки сохранены в {filepath}")

        except Exception as e:
            logger.error(f"Ошибка при сохранении деталей ошибки: {str(e)}")

    def _notify_admins(self, error):
        """
        Отправляет уведомление администраторам о возникшей ошибке.

        Args:
            error: Объект ошибки или исключения
        """
        if not self.notification_service:
            return

        try:
            # Формируем сообщение об ошибке
            error_message = (
                f"⚠️ *Ошибка на сервере*\n"
                f"Тип: `{error.__class__.__name__}`\n"
                f"Сообщение: `{str(error)}`\n"
                f"Путь: `{request.path}`\n"
                f"Метод: `{request.method}`\n"
                f"IP: `{request.remote_addr}`\n"
                f"Время: `{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}`"
            )

            # Отправляем уведомление
            self.notification_service.send_admin_notification(error_message, parse_mode='Markdown')

            logger.info("Уведомление об ошибке отправлено администраторам")

        except Exception as e:
            logger.error(f"Ошибка при отправке уведомления администраторам: {str(e)}")

def handle_errors(f):
    """
    Декоратор для обработки ошибок в функциях.

    Args:
        f: Декорируемая функция

    Returns:
        function: Декорированная функция
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except Exception as e:
            logger.error(f"Ошибка в функции {f.__name__}: {str(e)}")
            logger.error(traceback.format_exc())

            # Если это API-функция, возвращаем JSON с ошибкой
            if hasattr(current_app, 'config') and current_app.config.get('API_FUNCTION', False):
                return jsonify({
                    'error': str(e),
                    'status': 500
                }), 500

            # Иначе пробрасываем исключение дальше
            raise

    return decorated_function

# Создаем глобальный экземпляр обработчика ошибок
error_handler = ErrorHandler()

def init_error_handler(app, notification_service=None):
    """
    Инициализирует обработчик ошибок для Flask-приложения.

    Args:
        app: Экземпляр Flask-приложения
        notification_service: Сервис для отправки уведомлений об ошибках
    """
    global error_handler
    error_handler = ErrorHandler(app, notification_service)
    return error_handler

# Определение пользовательских исключений
class InvalidRequestError(Exception):
    """Исключение для неверных запросов"""
    def __init__(self, message="Неверный запрос", status_code=400):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)

class UnauthorizedError(Exception):
    """Исключение для неавторизованных запросов"""
    def __init__(self, message="Необходима авторизация", status_code=401):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)

class ForbiddenError(Exception):
    """Исключение для запрещенных запросов"""
    def __init__(self, message="Доступ запрещен", status_code=403):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)

class NotFoundError(Exception):
    """Исключение для ресурсов, которые не найдены"""
    def __init__(self, message="Ресурс не найден", status_code=404):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)

def handle_exceptions(f):
    """
    Декоратор для обработки исключений в API-функциях.

    Args:
        f: Декорируемая функция

    Returns:
        function: Декорированная функция
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except InvalidRequestError as e:
            logger.warning(f"Неверный запрос: {str(e)}")
            return jsonify({
                'error': e.message,
                'status': e.status_code
            }), e.status_code
        except UnauthorizedError as e:
            logger.warning(f"Неавторизованный запрос: {str(e)}")
            return jsonify({
                'error': e.message,
                'status': e.status_code
            }), e.status_code
        except ForbiddenError as e:
            logger.warning(f"Запрещенный запрос: {str(e)}")
            return jsonify({
                'error': e.message,
                'status': e.status_code
            }), e.status_code
        except NotFoundError as e:
            logger.warning(f"Ресурс не найден: {str(e)}")
            return jsonify({
                'error': e.message,
                'status': e.status_code
            }), e.status_code
        except Exception as e:
            logger.error(f"Необработанное исключение в API: {str(e)}")
            logger.error(traceback.format_exc())
            return jsonify({
                'error': "Внутренняя ошибка сервера",
                'status': 500
            }), 500

    return decorated_function
