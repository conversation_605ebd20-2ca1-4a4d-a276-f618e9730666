{% extends "base.html" %}

{% block title %}Подключение Discord - PlayLand{% endblock %}

{% block head_extra %}
<style>
    :root {
        --discord-color: #5865F2;
        --discord-hover: #4752C4;
    }
    
    .connect-page {
        max-width: 700px;
        margin: 30px auto;
        padding: 0;
        animation: fadeIn 0.8s ease-out forwards;
        position: relative;
    }
    
    .connect-header {
        background-color: rgba(0, 0, 0, 0.7);
        padding: 25px;
        position: relative;
        border: 4px solid var(--minecraft-stone);
        margin-bottom: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        overflow: hidden;
        text-align: center;
    }
    
    .connect-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: linear-gradient(90deg, transparent, var(--discord-color), transparent);
        animation: headerGlow 3s infinite;
    }
    
    @keyframes headerGlow {
        0%, 100% { opacity: 0.5; }
        50% { opacity: 1; }
    }
    
    .connect-title {
        font-family: var(--pixel-font);
        font-size: 2em;
        color: var(--discord-color);
        text-shadow: 2px 2px 0 #000, 0 0 10px var(--discord-color);
        margin-bottom: 15px;
        text-transform: uppercase;
        letter-spacing: 2px;
    }
    
    .connect-content {
        background-color: rgba(0, 0, 0, 0.6);
        border: 4px solid var(--minecraft-dirt);
        padding: 25px;
        position: relative;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        overflow: hidden;
    }
    
    .connect-content::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: repeating-linear-gradient(
            45deg,
            rgba(255,255,255,0.03),
            rgba(255,255,255,0.03) 10px,
            rgba(0,0,0,0.05) 10px,
            rgba(0,0,0,0.05) 20px
        );
        pointer-events: none;
    }
    
    .connect-info {
        margin-bottom: 30px;
        position: relative;
        z-index: 1;
    }
    
    .connect-info p {
        margin-bottom: 15px;
        line-height: 1.6;
    }
    
    .discord-benefits {
        list-style: none;
        padding: 0;
        margin-bottom: 30px;
    }
    
    .discord-benefits li {
        padding: 10px 0 10px 30px;
        position: relative;
        border-bottom: 1px dashed rgba(88, 101, 242, 0.2);
    }
    
    .discord-benefits li:before {
        content: '\f00c';
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        color: var(--discord-color);
        position: absolute;
        left: 0;
        top: 10px;
    }
    
    .discord-btn {
        display: inline-block;
        padding: 15px 25px;
        background-color: var(--discord-color);
        color: white;
        border: 3px solid rgba(0, 0, 0, 0.8);
        text-decoration: none;
        font-family: var(--pixel-font);
        font-size: 0.9rem;
        position: relative;
        text-align: center;
        text-shadow: 2px 2px 0px rgba(0, 0, 0, 0.8);
        box-shadow: inset -3px -3px 0px 0px rgba(0, 0, 0, 0.3), 
                    inset 3px 3px 0px 0px rgba(255, 255, 255, 0.3);
        cursor: pointer;
        transition: all 0.1s;
        transform-style: preserve-3d;
        letter-spacing: 1px;
        overflow: hidden;
    }
    
    .discord-btn::before {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        top: 6px;
        left: 0;
        background-color: rgba(0, 0, 0, 0.3);
        z-index: -1;
        transform: translateZ(-1px);
        transition: all 0.2s;
    }
    
    .discord-btn:hover {
        transform: translate(0, -3px);
        background-color: var(--discord-hover);
    }
    
    .discord-btn:hover::before {
        top: 9px;
    }
    
    .discord-btn:active {
        transform: translate(0, 3px);
    }
    
    .discord-btn:active::before {
        top: 3px;
    }
    
    .discord-btn i {
        margin-right: 10px;
    }
    
    .discord-status {
        margin-top: 20px;
        padding: 15px;
        border-radius: 3px;
        background-color: rgba(0, 0, 0, 0.3);
        border-left: 3px solid var(--discord-color);
    }
    
    .discord-status.connected {
        border-left-color: #43B581;
    }
    
    .discord-status.error {
        border-left-color: #F04747;
    }
    
    .discord-username {
        display: flex;
        align-items: center;
        margin-top: 10px;
    }
    
    .discord-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 10px;
        border: 2px solid var(--discord-color);
    }
    
    .back-link {
        display: block;
        margin-top: 20px;
        color: white;
        text-decoration: none;
        font-family: var(--pixel-font);
        font-size: 0.8em;
        transition: all 0.3s;
        position: relative;
        z-index: 1;
        text-align: center;
    }
    
    .back-link:hover {
        color: var(--discord-color);
        text-shadow: 0 0 10px var(--discord-color);
    }
</style>
{% endblock %}

{% block content %}
<div class="connect-page">
    <div class="connect-header">
        <h1 class="connect-title">Подключение Discord</h1>
    </div>
    
    <div class="connect-content">
        <div class="connect-info">
            <p>Подключите ваш аккаунт Discord к профилю на PlayLand для получения дополнительных возможностей и удобства использования сервиса.</p>
            
            <h3>Преимущества подключения Discord:</h3>
            <ul class="discord-benefits">
                <li>Быстрый вход на сайт без пароля</li>
                <li>Автоматический вайтлист на сервере</li>
                <li>Доступ к закрытым каналам сообщества</li>
                <li>Уведомления о важных событиях сервера</li>
                <li>Специальная роль на Discord-сервере</li>
            </ul>
            
            {% if current_user.discord_id %}
                <div class="discord-status connected">
                    <p><i class="fas fa-check-circle"></i> Ваш аккаунт Discord успешно подключен!</p>
                    {% if current_user.avatar_url %}
                    <div class="discord-username">
                        <img src="{{ current_user.avatar_url }}" alt="Discord Avatar" class="discord-avatar">
                        <span>{{ current_user.username }}</span>
                    </div>
                    {% endif %}
                    <a href="{{ url_for('disconnect_discord') }}" class="discord-btn" style="margin-top: 15px; background-color: #F04747;">
                        <i class="fas fa-unlink"></i> Отключить Discord
                    </a>
                </div>
            {% else %}
                <a href="{{ url_for('auth_discord') }}" class="discord-btn">
                    <i class="fab fa-discord"></i> Подключить Discord
                </a>
                
                {% if error %}
                <div class="discord-status error">
                    <p><i class="fas fa-exclamation-triangle"></i> {{ error }}</p>
                </div>
                {% endif %}
            {% endif %}
        </div>
        
        <a href="{{ url_for('profile.view_profile') }}" class="back-link">⬅ Вернуться в профиль</a>
    </div>
</div>
{% endblock %} 