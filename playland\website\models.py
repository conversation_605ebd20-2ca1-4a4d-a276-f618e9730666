#!/usr/bin/env python
# -*- coding: utf-8 -*-

import uuid
import json
import secrets
import pyotp
import qrcode
import io
import base64
from datetime import datetime, timedelta, timezone
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash

db = SQLAlchemy()




class User(db.Model, UserMixin):
    """Модель пользователя для системы авторизации"""
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, index=True)
    email = db.Column(db.String(120), unique=True, index=True)
    password_hash = db.Column(db.String(128))
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    last_seen = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    is_admin = db.Column(db.Boolean, default=False)
    is_activated = db.Column(db.Boolean, default=False)
    activation_token = db.Column(db.String(64), nullable=True)
    nickname = db.Column(db.String(64), index=True)
    about = db.Column(db.Text)

    # Новые поля для профиля
    bio = db.Column(db.Text, nullable=True)
    minecraft_experience = db.Column(db.String(50), nullable=True)  # Например: Новичок, Продвинутый, Эксперт
    interests = db.Column(db.Text, nullable=True)  # JSON-строка с интересами
    last_seen_on_server = db.Column(db.DateTime, nullable=True)
    achievements = db.Column(db.Text, nullable=True)  # JSON-строка с достижениями
    social_links = db.Column(db.Text, nullable=True)  # JSON-строка с соц. сетями

    # Новые поля для социальных сетей
    discord = db.Column(db.String(64))
    discord_id = db.Column(db.String(64), unique=True, nullable=True)
    discord_tag = db.Column(db.String(64))  # Для совместимости с Telegram ботом
    telegram = db.Column(db.String(64))
    telegram_id = db.Column(db.Integer, unique=True, nullable=True)  # Изменено на Integer для совместимости
    avatar_url = db.Column(db.String(256))

    # Дополнительные поля для совместимости с Telegram ботом
    first_name = db.Column(db.String(64))
    last_name = db.Column(db.String(64))
    registration_date = db.Column(db.String(64))  # Для совместимости с форматом Telegram бота
    last_activity = db.Column(db.String(64))  # Для совместимости с форматом Telegram бота
    is_banned = db.Column(db.Boolean, default=False)
    ban_reason = db.Column(db.Text)

    # Поля для игровых данных
    status = db.Column(db.String(20), default='pending')  # pending, approved, rejected
    balance = db.Column(db.Float, default=0.0)

    # Отношения
    transactions = db.relationship('Transaction', backref='user', lazy='dynamic')

    # Поля для системы вайтлиста
    is_whitelisted = db.Column(db.Boolean, default=False)
    whitelist_status = db.Column(db.String(20), default='pending')  # pending, approved, rejected

    # Поля для системы монетизации
    premium_until = db.Column(db.DateTime, nullable=True)  # Срок действия премиум-статуса

    # Поля для безопасности и аутентификации
    failed_login_attempts = db.Column(db.Integer, default=0)
    last_failed_login = db.Column(db.DateTime, nullable=True)
    account_locked_until = db.Column(db.DateTime, nullable=True)
    password_reset_token = db.Column(db.String(128), nullable=True)
    password_reset_expires = db.Column(db.DateTime, nullable=True)
    email_verification_token = db.Column(db.String(128), nullable=True)
    email_verified = db.Column(db.Boolean, default=False)

    # Двухфакторная аутентификация
    two_factor_enabled = db.Column(db.Boolean, default=False)
    two_factor_secret = db.Column(db.String(32), nullable=True)
    backup_codes = db.Column(db.Text, nullable=True)  # JSON список резервных кодов

    # Управление сессиями
    session_token = db.Column(db.String(128), nullable=True)
    session_expires = db.Column(db.DateTime, nullable=True)

    # Отношения с другими таблицами с явным указанием foreign_keys
    applications = db.relationship('Application', foreign_keys='Application.user_id', backref='user', lazy=True)
    # transactions мы определяем в классе Transaction с backref
    whitelist_requests = db.relationship('WhitelistRequest', foreign_keys='WhitelistRequest.user_id', backref='user', lazy=True)

    # Дополнительные отношения для разных ролей пользователя
    reviewed_applications = db.relationship('Application', foreign_keys='Application.reviewed_by', backref='reviewer', lazy=True)
    approved_whitelist = db.relationship('WhitelistRequest', foreign_keys='WhitelistRequest.processed_by', backref='whitelist_processor', lazy=True)

    # Связи с новыми таблицами безопасности
    trusted_devices = db.relationship('TrustedDevice', backref='user', lazy='dynamic', cascade='all, delete-orphan')
    login_history = db.relationship('LoginHistory', backref='user', lazy='dynamic', cascade='all, delete-orphan')

    @property
    def password(self):
        raise AttributeError('password is not a readable attribute')

    @password.setter
    def password(self, password):
        self.password_hash = generate_password_hash(password)

    def verify_password(self, password):
        return check_password_hash(self.password_hash, password)

    def check_password(self, password):
        """Проверяет пароль пользователя (алиас для verify_password)"""
        return self.verify_password(password)

    def is_premium(self):
        """Проверяет, является ли пользователь премиум-пользователем"""
        if self.premium_until is None:
            return False
        return self.premium_until > datetime.now(timezone.utc)

    # Методы безопасности и аутентификации
    def is_account_locked(self):
        """Проверяет, заблокирован ли аккаунт"""
        if self.account_locked_until is None:
            return False
        return datetime.now(timezone.utc) < self.account_locked_until

    def lock_account(self, duration_minutes=30):
        """Блокирует аккаунт на указанное время"""
        self.account_locked_until = datetime.now(timezone.utc) + timedelta(minutes=duration_minutes)
        self.failed_login_attempts = 0

    def unlock_account(self):
        """Разблокирует аккаунт"""
        self.account_locked_until = None
        self.failed_login_attempts = 0

    def increment_failed_login(self):
        """Увеличивает счетчик неудачных попыток входа"""
        self.failed_login_attempts += 1
        self.last_failed_login = datetime.now(timezone.utc)

        # Блокируем аккаунт после 5 неудачных попыток
        if self.failed_login_attempts >= 5:
            self.lock_account(30)  # Блокируем на 30 минут

    def reset_failed_login_attempts(self):
        """Сбрасывает счетчик неудачных попыток входа"""
        self.failed_login_attempts = 0
        self.last_failed_login = None

    def generate_password_reset_token(self):
        """Генерирует токен для сброса пароля"""
        self.password_reset_token = secrets.token_urlsafe(32)
        self.password_reset_expires = datetime.now(timezone.utc) + timedelta(hours=1)
        return self.password_reset_token

    def verify_password_reset_token(self, token):
        """Проверяет токен сброса пароля"""
        if not self.password_reset_token or not self.password_reset_expires:
            return False
        if datetime.now(timezone.utc) > self.password_reset_expires:
            return False
        return self.password_reset_token == token

    def clear_password_reset_token(self):
        """Очищает токен сброса пароля"""
        self.password_reset_token = None
        self.password_reset_expires = None

    def generate_email_verification_token(self):
        """Генерирует токен для подтверждения email"""
        self.email_verification_token = secrets.token_urlsafe(32)
        return self.email_verification_token

    def verify_email_token(self, token):
        """Проверяет токен подтверждения email"""
        if not self.email_verification_token:
            return False
        return self.email_verification_token == token

    def confirm_email(self):
        """Подтверждает email пользователя"""
        self.email_verified = True
        self.email_verification_token = None

    # Методы двухфакторной аутентификации
    def enable_two_factor(self):
        """Включает двухфакторную аутентификацию"""
        if not self.two_factor_secret:
            self.two_factor_secret = pyotp.random_base32()
        self.two_factor_enabled = True
        return self.two_factor_secret

    def disable_two_factor(self):
        """Отключает двухфакторную аутентификацию"""
        self.two_factor_enabled = False
        self.two_factor_secret = None
        self.backup_codes = None

    def get_totp_uri(self, app_name="PlayLand"):
        """Получает URI для QR-кода TOTP"""
        if not self.two_factor_secret:
            return None
        return pyotp.totp.TOTP(self.two_factor_secret).provisioning_uri(
            name=self.email,
            issuer_name=app_name
        )

    def verify_totp(self, token):
        """Проверяет TOTP токен"""
        if not self.two_factor_enabled or not self.two_factor_secret:
            return False
        totp = pyotp.TOTP(self.two_factor_secret)
        return totp.verify(token, valid_window=1)

    def generate_backup_codes(self, count=10):
        """Генерирует резервные коды для 2FA"""
        codes = [secrets.token_hex(4).upper() for _ in range(count)]
        self.backup_codes = json.dumps(codes)
        return codes

    def get_backup_codes(self):
        """Получает список резервных кодов"""
        if not self.backup_codes:
            return []
        return json.loads(self.backup_codes)

    def verify_backup_code(self, code):
        """Проверяет и использует резервный код"""
        codes = self.get_backup_codes()
        if code.upper() in codes:
            codes.remove(code.upper())
            self.backup_codes = json.dumps(codes)
            return True
        return False

    def generate_qr_code(self, app_name="PlayLand"):
        """Генерирует QR-код для настройки 2FA"""
        uri = self.get_totp_uri(app_name)
        if not uri:
            return None

        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(uri)
        qr.make(fit=True)

        img = qr.make_image(fill_color="black", back_color="white")
        img_buffer = io.BytesIO()
        img.save(img_buffer, format='PNG')
        img_buffer.seek(0)

        return base64.b64encode(img_buffer.getvalue()).decode()

    def generate_activation_token(self):
        """Генерирует токен активации для пользователя"""
        self.activation_token = str(uuid.uuid4())
        return self.activation_token

    def activate(self):
        """Активирует аккаунт пользователя"""
        self.is_activated = True
        self.activation_token = None
        return self

    def activate_account(self):
        self.is_activated = True
        self.activation_token = None

    # Методы для работы с JSON-полями
    def get_social_links(self):
        """Получение словаря социальных сетей пользователя"""
        if not self.social_links:
            return {}
        try:
            return json.loads(self.social_links)
        except json.JSONDecodeError:
            return {}

    def set_social_links(self, links_dict):
        """Сохранение словаря социальных сетей пользователя"""
        if not isinstance(links_dict, dict):
            raise ValueError("links_dict должен быть словарем")
        self.social_links = json.dumps(links_dict)

    def add_social_link(self, platform, url):
        """Добавление или обновление ссылки на социальную сеть"""
        links = self.get_social_links()
        links[platform] = url
        self.set_social_links(links)

    def remove_social_link(self, platform):
        """Удаление ссылки на социальную сеть"""
        links = self.get_social_links()
        if platform in links:
            del links[platform]
            self.set_social_links(links)
            return True
        return False

    def get_interests(self):
        """Получение списка интересов пользователя"""
        if not self.interests:
            return []
        try:
            return json.loads(self.interests)
        except json.JSONDecodeError:
            return []

    def set_interests(self, interests_list):
        """Сохранение списка интересов пользователя"""
        if not isinstance(interests_list, list):
            raise ValueError("interests_list должен быть списком")
        self.interests = json.dumps(interests_list)

    def get_achievements(self):
        """Получение словаря достижений пользователя"""
        if not self.achievements:
            return {}
        try:
            return json.loads(self.achievements)
        except json.JSONDecodeError:
            return {}

    def set_achievements(self, achievements_dict):
        """Сохранение словаря достижений пользователя"""
        if not isinstance(achievements_dict, dict):
            raise ValueError("achievements_dict должен быть словарем")
        self.achievements = json.dumps(achievements_dict)

    def add_achievement(self, achievement_id, achievement_data):
        """Добавление достижения пользователю"""
        achievements = self.get_achievements()
        achievements[achievement_id] = achievement_data
        self.set_achievements(achievements)

    # Методы для работы с доверенными устройствами
    def is_device_trusted(self, device_fingerprint):
        """Проверяет, является ли устройство доверенным"""
        device = self.trusted_devices.filter_by(
            device_fingerprint=device_fingerprint,
            is_active=True
        ).first()

        if not device:
            return False

        if device.is_expired():
            device.revoke()
            db.session.commit()
            return False

        return True

    def add_trusted_device(self, device_fingerprint, device_name=None, ip_address=None, user_agent=None, location=None):
        """Добавляет устройство в список доверенных"""
        # Проверяем, не существует ли уже такое устройство
        existing_device = self.trusted_devices.filter_by(device_fingerprint=device_fingerprint).first()

        if existing_device:
            # Обновляем существующее устройство
            existing_device.is_active = True
            existing_device.extend_trust()
            existing_device.device_name = device_name or existing_device.device_name
            existing_device.ip_address = ip_address or existing_device.ip_address
            existing_device.location = location or existing_device.location
            return existing_device
        else:
            # Создаем новое доверенное устройство
            device = TrustedDevice(
                user_id=self.id,
                device_fingerprint=device_fingerprint,
                device_name=device_name,
                ip_address=ip_address,
                user_agent=user_agent,
                location=location
            )
            db.session.add(device)
            return device

    def revoke_trusted_device(self, device_fingerprint):
        """Отзывает доверие к устройству"""
        device = self.trusted_devices.filter_by(device_fingerprint=device_fingerprint).first()
        if device:
            device.revoke()
            return True
        return False

    def get_active_trusted_devices(self):
        """Получает список активных доверенных устройств"""
        return self.trusted_devices.filter_by(is_active=True).all()

    def cleanup_expired_devices(self):
        """Удаляет истекшие доверенные устройства"""
        expired_devices = self.trusted_devices.filter(
            TrustedDevice.expires_at < datetime.now(timezone.utc)
        ).all()

        for device in expired_devices:
            device.revoke()

        return len(expired_devices)

    # Методы для работы с историей входов
    def add_login_record(self, ip_address=None, user_agent=None, location=None,
                        device_fingerprint=None, login_method='password', success=True, failure_reason=None):
        """Добавляет запись в историю входов"""
        login_record = LoginHistory(
            user_id=self.id,
            ip_address=ip_address,
            user_agent=user_agent,
            location=location,
            device_fingerprint=device_fingerprint,
            login_method=login_method,
            success=success,
            failure_reason=failure_reason
        )
        db.session.add(login_record)
        return login_record

    def get_recent_logins(self, limit=10):
        """Получает последние входы пользователя"""
        return self.login_history.filter_by(success=True).order_by(
            LoginHistory.timestamp.desc()
        ).limit(limit).all()

    def get_failed_logins(self, hours=24):
        """Получает неудачные попытки входа за указанный период"""
        since = datetime.now(timezone.utc) - timedelta(hours=hours)
        return self.login_history.filter(
            LoginHistory.success == False,
            LoginHistory.timestamp >= since
        ).order_by(LoginHistory.timestamp.desc()).all()

    # Методы для работы с Telegram интеграцией
    def link_telegram(self, telegram_id, telegram_username=None, first_name=None, last_name=None):
        """Связывает аккаунт с Telegram"""
        self.telegram_id = telegram_id
        if telegram_username:
            self.telegram = telegram_username
        if first_name:
            self.first_name = first_name
        if last_name:
            self.last_name = last_name

        # Обновляем время активности
        self.last_activity = datetime.now(timezone.utc).isoformat()
        self.last_seen = datetime.now(timezone.utc)

        return True

    def unlink_telegram(self):
        """Отвязывает аккаунт от Telegram"""
        self.telegram_id = None
        self.telegram = None
        return True

    def is_telegram_linked(self):
        """Проверяет, связан ли аккаунт с Telegram"""
        return self.telegram_id is not None

    def get_display_name(self):
        """Получает отображаемое имя пользователя"""
        if self.nickname:
            return self.nickname
        elif self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        elif self.first_name:
            return self.first_name
        elif self.username:
            return self.username
        else:
            return f"User {self.id}"

    def sync_with_telegram_data(self, telegram_data):
        """Синхронизирует данные с Telegram ботом"""
        if telegram_data.get('username'):
            self.telegram = telegram_data['username']
        if telegram_data.get('first_name'):
            self.first_name = telegram_data['first_name']
        if telegram_data.get('last_name'):
            self.last_name = telegram_data['last_name']
        if telegram_data.get('last_activity'):
            self.last_activity = telegram_data['last_activity']

        # Обновляем время последней активности
        self.last_seen = datetime.now(timezone.utc)

        return True

    @classmethod
    def find_by_telegram_id(cls, telegram_id):
        """Находит пользователя по Telegram ID"""
        return cls.query.filter_by(telegram_id=telegram_id).first()

    @classmethod
    def find_by_username_or_email(cls, identifier):
        """Находит пользователя по имени пользователя или email"""
        return cls.query.filter(
            (cls.username == identifier) | (cls.email == identifier)
        ).first()

    def __repr__(self):
        return f'<User {self.username}>'

class TrustedDevice(db.Model):
    """Модель доверенного устройства для 2FA"""
    __tablename__ = 'trusted_devices'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    device_fingerprint = db.Column(db.String(128), nullable=False)  # Уникальный отпечаток устройства
    device_name = db.Column(db.String(100))  # Название устройства (например, "Chrome на Windows")
    ip_address = db.Column(db.String(50))
    user_agent = db.Column(db.Text)
    location = db.Column(db.String(100))  # Геолокация (город, страна)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    last_used = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    expires_at = db.Column(db.DateTime)  # Срок действия доверия (например, 30 дней)
    is_active = db.Column(db.Boolean, default=True)

    def __init__(self, user_id, device_fingerprint, device_name=None, ip_address=None, user_agent=None, location=None):
        self.user_id = user_id
        self.device_fingerprint = device_fingerprint
        self.device_name = device_name
        self.ip_address = ip_address
        self.user_agent = user_agent
        self.location = location
        self.expires_at = datetime.now(timezone.utc) + timedelta(days=30)

    def is_expired(self):
        """Проверяет, истек ли срок доверия устройства"""
        return datetime.now(timezone.utc) > self.expires_at

    def extend_trust(self, days=30):
        """Продлевает доверие к устройству"""
        self.expires_at = datetime.now(timezone.utc) + timedelta(days=days)
        self.last_used = datetime.now(timezone.utc)

    def revoke(self):
        """Отзывает доверие к устройству"""
        self.is_active = False

    def __repr__(self):
        return f'<TrustedDevice {self.id} for user {self.user_id}>'

class LoginHistory(db.Model):
    """Модель истории входов пользователя"""
    __tablename__ = 'login_history'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    ip_address = db.Column(db.String(50))
    user_agent = db.Column(db.Text)
    location = db.Column(db.String(100))  # Геолокация
    device_fingerprint = db.Column(db.String(128))
    login_method = db.Column(db.String(50))  # password, 2fa, telegram, discord
    success = db.Column(db.Boolean, default=True)
    failure_reason = db.Column(db.String(100))  # Причина неудачи (если success=False)
    timestamp = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    session_duration = db.Column(db.Integer)  # Длительность сессии в секундах

    def __init__(self, user_id, ip_address=None, user_agent=None, location=None,
                 device_fingerprint=None, login_method='password', success=True, failure_reason=None):
        self.user_id = user_id
        self.ip_address = ip_address
        self.user_agent = user_agent
        self.location = location
        self.device_fingerprint = device_fingerprint
        self.login_method = login_method
        self.success = success
        self.failure_reason = failure_reason

    def __repr__(self):
        return f'<LoginHistory {self.id} for user {self.user_id}>'

class Role(db.Model):
    """Модель роли пользователя"""
    __tablename__ = 'roles'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), unique=True)
    description = db.Column(db.String(255))

    def __repr__(self):
        return f'<Role {self.name}>'

# Таблица связи пользователей и ролей (многие ко многим)
user_roles = db.Table('user_roles',
    db.Column('user_id', db.Integer, db.ForeignKey('users.id'), primary_key=True),
    db.Column('role_id', db.Integer, db.ForeignKey('roles.id'), primary_key=True)
)

class Application(db.Model):
    """Модель заявки на сервер"""
    __tablename__ = 'applications'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    minecraft_username = db.Column(db.String(50), nullable=False)
    age = db.Column(db.Integer, nullable=False)
    reason = db.Column(db.Text, nullable=False)
    status = db.Column(db.String(20), default='pending')  # pending, approved, rejected
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    reviewed_at = db.Column(db.DateTime)
    reviewed_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    review_comment = db.Column(db.Text)

    # Информация для аудита
    approved_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    approved_at = db.Column(db.DateTime, nullable=True)
    rejected_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    rejected_at = db.Column(db.DateTime, nullable=True)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)

    # Отношения с другими таблицами - удаляем дублирующиеся определения backref
    approver = db.relationship('User', foreign_keys=[approved_by], lazy=True)
    rejecter = db.relationship('User', foreign_keys=[rejected_by], lazy=True)
    creator = db.relationship('User', foreign_keys=[created_by], lazy=True)

    # Поля для интеграции с Telegram ботом
    telegram_user_id = db.Column(db.Integer, nullable=True, index=True)  # ID пользователя в Telegram
    telegram_id = db.Column(db.String(50), nullable=True, index=True)  # Для совместимости (строковый ID)
    nickname = db.Column(db.String(50), nullable=True)  # Алиас для minecraft_username
    discord = db.Column(db.String(50))
    discord_tag = db.Column(db.String(50), nullable=True)  # Для совместимости с Telegram ботом
    rejection_reason = db.Column(db.Text, nullable=True)
    telegram_username = db.Column(db.String(50), nullable=True)
    first_name = db.Column(db.String(50), nullable=True)
    last_name = db.Column(db.String(50), nullable=True)
    timestamp = db.Column(db.String(64), nullable=True)  # Для совместимости с Telegram ботом
    processed_at = db.Column(db.DateTime, nullable=True)  # Для совместимости
    processed_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)  # Для совместимости
    source = db.Column(db.String(20), default='web')  # web, telegram

    def __init__(self, user_id, minecraft_username, age, reason, created_by=None):
        self.user_id = user_id
        self.minecraft_username = minecraft_username
        self.age = age
        self.reason = reason
        self.created_by = created_by

    def approve(self, admin_id):
        """Одобрение заявки"""
        self.status = 'approved'
        self.approved_by = admin_id
        self.approved_at = datetime.now(timezone.utc)
        return self

    def reject(self, admin_id):
        """Отклонение заявки"""
        self.status = 'rejected'
        self.rejected_by = admin_id
        self.rejected_at = datetime.now(timezone.utc)
        return self

    def to_dict(self):
        """Преобразование в словарь для API"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'telegram_user_id': self.telegram_user_id,
            'minecraft_username': self.minecraft_username,
            'minecraft_nickname': self.nickname or self.minecraft_username,  # Для совместимости
            'discord': self.discord,
            'discord_tag': self.discord_tag,
            'age': self.age,
            'reason': self.reason,
            'status': self.status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'timestamp': self.timestamp,  # Для совместимости с Telegram ботом
            'reviewed_at': self.reviewed_at.isoformat() if self.reviewed_at else None,
            'processed_at': self.processed_at.isoformat() if self.processed_at else None,
            'reviewed_by': self.reviewed_by,
            'processed_by': self.processed_by,
            'review_comment': self.review_comment,
            'rejection_reason': self.rejection_reason,
            'approved_by': self.approved_by,
            'approved_at': self.approved_at.isoformat() if self.approved_at else None,
            'rejected_by': self.rejected_by,
            'rejected_at': self.rejected_at.isoformat() if self.rejected_at else None,
            'source': self.source,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'telegram_username': self.telegram_username
        }

    @classmethod
    def create_from_telegram(cls, telegram_user_id, minecraft_nickname, discord_tag=None, reason=None):
        """Создает заявку из Telegram бота"""
        application = cls(
            user_id=None,  # Может быть связан позже
            minecraft_username=minecraft_nickname,
            age=None,  # Может быть заполнен позже
            reason=reason or "Заявка через Telegram бота"
        )
        application.telegram_user_id = telegram_user_id
        application.nickname = minecraft_nickname
        application.discord_tag = discord_tag
        application.source = 'telegram'
        application.timestamp = datetime.now(timezone.utc).isoformat()
        return application

    def link_to_user(self, user):
        """Связывает заявку с пользователем веб-сайта"""
        self.user_id = user.id
        if self.telegram_user_id and not user.telegram_id:
            user.telegram_id = self.telegram_user_id
        return True

    def get_display_name(self):
        """Получает отображаемое имя заявителя"""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        elif self.first_name:
            return self.first_name
        elif self.telegram_username:
            return f"@{self.telegram_username}"
        elif self.minecraft_username:
            return self.minecraft_username
        else:
            return f"Application {self.id}"

    def __repr__(self):
        return f'<Application {self.id} by {self.minecraft_username}>'

class AuditLog(db.Model):
    """Модель для аудита действий на сайте"""
    __tablename__ = 'audit_logs'

    id = db.Column(db.Integer, primary_key=True)
    timestamp = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    ip_address = db.Column(db.String(50))
    action = db.Column(db.String(100), nullable=False)
    details = db.Column(db.Text)

    # Отношения с другими таблицами с явным указанием foreign_keys
    user = db.relationship('User', foreign_keys=[user_id], backref='audit_logs', lazy=True)

    def __init__(self, action, user_id=None, ip_address=None, details=None):
        self.action = action
        self.user_id = user_id
        self.ip_address = ip_address
        self.details = details

    def __repr__(self):
        return f'<AuditLog {self.id} - {self.action}>'

class Transaction(db.Model):
    __tablename__ = 'transactions'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    amount = db.Column(db.Float, nullable=False)
    description = db.Column(db.String(256))
    timestamp = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    status = db.Column(db.String(20), default='completed')  # completed, pending, canceled
    external_id = db.Column(db.String(64), nullable=True)  # ID транзакции во внешней системе

    def __repr__(self):
        return f'<Transaction {self.id}: {self.amount} for user {self.user_id}>'

class WhitelistRequest(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    minecraft_username = db.Column(db.String(50), nullable=False)
    status = db.Column(db.String(20), default='pending')  # pending, approved, rejected
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    processed_at = db.Column(db.DateTime)
    processed_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)

    # Изменяем имя отношения с processor на processor_user, чтобы избежать конфликта
    processor_user = db.relationship('User', foreign_keys=[processed_by], lazy=True, overlaps="approved_whitelist,whitelist_processor")

    def __repr__(self):
        return f'<WhitelistRequest {self.id} for {self.minecraft_username}>'

class ServerInfo(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    address = db.Column(db.String(100), nullable=False)
    port = db.Column(db.Integer, default=25565)
    version = db.Column(db.String(20))
    status = db.Column(db.String(20), default='offline')  # online, offline, maintenance
    players_online = db.Column(db.Integer, default=0)
    max_players = db.Column(db.Integer, default=0)
    last_updated = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

    def __repr__(self):
        return f'<ServerInfo {self.name}>'

class Ticket(db.Model):
    """Модель тикета для системы поддержки"""
    __tablename__ = 'tickets'

    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    content = db.Column(db.Text, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    status = db.Column(db.String(20), default='open')  # open, closed, answered
    application_type = db.Column(db.String(50), nullable=True)  # server, team, whitelist, etc.
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # Отношения с другими таблицами
    user = db.relationship('User', foreign_keys=[user_id], backref='tickets', lazy=True)
    messages = db.relationship('TicketMessage', backref='ticket', lazy=True, cascade='all, delete-orphan')

    def __init__(self, title, content, user_id, application_type=None):
        self.title = title
        self.content = content
        self.user_id = user_id
        self.application_type = application_type

    def to_dict(self):
        """Преобразование в словарь для API"""
        return {
            'id': self.id,
            'title': self.title,
            'content': self.content,
            'user_id': self.user_id,
            'status': self.status,
            'application_type': self.application_type,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'messages_count': len(self.messages) if self.messages else 0
        }

    def __repr__(self):
        return f'<Ticket {self.id} - {self.title}>'

class TicketMessage(db.Model):
    """Модель сообщения в тикете"""
    __tablename__ = 'ticket_messages'

    id = db.Column(db.Integer, primary_key=True)
    ticket_id = db.Column(db.Integer, db.ForeignKey('tickets.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    content = db.Column(db.Text, nullable=False)
    is_admin_reply = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

    # Поля для синхронизации между сайтом и Telegram
    source_platform = db.Column(db.String(20), default='website')  # website, telegram
    telegram_message_id = db.Column(db.Integer, nullable=True)  # ID сообщения в Telegram
    is_synced = db.Column(db.Boolean, default=False)  # Синхронизировано ли с другой платформой
    sync_error = db.Column(db.Text, nullable=True)  # Ошибка синхронизации если есть

    # Отношения с другими таблицами
    user = db.relationship('User', foreign_keys=[user_id], backref='ticket_messages', lazy=True)

    def __init__(self, ticket_id, user_id, content, is_admin_reply=False):
        self.ticket_id = ticket_id
        self.user_id = user_id
        self.content = content
        self.is_admin_reply = is_admin_reply

    def to_dict(self):
        """Преобразование в словарь для API"""
        return {
            'id': self.id,
            'ticket_id': self.ticket_id,
            'user_id': self.user_id,
            'content': self.content,
            'is_admin_reply': self.is_admin_reply,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'username': self.user.username if self.user else 'Unknown'
        }

    def __repr__(self):
        return f'<TicketMessage {self.id} for Ticket {self.ticket_id}>'

class TeamApplication(db.Model):
    """Модель заявки на вступление в команду"""
    __tablename__ = 'team_applications'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    minecraft_nickname = db.Column(db.String(50), nullable=False)
    discord_tag = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(100), nullable=False)
    age = db.Column(db.Integer, nullable=False)
    about_yourself = db.Column(db.Text, nullable=False)
    desired_position = db.Column(db.String(50), nullable=False)
    custom_position = db.Column(db.String(100), nullable=True)
    experience = db.Column(db.Text, nullable=False)
    motivation = db.Column(db.Text, nullable=False)
    ideas = db.Column(db.Text, nullable=True)
    status = db.Column(db.String(20), default='pending')  # pending, approved, rejected, interview
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    reviewed_at = db.Column(db.DateTime, nullable=True)
    reviewed_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    ticket_id = db.Column(db.Integer, db.ForeignKey('tickets.id'), nullable=True)

    # Отношения с другими таблицами
    user = db.relationship('User', foreign_keys=[user_id], backref='team_applications', lazy=True)
    reviewer = db.relationship('User', foreign_keys=[reviewed_by], lazy=True)
    ticket = db.relationship('Ticket', foreign_keys=[ticket_id], lazy=True)

    def __init__(self, user_id, minecraft_nickname, discord_tag, email, age,
                 about_yourself, desired_position, experience, motivation,
                 custom_position=None, ideas=None):
        self.user_id = user_id
        self.minecraft_nickname = minecraft_nickname
        self.discord_tag = discord_tag
        self.email = email
        self.age = age
        self.about_yourself = about_yourself
        self.desired_position = desired_position
        self.custom_position = custom_position
        self.experience = experience
        self.motivation = motivation
        self.ideas = ideas

    def to_dict(self):
        """Преобразование в словарь для API"""
        position = self.custom_position if self.desired_position == 'other' else self.desired_position
        return {
            'id': self.id,
            'user_id': self.user_id,
            'minecraft_nickname': self.minecraft_nickname,
            'discord_tag': self.discord_tag,
            'email': self.email,
            'age': self.age,
            'about_yourself': self.about_yourself,
            'desired_position': position,
            'experience': self.experience,
            'motivation': self.motivation,
            'ideas': self.ideas,
            'status': self.status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'reviewed_at': self.reviewed_at.isoformat() if self.reviewed_at else None,
            'reviewed_by': self.reviewed_by,
            'ticket_id': self.ticket_id
        }

    def __repr__(self):
        return f'<TeamApplication {self.id} - {self.minecraft_nickname}>'

class News(db.Model):
    """Модель новостей для главной страницы"""
    __tablename__ = 'news'

    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    excerpt = db.Column(db.Text, nullable=False)  # Краткое описание
    content = db.Column(db.Text, nullable=True)   # Полный текст (опционально)
    image_url = db.Column(db.String(255), nullable=True)  # URL изображения
    author_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    is_published = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # Связи
    author = db.relationship('User', backref='news_articles', lazy=True)

    def __init__(self, title, excerpt, author_id, content=None, image_url=None):
        self.title = title
        self.excerpt = excerpt
        self.content = content
        self.image_url = image_url
        self.author_id = author_id

    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'excerpt': self.excerpt,
            'content': self.content,
            'image_url': self.image_url,
            'author': self.author.username if self.author else 'Неизвестно',
            'is_published': self.is_published,
            'created_at': self.created_at.strftime('%d.%m.%Y'),
            'updated_at': self.updated_at.strftime('%d.%m.%Y %H:%M')
        }

    def __repr__(self):
        return f'<News {self.id} - {self.title}>'

class ServerEvent(db.Model):
    """Модель для хранения событий сервера"""
    __tablename__ = 'server_events'

    id = db.Column(db.Integer, primary_key=True)
    event_type = db.Column(db.String(50), nullable=False)
    content = db.Column(db.Text, nullable=False)
    event_metadata = db.Column(db.Text)  # JSON строка с метаданными (переименовано из metadata)
    timestamp = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

    def __repr__(self):
        return f'<ServerEvent {self.event_type} {self.timestamp}>'

    def to_dict(self):
        """Преобразует событие в словарь для API"""
        try:
            metadata_dict = json.loads(self.event_metadata) if self.event_metadata else {}
        except:
            metadata_dict = {}

        return {
            'id': self.id,
            'event_type': self.event_type,
            'content': self.content,
            'metadata': metadata_dict,
            'timestamp': self.timestamp.isoformat()
        }
