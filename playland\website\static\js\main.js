/**
 * PLAYLAND Minecraft Server - Основной JavaScript файл
 * Включает функциональность сайта и интерактивные элементы
 */

// Функция для инициализации анимации перехода между страницами
function initPageTransitions() {
    // Создаем оверлей для анимации перехода
    if (!document.querySelector('.page-transition-overlay')) {
        const overlay = document.createElement('div');
        overlay.className = 'page-transition-overlay';
        
        const image = document.createElement('img');
        image.className = 'page-transition-image';
        image.src = '/static/images/playland_logo.png'; // Используем логотип для анимации
        image.alt = 'Загрузка';
        
        overlay.appendChild(image);
        document.body.appendChild(overlay);
    }
    
    // Перехватываем клики по всем внутренним ссылкам
    document.addEventListener('click', function(event) {
        const target = event.target.closest('a');
        
        if (target && target.getAttribute('href') && 
            target.getAttribute('href').indexOf('#') !== 0 && // не якорные ссылки
            target.getAttribute('href').indexOf('javascript:') !== 0 && // не javascript ссылки 
            target.getAttribute('target') !== '_blank' && // не новые окна
            target.getAttribute('href').indexOf(':') === -1) { // не внешние ссылки
            
            event.preventDefault();
            const href = target.getAttribute('href');
            
            // Показываем экран загрузки
            const overlay = document.querySelector('.page-transition-overlay');
            overlay.style.left = '0';
            
            // Переходим на новую страницу через небольшую задержку
            setTimeout(() => {
                window.location.href = href;
            }, 500);
        }
    });
}

// Обработка мобильных устройств
function handleMobileDevices() {
    // Исправление 100vh на мобильных устройствах
    function setVhVariable() {
        let vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);
    }
    
    // Установка при загрузке и при изменении размера
    setVhVariable();
    window.addEventListener('resize', setVhVariable);
    window.addEventListener('orientationchange', setVhVariable);
    
    // Убираем 300ms задержку касания на мобильных
    document.addEventListener('touchstart', function() {}, {passive: true});
    
    // Закрываем мобильное меню при прокрутке
    let lastScrollTop = 0;
    const navbar = document.querySelector('.header');
    const mobileMenu = document.querySelector('.nav-links');
    const burger = document.querySelector('.burger');
    
    window.addEventListener('scroll', function() {
        let scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        // Если прокрутили больше 50px и меню открыто, закрываем его
        if (Math.abs(lastScrollTop - scrollTop) > 50 && mobileMenu && mobileMenu.classList.contains('active')) {
            mobileMenu.classList.remove('active');
            if (burger) burger.classList.remove('active');
            document.body.classList.remove('menu-open');
        }
        
        // Показываем/скрываем навбар при прокрутке вниз/вверх
        if (scrollTop > 100) {
            if (scrollTop > lastScrollTop && !mobileMenu.classList.contains('active')) {
                // Прокрутка вниз - скрываем навбар
                navbar.style.transform = 'translateY(-60px)';
            } else {
                // Прокрутка вверх - показываем навбар
                navbar.style.transform = 'translateY(0)';
            }
            navbar.style.transition = 'transform 0.3s ease-in-out';
        } else {
            navbar.style.transform = 'translateY(0)';
        }
        
        lastScrollTop = scrollTop <= 0 ? 0 : scrollTop;
    }, {passive: true});
    
    // Активация скролла для iOS Safari
    document.addEventListener('gesturestart', function(e) {
        e.preventDefault();
    }, {passive: false});
}

// Кнопка прокрутки вверх
function initScrollTopButton() {
    // Создаем кнопку, если ее еще нет
    if (!document.querySelector('.scroll-top')) {
        const scrollTopBtn = document.createElement('div');
        scrollTopBtn.className = 'scroll-top';
        scrollTopBtn.id = 'scrollTopBtn';
        scrollTopBtn.innerHTML = '<i class="fas fa-arrow-up"></i>';
        document.body.appendChild(scrollTopBtn);
        
        // Показываем/скрываем кнопку при прокрутке
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                scrollTopBtn.classList.add('active');
            } else {
                scrollTopBtn.classList.remove('active');
            }
        }, {passive: true});
        
        // Прокрутка вверх при клике
        scrollTopBtn.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }
}

// Инициализация функций при загрузке страницы
document.addEventListener('DOMContentLoaded', function() {
    initPageTransitions();
    handleMobileDevices();
    initScrollTopButton();
    
    // Анимация при загрузке страницы
    document.querySelectorAll('.animate-fade-in, .animate-fade-in-up, .animate-fade-in-left, .animate-fade-in-right').forEach(function(element) {
        // Проверяем, находится ли элемент в области видимости
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.visibility = 'visible';
                    observer.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.1
        });
        
        element.style.visibility = 'hidden';
        observer.observe(element);
    });
    
    // Инициализация тостов (уведомлений)
    window.showToast = function(message, type = 'success', duration = 3000) {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type} show`;
        toast.innerHTML = `<i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation' : 'info'}-circle"></i> ${message}`;
        
        // Создаем контейнер для тостов, если его нет
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container';
            document.body.appendChild(toastContainer);
        }
        
        toastContainer.appendChild(toast);
        
        // Автоматически удаляем через указанное время
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                toast.remove();
                if (toastContainer.children.length === 0) {
                    toastContainer.remove();
                }
            }, 300);
        }, duration);
    };
});

/**
 * Инициализация мобильной навигации
 */
function initNavigation() {
    const burger = document.querySelector('.burger');
    const nav = document.querySelector('.nav-links');
    const body = document.body;
    
    // Создаем оверлей для мобильного меню
    const overlay = document.createElement('div');
    overlay.classList.add('mobile-menu-overlay');
    document.body.appendChild(overlay);
    
    if (burger && nav) {
        // Функция для переключения меню
        function toggleMenu() {
            nav.classList.toggle('active');
            burger.classList.toggle('active');
            overlay.classList.toggle('active');
            
            // Блокируем прокрутку страницы при открытом меню
            if (nav.classList.contains('active')) {
                body.style.overflow = 'hidden';
            } else {
                body.style.overflow = '';
            }
        }
        
        burger.addEventListener('click', toggleMenu);
        
        // Закрываем меню при клике на оверлей
        overlay.addEventListener('click', toggleMenu);
        
        // Закрываем меню при клике на ссылку
        const navLinks = document.querySelectorAll('.nav-links a');
        navLinks.forEach(link => {
            link.addEventListener('click', () => {
                nav.classList.remove('active');
                burger.classList.remove('active');
                overlay.classList.remove('active');
                body.style.overflow = '';
            });
        });
        
        // Закрываем меню при изменении ориентации экрана или ресайзе
        window.addEventListener('resize', () => {
            if (window.innerWidth > 768 && nav.classList.contains('active')) {
                nav.classList.remove('active');
                burger.classList.remove('active');
                overlay.classList.remove('active');
                body.style.overflow = '';
            }
        });
    }
}

/**
 * Создание частиц для фонового эффекта
 */
function initParticles() {
    const container = document.getElementById('particles-js');
    if (!container) return;
    
    const particleCount = 70; // Увеличим количество частиц
    const colors = ['rgba(139, 195, 74, 0.6)', 'rgba(102, 187, 106, 0.6)', 'rgba(76, 175, 80, 0.5)']; // Зеленые оттенки

    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.classList.add('particle');
        
        const posX = Math.random() * 100;
        const delay = Math.random() * 20; // Задержка анимации
        const size = Math.random() * 8 + 3; // Размер от 3px до 11px
        const speed = 20 + Math.random() * 15; // Скорость анимации от 20с до 35с
        const randomColor = colors[Math.floor(Math.random() * colors.length)];

        particle.style.left = `${posX}%`;
        particle.style.width = `${size}px`;
        particle.style.height = `${size}px`;
        particle.style.backgroundColor = randomColor;
        particle.style.setProperty('--i', Math.random()); // Для вариативности движения по X в CSS
        
        // Добавляем легкое свечение
        particle.style.boxShadow = `0 0 ${Math.random() * 5 + 5}px ${randomColor.replace('0.6', '0.3').replace('0.5', '0.2')}`;
        
        particle.style.animationDuration = `${speed}s`;
        particle.style.animationDelay = `${delay}s`;
        
        container.appendChild(particle);
    }
}

/**
 * Копирование IP адреса сервера в буфер обмена
 */
function initCopyToClipboard() {
    const copyButtons = document.querySelectorAll('.copy-btn');
    
    copyButtons.forEach(button => {
        button.addEventListener('click', function() {
            const textToCopy = this.parentElement.textContent.split(' ')[0].trim();
            navigator.clipboard.writeText(textToCopy).then(() => {
                showToast('IP-адрес скопирован в буфер обмена!');
            }).catch(err => {
                console.error('Ошибка при копировании: ', err);
                showToast('Не удалось скопировать. Попробуйте вручную.', 'error');
            });
        });
    });
}

/**
 * Плавная прокрутка к секциям
 */
function initSmoothScroll() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            const targetId = this.getAttribute('href');
            if (targetId === '#') return;
            
            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                e.preventDefault();
                
                window.scrollTo({
                    top: targetElement.offsetTop - 80, // С учетом высоты шапки
                    behavior: 'smooth'
                });
            }
        });
    });
}

/**
 * Инициализация и автоматическое скрытие сообщений
 */
function initFlashMessages() {
    const flashMessages = document.querySelectorAll('.flash-message');
    
    flashMessages.forEach(message => {
        // Добавляем кнопку закрытия
        const closeBtn = document.createElement('button');
        closeBtn.innerHTML = '&times;';
        closeBtn.classList.add('flash-close');
        closeBtn.addEventListener('click', () => {
            message.style.opacity = '0';
            setTimeout(() => {
                message.remove();
            }, 300);
        });
        message.appendChild(closeBtn);
        
        // Автоматически скрываем через 5 секунд
        setTimeout(() => {
            message.style.opacity = '0';
            setTimeout(() => {
                message.remove();
            }, 300);
        }, 5000);
    });
}

/**
 * Получение статуса сервера Minecraft (имитация для демонстрации)
 */
function initServerStatus() {
    const statusElement = document.querySelector('.online-status');
    const playersElement = document.querySelector('.info-item:nth-child(4) p');
    
    if (!statusElement || !playersElement) return;
    
    // В реальном проекте здесь должен быть запрос к API сервера
    // Сейчас это просто имитация для демонстрации
    
    // Имитация запроса с интервалом 30 секунд
    updateServerStatus();
    setInterval(updateServerStatus, 30000);
    
    function updateServerStatus() {
        // Здесь должен быть настоящий запрос к API
        const isOnline = Math.random() > 0.1; // 90% вероятность онлайна для демонстрации
        const onlinePlayers = Math.floor(Math.random() * 50);
        const maxPlayers = 100;
        
        statusElement.textContent = isOnline ? 'Онлайн' : 'Офлайн';
        statusElement.style.color = isOnline ? 'var(--success-color)' : 'var(--error-color)';
        
        if (isOnline) {
            playersElement.textContent = `${onlinePlayers}/${maxPlayers}`;
        } else {
            playersElement.textContent = '0/0';
        }
    }
}

/**
 * Переключение темы (светлая/темная)
 */
function toggleTheme() {
    const html = document.documentElement;
    const themeSwitch = document.querySelector('.theme-switch');
    
    if (html.getAttribute('data-theme') === 'light') {
        html.setAttribute('data-theme', 'dark');
        localStorage.setItem('theme', 'dark');
        themeSwitch.classList.add('active');
    } else {
        html.setAttribute('data-theme', 'light');
        localStorage.setItem('theme', 'light');
        themeSwitch.classList.remove('active');
    }
}

/**
 * Анимация появления элементов при прокрутке страницы
 */
function initScrollAnimation() {
    const elements = document.querySelectorAll('.scroll-animate');
    
    if (elements.length === 0) return; // Выходим, если нет элементов для анимации

    // Базовый класс для всех анимируемых элементов Animate.css
    const baseAnimateClass = 'animate__animated';
    
    // Определяем, является ли устройство мобильным
    const isMobile = window.innerWidth <= 768;

    const observer = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const el = entry.target;
                
                // Получаем имя анимации из data-атрибута или используем значение по умолчанию
                // Для мобильных устройств может быть использована другая анимация
                let animationName;
                
                if (isMobile && el.dataset.mobileAnimation) {
                    // Если есть специальная анимация для мобильных и мы на мобильном
                    animationName = el.dataset.mobileAnimation;
                } else if (el.dataset.animation) {
                    // Если указана кастомная анимация
                    animationName = el.dataset.animation;
                } else {
                    // По умолчанию (можно задать разные для десктопа и мобильных)
                    animationName = isMobile ? 'animate__fadeIn' : 'animate__fadeInUp';
                }
                
                // Добавляем классы Animate.css
                el.classList.add(baseAnimateClass, animationName);
                
                // Опционально: добавляем класс 'is-visible' для стилизации
                el.classList.add('is-visible');

                // Задержка анимации на мобильных устройствах
                if (isMobile && el.dataset.mobileDelay) {
                    el.style.animationDelay = el.dataset.mobileDelay;
                } else if (el.dataset.delay) {
                    el.style.animationDelay = el.dataset.delay;
                }

                // Скорость анимации
                if (isMobile && el.dataset.mobileSpeed) {
                    el.style.animationDuration = el.dataset.mobileSpeed;
                } else if (el.dataset.speed) {
                    el.style.animationDuration = el.dataset.speed;
                } else {
                    // Немного ускоряем анимацию на мобильных для лучшего UX
                    el.style.animationDuration = isMobile ? '0.6s' : '0.8s';
                }

                // Прекращаем наблюдение за элементом после анимации
                observer.unobserve(el);
            }
        });
    }, {
        // Меньший порог для мобильных устройств
        threshold: isMobile ? 0.05 : 0.1,
        rootMargin: isMobile ? '0px 0px -50px 0px' : '0px'
    });
    
    elements.forEach(element => {
        observer.observe(element);
    });
    
    // Обновляем значения при изменении размера экрана
    window.addEventListener('resize', () => {
        // Пересоздаем наблюдатель только если изменилась категория устройства
        const newIsMobile = window.innerWidth <= 768;
        if (newIsMobile !== isMobile) {
            location.reload(); // Простой способ перезагрузить анимации
        }
    });
}

/**
 * Обратный отсчет до события (например, открытие сервера)
 * @param {string} targetDate Дата события в формате "YYYY-MM-DD HH:MM:SS"
 * @param {string} elementId ID элемента для вывода отсчета
 */
function initCountdown(targetDate, elementId) {
    const countdownElement = document.getElementById(elementId);
    if (!countdownElement) return;
    
    const targetTime = new Date(targetDate).getTime();
    
    function updateCountdown() {
        const now = new Date().getTime();
        const distance = targetTime - now;
        
        if (distance < 0) {
            countdownElement.innerHTML = 'Событие началось!';
            return;
        }
        
        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);
        
        countdownElement.innerHTML = `
            <div class="countdown-item"><span>${days}</span><div>Дней</div></div>
            <div class="countdown-item"><span>${hours}</span><div>Часов</div></div>
            <div class="countdown-item"><span>${minutes}</span><div>Минут</div></div>
            <div class="countdown-item"><span>${seconds}</span><div>Секунд</div></div>
        `;
    }
    
    updateCountdown();
    setInterval(updateCountdown, 1000);
}