# PlayLand Server

🎮 **PlayLand** - система подачи заявок для Minecraft сервера с Telegram ботом и веб-сайтом на едином сервере.

## 🚀 Быстрый запуск

### Вариант 1: Docker (рекомендуется)

```bash
# 1. Клонируйте репозиторий
git clone <repository-url>
cd PlayLand

# 2. Настройте переменные окружения
cp .env.example .env
# Отредактируйте .env файл

# 3. Запустите с Docker
docker-compose up -d
```

### Вариант 2: Локальный запуск

```bash
# 1. Установите зависимости
python -m pip install -r requirements.txt

# 2. Настройте окружение
python deploy.py

# 3. Запустите сервер
python main.py
```

## 📋 Компоненты

- **Telegram Bot** - прием заявок через бота
- **Website** - веб-форма для подачи заявок  
- **Database** - единая база данных SQLite
- **Admin Panel** - управление заявками

## 🔧 Настройка

### Обязательные переменные окружения:

- `TELEGRAM_BOT_TOKEN` - токен Telegram бота
- `SECRET_KEY` - секретный ключ для Flask

### Опциональные:

- `WEBSITE_HOST` - хост веб-сайта (по умолчанию: 0.0.0.0)
- `WEBSITE_PORT` - порт веб-сайта (по умолчанию: 5000)
- `DEBUG` - режим отладки (по умолчанию: false)

## 🌐 Доступ

После запуска:

- **Веб-сайт**: http://localhost:5000
- **Telegram Bot**: @YourBotName
- **Admin Panel**: http://localhost:5000/admin

## 📁 Структура проекта

```
PlayLand/
├── main.py              # Главный файл запуска
├── deploy.py            # Скрипт развертывания
├── requirements.txt     # Python зависимости
├── Dockerfile          # Docker образ
├── docker-compose.yml  # Docker Compose
├── playland/           # Основной модуль
│   ├── tg_bot/         # Telegram бот
│   ├── website/        # Веб-сайт
│   ├── database/       # База данных
│   ├── utils/          # Утилиты
│   └── config.py       # Конфигурация
└── logs/               # Логи
```

## 🛠️ Разработка

```bash
# Установка в режиме разработки
pip install -e .

# Запуск тестов
python -m pytest

# Форматирование кода
black playland/
```

## 📝 Лицензия

MIT License
