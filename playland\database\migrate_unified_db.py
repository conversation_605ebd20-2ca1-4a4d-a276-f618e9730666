#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Скрипт для объединения баз данных бота и сайта в единую систему
"""

import sqlite3
import os
import sys
import json
import logging
from datetime import datetime
from pathlib import Path

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('migration.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DatabaseMigrator:
    """Класс для миграции и объединения баз данных"""
    
    def __init__(self):
        self.bot_db_path = "bot_database.db"
        self.site_db_path = "instance/playland.db"
        self.unified_db_path = "instance/playland_unified.db"
        self.backup_dir = "database_backups"
        
    def create_backup(self):
        """Создание резервных копий баз данных"""
        logger.info("Создание резервных копий баз данных...")
        
        # Создаем директорию для бэкапов
        os.makedirs(self.backup_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Бэкап бота
        if os.path.exists(self.bot_db_path):
            backup_bot = f"{self.backup_dir}/bot_database_{timestamp}.db"
            self._copy_database(self.bot_db_path, backup_bot)
            logger.info(f"Создан бэкап бота: {backup_bot}")
        
        # Бэкап сайта
        if os.path.exists(self.site_db_path):
            backup_site = f"{self.backup_dir}/playland_{timestamp}.db"
            self._copy_database(self.site_db_path, backup_site)
            logger.info(f"Создан бэкап сайта: {backup_site}")
    
    def _copy_database(self, source, destination):
        """Копирование базы данных"""
        source_conn = sqlite3.connect(source)
        dest_conn = sqlite3.connect(destination)
        source_conn.backup(dest_conn)
        source_conn.close()
        dest_conn.close()
    
    def analyze_databases(self):
        """Анализ структуры существующих баз данных"""
        logger.info("Анализ структуры баз данных...")
        
        analysis = {
            'bot_db': self._analyze_database(self.bot_db_path),
            'site_db': self._analyze_database(self.site_db_path)
        }
        
        # Сохраняем анализ в файл
        with open('database_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(analysis, f, indent=2, ensure_ascii=False)
        
        logger.info("Анализ сохранен в database_analysis.json")
        return analysis
    
    def _analyze_database(self, db_path):
        """Анализ одной базы данных"""
        if not os.path.exists(db_path):
            return {"error": "Database not found"}
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Получаем список таблиц
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        analysis = {"tables": {}}
        
        for table in tables:
            # Структура таблицы
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()
            
            # Количество записей
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            
            analysis["tables"][table] = {
                "columns": [{"name": col[1], "type": col[2], "pk": bool(col[5])} for col in columns],
                "record_count": count
            }
        
        conn.close()
        return analysis
    
    def create_unified_schema(self):
        """Создание схемы объединенной базы данных"""
        logger.info("Создание схемы объединенной базы данных...")
        
        conn = sqlite3.connect(self.unified_db_path)
        cursor = conn.cursor()
        
        # Объединенная таблица пользователей
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            telegram_id INTEGER UNIQUE,
            username TEXT,
            email TEXT UNIQUE,
            password_hash TEXT,
            first_name TEXT,
            last_name TEXT,
            nickname TEXT,
            discord_id TEXT,
            discord_tag TEXT,
            avatar_url TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
            registration_date TEXT,
            is_admin BOOLEAN DEFAULT FALSE,
            is_activated BOOLEAN DEFAULT FALSE,
            is_banned BOOLEAN DEFAULT FALSE,
            ban_reason TEXT,
            last_activity TEXT,
            status TEXT DEFAULT 'pending',
            balance REAL DEFAULT 0.0,
            is_whitelisted BOOLEAN DEFAULT FALSE,
            whitelist_status TEXT DEFAULT 'pending'
        )
        ''')
        
        # Объединенная таблица заявок
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS applications (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            telegram_user_id INTEGER,
            minecraft_nickname TEXT NOT NULL,
            discord_tag TEXT,
            reason TEXT,
            status TEXT DEFAULT 'pending',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            timestamp TEXT,
            processed_by INTEGER,
            processed_at DATETIME,
            rejection_reason TEXT,
            source TEXT DEFAULT 'telegram',
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (telegram_user_id) REFERENCES users (telegram_id)
        )
        ''')
        
        # Таблица для синхронизации
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS sync_log (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            operation TEXT,
            table_name TEXT,
            record_id INTEGER,
            source TEXT,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            details TEXT
        )
        ''')
        
        conn.commit()
        conn.close()
        logger.info("Схема объединенной базы данных создана")
    
    def migrate_bot_data(self):
        """Миграция данных из базы бота"""
        logger.info("Миграция данных из базы бота...")
        
        if not os.path.exists(self.bot_db_path):
            logger.warning("База данных бота не найдена")
            return
        
        bot_conn = sqlite3.connect(self.bot_db_path)
        unified_conn = sqlite3.connect(self.unified_db_path)
        
        bot_cursor = bot_conn.cursor()
        unified_cursor = unified_conn.cursor()
        
        # Миграция пользователей бота
        bot_cursor.execute("SELECT * FROM users")
        bot_users = bot_cursor.fetchall()
        
        for user in bot_users:
            unified_cursor.execute('''
            INSERT OR IGNORE INTO users (
                telegram_id, username, first_name, last_name, 
                registration_date, is_admin, is_banned, ban_reason, last_activity
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', user)
        
        # Миграция заявок бота
        bot_cursor.execute("SELECT * FROM applications")
        bot_applications = bot_cursor.fetchall()
        
        for app in bot_applications:
            # Находим user_id в объединенной таблице по telegram_id
            unified_cursor.execute("SELECT id FROM users WHERE telegram_id = ?", (app[1],))
            user_result = unified_cursor.fetchone()
            user_id = user_result[0] if user_result else None
            
            unified_cursor.execute('''
            INSERT INTO applications (
                user_id, telegram_user_id, minecraft_nickname, discord_tag, 
                reason, status, timestamp, processed_by, processed_at, rejection_reason, source
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'telegram')
            ''', (user_id, app[1], app[2], app[3], app[4], app[5], app[6], app[7], app[8], app[9]))
        
        unified_conn.commit()
        bot_conn.close()
        unified_conn.close()
        
        logger.info(f"Мигрировано {len(bot_users)} пользователей и {len(bot_applications)} заявок из бота")
    
    def migrate_site_data(self):
        """Миграция данных из базы сайта"""
        logger.info("Миграция данных из базы сайта...")
        
        if not os.path.exists(self.site_db_path):
            logger.warning("База данных сайта не найдена")
            return
        
        site_conn = sqlite3.connect(self.site_db_path)
        unified_conn = sqlite3.connect(self.unified_db_path)
        
        site_cursor = site_conn.cursor()
        unified_cursor = unified_conn.cursor()
        
        # Миграция пользователей сайта
        try:
            site_cursor.execute("SELECT * FROM users")
            site_users = site_cursor.fetchall()
            
            for user in site_users:
                # Обновляем существующих пользователей или создаем новых
                unified_cursor.execute('''
                INSERT OR REPLACE INTO users (
                    id, username, email, password_hash, nickname, discord_id, 
                    telegram_id, avatar_url, created_at, last_seen, is_admin, is_activated
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', user[:12])  # Берем первые 12 полей
            
            logger.info(f"Мигрировано {len(site_users)} пользователей из сайта")
        except Exception as e:
            logger.error(f"Ошибка при миграции пользователей сайта: {e}")
        
        # Миграция заявок сайта (если есть)
        try:
            site_cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='applications'")
            if site_cursor.fetchone():
                site_cursor.execute("SELECT * FROM applications")
                site_applications = site_cursor.fetchall()
                
                for app in site_applications:
                    unified_cursor.execute('''
                    INSERT INTO applications (
                        user_id, minecraft_nickname, discord_tag, reason, 
                        status, created_at, source
                    ) VALUES (?, ?, ?, ?, ?, ?, 'website')
                    ''', (app[1], app[2], app[3], app[4], app[5], app[6]))
                
                logger.info(f"Мигрировано {len(site_applications)} заявок из сайта")
        except Exception as e:
            logger.error(f"Ошибка при миграции заявок сайта: {e}")
        
        unified_conn.commit()
        site_conn.close()
        unified_conn.close()
    
    def verify_migration(self):
        """Проверка корректности миграции"""
        logger.info("Проверка корректности миграции...")
        
        conn = sqlite3.connect(self.unified_db_path)
        cursor = conn.cursor()
        
        # Подсчет записей
        cursor.execute("SELECT COUNT(*) FROM users")
        users_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM applications")
        apps_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM applications WHERE source = 'telegram'")
        telegram_apps = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM applications WHERE source = 'website'")
        website_apps = cursor.fetchone()[0]
        
        conn.close()
        
        logger.info(f"Результаты миграции:")
        logger.info(f"- Пользователей: {users_count}")
        logger.info(f"- Заявок всего: {apps_count}")
        logger.info(f"- Заявок из Telegram: {telegram_apps}")
        logger.info(f"- Заявок с сайта: {website_apps}")
        
        return {
            'users': users_count,
            'applications': apps_count,
            'telegram_applications': telegram_apps,
            'website_applications': website_apps
        }
    
    def run_migration(self):
        """Запуск полной миграции"""
        logger.info("Начало миграции баз данных...")
        
        try:
            # 1. Создание бэкапов
            self.create_backup()
            
            # 2. Анализ существующих БД
            analysis = self.analyze_databases()
            
            # 3. Создание объединенной схемы
            self.create_unified_schema()
            
            # 4. Миграция данных
            self.migrate_bot_data()
            self.migrate_site_data()
            
            # 5. Проверка результатов
            results = self.verify_migration()
            
            logger.info("Миграция завершена успешно!")
            return True, results
            
        except Exception as e:
            logger.error(f"Ошибка при миграции: {e}")
            return False, str(e)

def main():
    """Главная функция"""
    migrator = DatabaseMigrator()
    success, results = migrator.run_migration()
    
    if success:
        print("✅ Миграция завершена успешно!")
        print(f"📊 Результаты: {results}")
    else:
        print(f"❌ Ошибка миграции: {results}")
        sys.exit(1)

if __name__ == "__main__":
    main()
