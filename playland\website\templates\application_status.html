{% extends "base.html" %}

{% block title %}Статус заявки - PlayLand{% endblock %}

{% block content %}
<div class="application-status-container">
    <div class="container">
        <div class="status-header">
            <h1>Статус вашей заявки</h1>
            <p>Отслеживайте прогресс рассмотрения заявки на сервер PlayLand</p>
        </div>

        {% if application %}
        <div class="status-card">
            <div class="status-info">
                <div class="status-badge status-{{ application.status.value }}">
                    {% if application.status.value == 'pending' %}
                        <i class="fas fa-clock"></i>
                        Ожидает рассмотрения
                    {% elif application.status.value == 'approved' %}
                        <i class="fas fa-check-circle"></i>
                        Одобрена
                    {% elif application.status.value == 'rejected' %}
                        <i class="fas fa-times-circle"></i>
                        Отклонена
                    {% endif %}
                </div>

                <div class="application-details">
                    <div class="detail-row">
                        <span class="label">Minecraft никнейм:</span>
                        <span class="value">{{ application.minecraft_username }}</span>
                    </div>
                    
                    {% if application.age %}
                    <div class="detail-row">
                        <span class="label">Возраст:</span>
                        <span class="value">{{ application.age }} лет</span>
                    </div>
                    {% endif %}
                    
                    <div class="detail-row">
                        <span class="label">Дата подачи:</span>
                        <span class="value">{{ application.created_at.strftime('%d.%m.%Y %H:%M') }}</span>
                    </div>
                    
                    {% if application.reviewed_at %}
                    <div class="detail-row">
                        <span class="label">Дата рассмотрения:</span>
                        <span class="value">{{ application.reviewed_at.strftime('%d.%m.%Y %H:%M') }}</span>
                    </div>
                    {% endif %}
                </div>

                {% if application.status.value == 'approved' %}
                <div class="status-message success">
                    <h3>🎉 Поздравляем!</h3>
                    <p>Ваша заявка одобрена! Теперь вы можете подключиться к серверу PlayLand.</p>
                    <div class="server-info">
                        <div class="server-ip">
                            <strong>IP сервера:</strong> 
                            <span class="ip-address" onclick="copyToClipboard('play.playland.ru')">play.playland.ru</span>
                            <button class="copy-btn" onclick="copyToClipboard('play.playland.ru')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                        <p class="server-note">Нажмите на IP для копирования</p>
                    </div>
                </div>
                {% elif application.status.value == 'rejected' %}
                <div class="status-message error">
                    <h3>❌ Заявка отклонена</h3>
                    {% if application.rejection_reason %}
                    <p><strong>Причина:</strong> {{ application.rejection_reason }}</p>
                    {% endif %}
                    <p>Вы можете подать новую заявку, исправив указанные недочеты.</p>
                    <a href="{{ url_for('application') }}" class="btn btn-primary">Подать новую заявку</a>
                </div>
                {% else %}
                <div class="status-message pending">
                    <h3>⏳ Заявка на рассмотрении</h3>
                    <p>Ваша заявка находится в очереди на рассмотрение администраторами. Обычно это занимает 1-3 дня.</p>
                    <p>Мы уведомим вас о результате через Telegram или Discord, если вы привязали аккаунты.</p>
                </div>
                {% endif %}

                {% if application.experience %}
                <div class="application-content">
                    <h4>Ваш опыт игры:</h4>
                    <p>{{ application.experience }}</p>
                </div>
                {% endif %}

                {% if application.why_join %}
                <div class="application-content">
                    <h4>Почему хотите присоединиться:</h4>
                    <p>{{ application.why_join }}</p>
                </div>
                {% endif %}
            </div>
        </div>
        {% else %}
        <div class="no-application">
            <div class="no-app-icon">
                <i class="fas fa-file-alt"></i>
            </div>
            <h2>У вас нет поданной заявки</h2>
            <p>Чтобы играть на сервере PlayLand, необходимо подать заявку на вступление.</p>
            <a href="{{ url_for('application') }}" class="btn btn-primary">Подать заявку</a>
        </div>
        {% endif %}

        <div class="additional-info">
            <h3>Полезная информация</h3>
            <div class="info-grid">
                <div class="info-card">
                    <i class="fas fa-users"></i>
                    <h4>Сообщество</h4>
                    <p>Присоединяйтесь к нашему Discord серверу для общения с другими игроками</p>
                    <a href="https://discord.gg/49YG5eNXKJ" target="_blank" class="btn btn-outline">Discord</a>
                </div>
                <div class="info-card">
                    <i class="fas fa-book"></i>
                    <h4>Правила</h4>
                    <p>Ознакомьтесь с правилами сервера перед началом игры</p>
                    <a href="{{ url_for('rules') }}" class="btn btn-outline">Правила</a>
                </div>
                <div class="info-card">
                    <i class="fas fa-question-circle"></i>
                    <h4>FAQ</h4>
                    <p>Ответы на часто задаваемые вопросы о сервере</p>
                    <a href="{{ url_for('faq') }}" class="btn btn-outline">FAQ</a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.application-status-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
    padding: 80px 0 40px;
}

.status-header {
    text-align: center;
    margin-bottom: 40px;
}

.status-header h1 {
    color: #00ff00;
    font-family: 'Press Start 2P', cursive;
    font-size: 2rem;
    margin-bottom: 15px;
    text-shadow: 0 0 15px rgba(0, 255, 0, 0.5);
}

.status-header p {
    color: #ccc;
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
}

.status-card {
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid rgba(0, 255, 0, 0.3);
    border-radius: 15px;
    padding: 40px;
    margin-bottom: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 15px 25px;
    border-radius: 25px;
    font-weight: bold;
    font-size: 1.1rem;
    margin-bottom: 30px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.status-pending {
    background: rgba(255, 193, 7, 0.2);
    color: #ffc107;
    border: 2px solid #ffc107;
}

.status-approved {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
    border: 2px solid #28a745;
}

.status-rejected {
    background: rgba(220, 53, 69, 0.2);
    color: #dc3545;
    border: 2px solid #dc3545;
}

.application-details {
    margin-bottom: 30px;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-row .label {
    color: #ccc;
    font-weight: 600;
}

.detail-row .value {
    color: #fff;
    font-weight: bold;
}

.status-message {
    padding: 25px;
    border-radius: 10px;
    margin-bottom: 30px;
}

.status-message h3 {
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.status-message.success {
    background: rgba(40, 167, 69, 0.1);
    border: 2px solid #28a745;
    color: #28a745;
}

.status-message.error {
    background: rgba(220, 53, 69, 0.1);
    border: 2px solid #dc3545;
    color: #dc3545;
}

.status-message.pending {
    background: rgba(255, 193, 7, 0.1);
    border: 2px solid #ffc107;
    color: #ffc107;
}

.server-info {
    margin-top: 20px;
    padding: 20px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
}

.server-ip {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.ip-address {
    background: rgba(0, 255, 0, 0.1);
    color: #00ff00;
    padding: 8px 15px;
    border-radius: 5px;
    font-family: monospace;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.ip-address:hover {
    background: rgba(0, 255, 0, 0.2);
    transform: scale(1.05);
}

.copy-btn {
    background: transparent;
    border: 1px solid #00ff00;
    color: #00ff00;
    padding: 8px 12px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.copy-btn:hover {
    background: #00ff00;
    color: #000;
}

.server-note {
    color: #999;
    font-size: 0.9rem;
    margin: 0;
}

.application-content {
    margin-bottom: 25px;
}

.application-content h4 {
    color: #00ff00;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.application-content p {
    color: #ccc;
    line-height: 1.6;
    background: rgba(255, 255, 255, 0.05);
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #00ff00;
}

.no-application {
    text-align: center;
    padding: 60px 40px;
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid rgba(0, 255, 0, 0.3);
    border-radius: 15px;
    margin-bottom: 40px;
}

.no-app-icon {
    font-size: 4rem;
    color: #00ff00;
    margin-bottom: 20px;
}

.no-application h2 {
    color: #fff;
    margin-bottom: 15px;
    font-size: 1.8rem;
}

.no-application p {
    color: #ccc;
    margin-bottom: 30px;
    font-size: 1.1rem;
}

.additional-info {
    margin-top: 40px;
}

.additional-info h3 {
    color: #00ff00;
    text-align: center;
    margin-bottom: 30px;
    font-size: 1.5rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.info-card {
    background: rgba(0, 0, 0, 0.6);
    border: 1px solid rgba(0, 255, 0, 0.2);
    border-radius: 10px;
    padding: 25px;
    text-align: center;
    transition: all 0.3s ease;
}

.info-card:hover {
    border-color: rgba(0, 255, 0, 0.5);
    transform: translateY(-5px);
}

.info-card i {
    font-size: 2.5rem;
    color: #00ff00;
    margin-bottom: 15px;
}

.info-card h4 {
    color: #fff;
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.info-card p {
    color: #ccc;
    margin-bottom: 20px;
    line-height: 1.5;
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.btn-primary {
    background: linear-gradient(45deg, #00ff00, #00cc00);
    color: #000;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #00cc00, #009900);
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    color: #00ff00;
    border-color: #00ff00;
}

.btn-outline:hover {
    background: #00ff00;
    color: #000;
}

@media (max-width: 768px) {
    .status-header h1 {
        font-size: 1.5rem;
    }
    
    .status-card {
        padding: 25px 20px;
    }
    
    .detail-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .server-ip {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Показываем уведомление
        const notification = document.createElement('div');
        notification.textContent = 'IP скопирован в буфер обмена!';
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #00ff00;
            color: #000;
            padding: 15px 20px;
            border-radius: 8px;
            font-weight: bold;
            z-index: 1000;
            animation: slideIn 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    });
}

// CSS анимация для уведомления
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
