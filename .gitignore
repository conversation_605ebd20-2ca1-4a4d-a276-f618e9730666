# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# PyCharm
.idea/

# VS Code
.vscode/
*.code-workspace

# Sublime Text
*.sublime-project
*.sublime-workspace

# Project specific
logs/
*.db
*.sqlite
*.sqlite3
*.log
*.pid
*.swp
*.swo
.DS_Store
Thumbs.db

# Minecraft server specific
playland/server/world/
playland/server/world_nether/
playland/server/world_the_end/
playland/server/logs/
playland/server/crash-reports/
playland/server/plugins/*/cache/
playland/server/plugins/*/data/
playland/server/plugins/*/logs/
playland/server/plugins/*/temp/
playland/server/plugins/*/backups/
playland/server/plugins/*/userdata/
playland/server/plugins/*/worlds/
playland/server/plugins/*/players/
playland/server/plugins/*/config/
playland/server/plugins/*/database/
playland/server/plugins/*/economy/
playland/server/plugins/*/permissions/
playland/server/plugins/*/stats/
playland/server/plugins/*/warps/
playland/server/plugins/*/homes/
playland/server/plugins/*/kits/
playland/server/plugins/*/shops/
playland/server/plugins/*/jail/
playland/server/plugins/*/bans/
playland/server/plugins/*/mutes/
playland/server/plugins/*/warns/
playland/server/plugins/*/reports/
playland/server/plugins/*/tickets/
playland/server/plugins/*/mail/
playland/server/plugins/*/messages/
playland/server/plugins/*/chat/
playland/server/plugins/*/votes/
playland/server/plugins/*/rewards/
playland/server/plugins/*/ranks/
playland/server/plugins/*/groups/
playland/server/plugins/*/permissions/
playland/server/plugins/*/economy/
playland/server/plugins/*/shops/
playland/server/plugins/*/warps/
playland/server/plugins/*/homes/
playland/server/plugins/*/kits/
playland/server/plugins/*/jail/
playland/server/plugins/*/bans/
playland/server/plugins/*/mutes/
playland/server/plugins/*/warns/
playland/server/plugins/*/reports/
playland/server/plugins/*/tickets/
playland/server/plugins/*/mail/
playland/server/plugins/*/messages/
playland/server/plugins/*/chat/
playland/server/plugins/*/votes/
playland/server/plugins/*/rewards/
playland/server/plugins/*/ranks/
playland/server/plugins/*/groups/
playland/server/plugins/*/permissions/
playland/server/plugins/*/economy/
playland/server/plugins/*/shops/
playland/server/plugins/*/warps/
playland/server/plugins/*/homes/
playland/server/plugins/*/kits/
playland/server/plugins/*/jail/
playland/server/plugins/*/bans/
playland/server/plugins/*/mutes/
playland/server/plugins/*/warns/
playland/server/plugins/*/reports/
playland/server/plugins/*/tickets/
playland/server/plugins/*/mail/
playland/server/plugins/*/messages/
playland/server/plugins/*/chat/
playland/server/plugins/*/votes/
playland/server/plugins/*/rewards/
playland/server/plugins/*/ranks/
playland/server/plugins/*/groups/
