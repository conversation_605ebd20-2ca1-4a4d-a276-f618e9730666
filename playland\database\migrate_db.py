#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import sqlite3
import json
from datetime import datetime

def run_migrations():
    """Выполнение миграций базы данных"""
    print("Начало миграции базы данных...")
    
    db_path = os.path.join('instance', 'playland.db')
    
    if not os.path.exists(db_path):
        print(f"Файл базы данных не найден: {db_path}")
        return False
    
    try:
        # Подключаемся к базе данных
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Проверяем существование таблицы пользователей
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        if not cursor.fetchone():
            print("Таблица пользователей не найдена.")
            conn.close()
            return False
        
        # Получаем список существующих колонок
        cursor.execute("PRAGMA table_info(users)")
        existing_columns = [row[1] for row in cursor.fetchall()]
        print(f"Существующие колонки: {existing_columns}")
        
        # Добавляем новые колонки, если их нет
        columns_to_add = {
            "bio": "TEXT",
            "minecraft_experience": "TEXT",
            "interests": "TEXT",
            "achievements": "TEXT",
            "social_links": "TEXT",
            "last_seen_on_server": "DATETIME",
            "discord": "TEXT"  # Поле для хранения Discord ID в формате username#1234
        }
        
        for column_name, column_type in columns_to_add.items():
            if column_name not in existing_columns:
                try:
                    cursor.execute(f"ALTER TABLE users ADD COLUMN {column_name} {column_type}")
                    print(f"Добавлена колонка {column_name} ({column_type})")
                except sqlite3.Error as e:
                    print(f"Ошибка при добавлении колонки {column_name}: {e}")
            else:
                print(f"Колонка {column_name} уже существует")
        
        # Если есть discord_id, но нет discord, копируем данные
        if "discord_id" in existing_columns and "discord" in existing_columns:
            try:
                cursor.execute("UPDATE users SET discord = discord_id WHERE discord IS NULL AND discord_id IS NOT NULL")
                print("Данные из discord_id скопированы в discord")
            except sqlite3.Error as e:
                print(f"Ошибка при копировании данных из discord_id: {e}")
        
        # Сохраняем изменения
        conn.commit()
        conn.close()
        
        print("Миграция базы данных завершена успешно.")
        return True
    except Exception as e:
        print(f"Ошибка при выполнении миграции: {e}")
        return False

if __name__ == "__main__":
    success = run_migrations()
    sys.exit(0 if success else 1) 