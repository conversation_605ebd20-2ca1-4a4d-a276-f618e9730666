from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session
from flask_login import <PERSON>ginManager, current_user, logout_user, login_user
from sqlalchemy import text
import os
from datetime import datetime, timezone
import sys
from flask_wtf.csrf import CSRFProtect
import secrets
from dotenv import load_dotenv

# Добавляем родительскую директорию в sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# Импортируем формы
from playland.website.forms import LoginForm, RegisterForm, ApplicationForm

# Импортируем основные модули
try:
    from playland.website.models import db, User, Application, AuditLog, TeamApplication, Ticket
    from playland.website.security import SecurityHeaders, validate_session, generate_csrf_token
    print("✅ Основные модели и безопасность импортированы")
except ImportError as e:
    print(f"❌ Ошибка импорта основных модулей: {e}")
    # Создаем заглушки для критичных модулей
    db = None
    SecurityHeaders = None
    validate_session = lambda: True
    generate_csrf_token = lambda: ""

# Импортируем Blueprint'ы с обработкой ошибок
profile_bp = None
admin_bp = None
auth_bp = None

try:
    from playland.website.routes.auth import auth_bp
    print("✅ Auth Blueprint импортирован")
except ImportError as e:
    print(f"⚠️ Auth Blueprint не найден: {e}")

try:
    from playland.website.routes.admin import admin_bp
    print("✅ Admin Blueprint импортирован")
except ImportError as e:
    print(f"⚠️ Admin Blueprint не найден: {e}")

try:
    from playland.website.routes.profile import profile_bp
    print("✅ Profile Blueprint импортирован")
except ImportError as e:
    print(f"⚠️ Profile Blueprint не найден: {e}")

# Опциональные импорты (могут отсутствовать)
try:
    from playland.integrations.cross_platform import init_integrations
    print("✅ Cross-platform интеграции импортированы")
except ImportError:
    print("⚠️ Cross-platform интеграции не найдены, используем заглушку")
    init_integrations = lambda app: None

try:
    from playland.integrations.telegram import init_telegram_integration
    print("✅ Telegram интеграции импортированы")
except ImportError:
    print("⚠️ Telegram интеграции не найдены, используем заглушку")
    init_telegram_integration = lambda app: None

try:
    from playland.integrations.discord import init_discord_integration
    print("✅ Discord интеграции импортированы")
except ImportError:
    print("⚠️ Discord интеграции не найдены, используем заглушку")
    init_discord_integration = lambda app: None

# Загрузка переменных окружения
load_dotenv()

def create_app():
    app = Flask(__name__)

    # Конфигурация приложения
    # Лучше вынести это в config.py или переменные окружения
    app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'your_fallback_secret_key_here_for_dev') # Обязательно смените в продакшене!
    app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL', 'sqlite:///playland/database/playland.db')
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

    # Настройка API
    app.config['API_SECRET_KEY'] = os.environ.get('API_SECRET_KEY', 'playland_api_secret_key')

    # Дополнительная конфигурация для интеграций
    app.config['DISCORD_CLIENT_ID'] = os.environ.get('DISCORD_CLIENT_ID', '')
    app.config['DISCORD_CLIENT_SECRET'] = os.environ.get('DISCORD_CLIENT_SECRET', '')
    app.config['DISCORD_REDIRECT_URI'] = os.environ.get('DISCORD_REDIRECT_URI', 'http://localhost:5000/auth/discord/callback')
    app.config['DISCORD_BOT_TOKEN'] = os.environ.get('DISCORD_BOT_TOKEN', '')
    app.config['DISCORD_GUILD_ID'] = os.environ.get('DISCORD_GUILD_ID', '')
    app.config['DISCORD_LOG_CHANNEL_ID'] = os.environ.get('DISCORD_LOG_CHANNEL_ID', '')

    # Инициализация расширений
    db.init_app(app)
    csrf = CSRFProtect(app)

    # Автоматическая инициализация БД при первом запуске
    with app.app_context():
        try:
            # Проверяем, существует ли таблица User
            from sqlalchemy import inspect
            inspector = inspect(db.engine)
            tables = inspector.get_table_names()

            if not tables or 'user' not in tables:
                print("🔧 База данных не инициализирована. Создаем таблицы...")
                db.create_all()
                print("✅ Таблицы базы данных созданы")

                # Создаем администратора по умолчанию
                admin_email = os.getenv('ADMIN_EMAIL', '<EMAIL>')
                admin_password = os.getenv('ADMIN_PASSWORD', 'admin123')

                existing_admin = User.query.filter_by(email=admin_email).first()
                if not existing_admin:
                    from werkzeug.security import generate_password_hash
                    admin = User(
                        username='admin',
                        email=admin_email,
                        password_hash=generate_password_hash(admin_password),
                        nickname='Администратор',
                        is_admin=True,
                        is_activated=True
                        # created_at будет установлено автоматически через default
                    )
                    db.session.add(admin)
                    db.session.commit()
                    print(f"✅ Создан администратор: {admin_email}")
            else:
                print("✅ База данных уже инициализирована")

        except Exception as e:
            print(f"⚠️ Ошибка при инициализации БД: {e}")
            # Не прерываем запуск приложения

    # Инициализация интеграций
    init_integrations(app)
    init_telegram_integration(app)
    init_discord_integration(app)

    # Middleware для безопасности
    @app.before_request
    def security_middleware():
        """Middleware для проверки безопасности"""
        # Валидация сессии
        if current_user.is_authenticated:
            if not validate_session():
                logout_user()
                session.clear()
                flash('Ваша сессия истекла. Пожалуйста, войдите снова.', 'warning')
                return redirect(url_for('auth.enhanced_login'))

    @app.after_request
    def after_request(response):
        """Установка заголовков безопасности"""
        return SecurityHeaders.set_security_headers(response)

    # Добавляем CSRF токен в контекст шаблонов
    @app.context_processor
    def inject_csrf_token():
        return dict(csrf_token=generate_csrf_token)

    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.enhanced_login' # Обновленный маршрут входа
    login_manager.login_message = "Пожалуйста, войдите, чтобы получить доступ к этой странице."
    login_manager.login_message_category = "info"

    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))

    # Регистрация Blueprints с проверкой
    blueprints_registered = 0

    if auth_bp:
        app.register_blueprint(auth_bp, url_prefix='/auth')
        print("✅ Auth Blueprint зарегистрирован с префиксом /auth")
        blueprints_registered += 1
    else:
        print("⚠️ Auth Blueprint не зарегистрирован")

    if admin_bp:
        app.register_blueprint(admin_bp, url_prefix='/admin')
        print("✅ Admin Blueprint зарегистрирован с префиксом /admin")
        blueprints_registered += 1
    else:
        print("⚠️ Admin Blueprint не зарегистрирован")

    if profile_bp:
        app.register_blueprint(profile_bp, url_prefix='/profile')
        print("✅ Profile Blueprint зарегистрирован с префиксом /profile")
        blueprints_registered += 1
    else:
        print("⚠️ Profile Blueprint не зарегистрирован")

    print(f"📊 Зарегистрировано Blueprint'ов: {blueprints_registered}/3")

    # Обработчики ошибок
    @app.errorhandler(404)
    def not_found_error(error):
        return render_template('404.html', e=error), 404

    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback() # Важно откатить сессию в случае ошибки БД
        return render_template('500.html', e=error), 500

    @app.errorhandler(401)
    def unauthorized_error(error):
        return render_template('errors/401.html'), 401

    @app.errorhandler(403)
    def forbidden_error(error):
        return render_template('errors/403.html'), 403

    # Добавляем current_user и datetime в контекст шаблонов для удобства
    @app.context_processor
    def inject_user_and_now():
        return dict(current_user=current_user, now=lambda: datetime.now(timezone.utc), config=app.config)

    # Health check endpoint для Docker
    @app.route('/health')
    def health_check():
        """Проверка здоровья приложения"""
        try:
            # Проверяем подключение к БД
            db.session.execute(text('SELECT 1'))
            return jsonify({
                'status': 'healthy',
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'database': 'connected'
            }), 200
        except Exception as e:
            return jsonify({
                'status': 'unhealthy',
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'error': str(e)
            }), 500

    # Страница предварительного просмотра для разработки
    @app.route('/preview')
    def preview_pages():
        """Страница для быстрого просмотра всех страниц сайта"""
        return render_template('preview.html')

    # Пример маршрута для главной страницы (лучше вынести в main_bp)
    @app.route('/')
    def index():
        return render_template('index.html') # Предполагается наличие index.html

    @app.route('/rules')
    def rules():
        return render_template('rules.html')

    @app.route('/gallery')
    def gallery():
        # В реальной ситуации здесь будет логика загрузки изображений
        sample_images = [
            {'url': 'images/gallery/img1.jpg', 'title': 'Наш красивый спавн'},
            {'url': 'images/gallery/img2.jpg', 'title': 'Общий вид на сервер'},
            {'url': 'images/gallery/img3.jpg', 'title': 'Ивент на День рождения сервера'},
            {'url': 'images/gallery/img4.jpg', 'title': 'Красивая постройка игрока'},
            {'url': 'images/gallery/img5.jpg', 'title': 'Совместный проект сообщества'},
            {'url': 'images/gallery/img6.jpg', 'title': 'Последний ивент'},
        ]
        return render_template('gallery.html', images=sample_images)

    @app.route('/faq')
    def faq():
        return render_template('faq.html')

    @app.route('/team')
    def team():
        # Данные о команде, сгруппированные по категориям
        team_data = {
            'admins': [
                {
                    'name': 'ALEZOEX',
                    'role': 'Владелец проекта',
                    'avatar': 'images_of_command/ALEZOEX.png',
                    'description': 'Владелец и создатель PlayLand. Руководит всеми аспектами проекта.',
                    'socials': [
                        {'platform': 'discord', 'icon': 'fab fa-discord', 'url': 'https://discord.com/users/706467232380747796'}
                    ]
                },
                {
                    'name': 'KIRUYXAN',
                    'role': 'Главный администратор',
                    'avatar': 'images_of_command/KIRUYXAN.jpg',
                    'description': 'Главный администратор сервера PlayLand. Отвечает за технические аспекты и развитие проекта.',
                    'socials': [
                        {'platform': 'discord', 'icon': 'fab fa-discord', 'url': 'https://discord.com/users/450335814329827329'},
                        {'platform': 'youtube', 'icon': 'fab fa-youtube', 'url': 'https://www.youtube.com/channel/UCsbU1JEWykmDfYq-FOHj0AA'},
                        {'platform': 'github', 'icon': 'fab fa-github', 'url': 'https://github.com/KIRUYXAN'},
                        {'platform': 'reddit', 'icon': 'fab fa-reddit', 'url': 'https://www.reddit.com/u/KIRUYXAN'},
                        {'platform': 'twitch', 'icon': 'fab fa-twitch', 'url': 'https://www.twitch.tv/kiruyxan'},
                        {'platform': 'steam', 'icon': 'fab fa-steam', 'url': 'https://steamcommunity.com/id/KIRUYXAN/'}
                    ]
                },
                {
                    'name': 'VOX',
                    'role': 'Главный управляющий Discord сервером',
                    'avatar': 'images_of_command/VOX.jpg',
                    'description': 'Отвечает за организацию и модерацию Discord сообщества проекта.',
                    'socials': [
                        {'platform': 'discord', 'icon': 'fab fa-discord', 'url': 'https://discord.com/users/1268546419359223862'},
                        {'platform': 'tiktok', 'icon': 'fab fa-tiktok', 'url': 'https://www.tiktok.com/@jellyuuy'}
                    ]
                },
                {
                    'name': 'OVEL',
                    'role': 'Главный управляющий ТГК по новостям',
                    'avatar': 'images_of_command/OVEL.jpg',
                    'description': 'Отвечает за создание и публикацию новостей через Telegram канал.'
                }
            ],
            'moderators': [
                {'name': 'Moder1', 'role': 'Старший модератор', 'avatar': 'images/avatars/moder1.png', 'description': 'Следит за порядком на сервере'},
                {'name': 'Moder2', 'role': 'Модератор', 'avatar': 'images/avatars/moder2.png', 'description': 'Помогает игрокам и следит за чатом'},
            ],
            'helpers': [
                {'name': 'Helper1', 'role': 'Хелпер', 'avatar': 'images/avatars/helper1.png', 'description': 'Отвечает на вопросы новичков'},
            ]
        }
        return render_template('team.html', team_data=team_data)

    @app.route('/application', methods=['GET', 'POST'], endpoint='application')
    def application():
        if request.method == 'POST':
            # Получаем данные из формы
            minecraft_nickname = request.form.get('minecraft_nickname')
            discord_tag = request.form.get('discord_tag')
            email = request.form.get('email')
            age = request.form.get('age')
            about_yourself = request.form.get('about_yourself')
            find_server = request.form.get('find_server')
            friend_referral = request.form.get('friend_referral', '')
            minecraft_experience = request.form.get('minecraft_experience')
            minecraft_preferences = request.form.get('minecraft_preferences', '')
            rules_acceptance = request.form.get('rules_acceptance') == 'on'
            additional_info = request.form.get('additional_info', '')
            notification_acceptance = request.form.get('notification_acceptance') == 'on'

            # Валидация данных
            errors = {}
            if not minecraft_nickname:
                errors['minecraft_nickname'] = 'Необходимо указать никнейм'
            if not discord_tag:
                errors['discord_tag'] = 'Необходимо указать тег Discord'
            if not email or '@' not in email:
                errors['email'] = 'Необходимо указать корректный email'
            if not age or not age.isdigit() or int(age) < 8 or int(age) > 100:
                errors['age'] = 'Необходимо указать корректный возраст (от 8 до 100)'
            if not about_yourself:
                errors['about_yourself'] = 'Расскажите немного о себе'
            if not find_server:
                errors['find_server'] = 'Укажите, как вы нашли наш сервер'
            if not minecraft_experience:
                errors['minecraft_experience'] = 'Укажите ваш опыт игры'
            if not rules_acceptance:
                errors['rules_acceptance'] = 'Необходимо принять правила сервера'

            # Если есть ошибки, возвращаем форму с сообщениями об ошибках
            if errors:
                return render_template('application.html', errors=errors, form_data=request.form)

            # Здесь должна быть логика сохранения заявки в БД
            application = Application(
                minecraft_nickname=minecraft_nickname,
                discord_tag=discord_tag,
                email=email,
                age=age,
                about_yourself=about_yourself,
                find_server=find_server,
                friend_referral=friend_referral,
                minecraft_experience=minecraft_experience,
                minecraft_preferences=minecraft_preferences,
                additional_info=additional_info
            )
            db.session.add(application)
            db.session.commit()

            # Отправка уведомления администраторам
            # Код отправки уведомлений...

            # Перенаправление с сообщением об успехе
            flash('Ваша заявка успешно отправлена! Мы свяжемся с вами через Discord или Email.', 'success')
            return redirect(url_for('index'))

        # GET запрос - просто отображаем форму
        return render_template('application.html')

    @app.route('/team_application', methods=['GET', 'POST'])
    def team_application():
        if request.method == 'POST':
            # Получаем данные из формы заявки в команду
            minecraft_nickname = request.form.get('minecraft_nickname')
            discord_tag = request.form.get('discord_tag')
            email = request.form.get('email')
            age = request.form.get('age')
            about_yourself = request.form.get('about_yourself')
            position = request.form.get('position')
            custom_position = request.form.get('custom_position')
            experience = request.form.get('experience')
            motivation = request.form.get('motivation')
            ideas = request.form.get('ideas', '')
            additional_info = request.form.get('additional_info', '')

            # Валидация данных
            errors = {}
            if not minecraft_nickname:
                errors['minecraft_nickname'] = 'Необходимо указать никнейм'
            if not discord_tag:
                errors['discord_tag'] = 'Необходимо указать тег Discord'
            if not email or '@' not in email:
                errors['email'] = 'Необходимо указать корректный email'
            if not age or not age.isdigit() or int(age) < 14 or int(age) > 100:
                errors['age'] = 'Необходимо указать корректный возраст (от 14 до 100)'
            if not about_yourself:
                errors['about_yourself'] = 'Расскажите немного о себе'
            if not position:
                errors['position'] = 'Необходимо выбрать желаемую роль'
            if position == 'other' and not custom_position:
                errors['custom_position'] = 'Необходимо указать желаемую должность'
            if not experience:
                errors['experience'] = 'Расскажите о своём опыте'
            if not motivation:
                errors['motivation'] = 'Укажите вашу мотивацию'

            # Если есть ошибки, возвращаем форму с сообщениями об ошибках
            if errors:
                return render_template('team_application.html', errors=errors, form_data=request.form)

            # Проверяем, авторизован ли пользователь
            user_id = current_user.id if current_user.is_authenticated else None

            if not user_id:
                # Если пользователь не авторизован, предлагаем зарегистрироваться
                flash('Для подачи заявки необходимо зарегистрироваться или войти в аккаунт.', 'warning')
                # Сохраняем данные формы в сессии для восстановления после регистрации
                session['team_application_data'] = {
                    'minecraft_nickname': minecraft_nickname,
                    'discord_tag': discord_tag,
                    'email': email,
                    'age': age,
                    'about_yourself': about_yourself,
                    'position': position,
                    'custom_position': custom_position,
                    'experience': experience,
                    'motivation': motivation,
                    'ideas': ideas,
                    'additional_info': additional_info
                }
                return redirect(url_for('register', next=url_for('team_application')))

            try:
                # Создаем тикет для отслеживания заявки
                ticket_title = f"Заявка в команду от {minecraft_nickname}"
                ticket_content = f"""
Никнейм: {minecraft_nickname}
Discord: {discord_tag}
Email: {email}
Возраст: {age}
Желаемая должность: {custom_position if position == 'other' else position}
Опыт: {experience}
Мотивация: {motivation}
Идеи: {ideas}
Дополнительная информация: {additional_info}
                """

                ticket = Ticket(
                    title=ticket_title,
                    content=ticket_content,
                    user_id=user_id,
                    application_type='team'
                )
                db.session.add(ticket)
                db.session.flush()  # Получаем ID тикета

                # Создаем заявку в команду
                team_app = TeamApplication(
                    user_id=user_id,
                    minecraft_nickname=minecraft_nickname,
                    discord_tag=discord_tag,
                    email=email,
                    age=int(age),
                    about_yourself=about_yourself,
                    desired_position=position,
                    custom_position=custom_position if position == 'other' else None,
                    experience=experience,
                    motivation=motivation,
                    ideas=ideas
                )
                team_app.ticket_id = ticket.id
                db.session.add(team_app)

                # Логирование
                log = AuditLog(
                    action="team_application_create",
                    user_id=user_id,
                    details=f"Создана заявка на вступление в команду от {minecraft_nickname}"
                )
                db.session.add(log)

                db.session.commit()

                # Отправка уведомления администраторам через Telegram бота
                try:
                    from playland.integrations.telegram import send_team_application_notification

                    # Формируем текст уведомления
                    notification_text = f"""
📧 <b>Новая заявка в команду!</b>
👤 Никнейм: <code>{minecraft_nickname}</code>
🔖 Discord: <code>{discord_tag}</code>
👶 Возраст: {age}
🎮 Должность: {custom_position if position == 'other' else position}
                    """

                    # Отправляем уведомление
                    send_team_application_notification(notification_text, team_app.id)
                except Exception as e:
                    app.logger.error(f"Ошибка отправки уведомления в Telegram: {str(e)}")

                # Перенаправление с сообщением об успехе
                flash('Ваша заявка на вступление в команду успешно отправлена! Вы можете отслеживать статус вашей заявки в разделе "Мои заявки".', 'success')
                return redirect(url_for('team'))

            except Exception as e:
                db.session.rollback()
                app.logger.error(f"Ошибка при создании заявки в команду: {str(e)}")
                flash('Произошла ошибка при создании заявки. Пожалуйста, попробуйте еще раз.', 'error')
                return render_template('team_application.html', form_data=request.form)

        # GET запрос - проверяем, есть ли сохраненные данные в сессии
        form_data = session.pop('team_application_data', None)

        # GET запрос - просто отображаем форму
        return render_template('team_application.html', form_data=form_data)

    # Старый маршрут входа удален - используется auth.enhanced_login

    # Старые маршруты регистрации, выхода и сброса пароля удалены - используются новые из auth_bp

    @app.route('/auth/discord/state')
    def auth_discord_state():
        """Генерация state для CSRF защиты при авторизации через Discord"""
        state = secrets.token_hex(16)
        session['discord_oauth_state'] = state
        return jsonify({'state': state})

    @app.route('/auth/telegram')
    def auth_telegram():
        """Начало процесса авторизации через Telegram.
        Эта функция не будет использоваться напрямую, так как виджет встроен в страницу."""
        # Теперь виджет Telegram встроен в страницы login.html и register.html
        # Эта функция оставлена для совместимости
        return redirect(url_for('login'))

    @app.route('/auth/telegram/callback')
    def auth_telegram_callback():
        """Обработка данных от Telegram Login Widget"""
        # Получаем данные из запроса
        auth_data = {k: request.args.get(k) for k in request.args}

        # Логирование попытки входа через Telegram
        app.logger.info(f"Попытка входа через Telegram: IP={request.remote_addr}, данные={auth_data}")

        # Проверка подписи
        if not validate_telegram_data(auth_data):
            app.logger.warning(f"Неудачная попытка входа через Telegram: неверная подпись, IP={request.remote_addr}")
            flash('Ошибка авторизации Telegram: неверная подпись данных', 'error')
            return redirect(url_for('login'))

        # Получаем Telegram ID пользователя
        telegram_id = auth_data.get('id')
        if not telegram_id:
            app.logger.warning(f"Неудачная попытка входа через Telegram: отсутствует ID, IP={request.remote_addr}")
            flash('Не удалось получить идентификатор Telegram', 'error')
            return redirect(url_for('login'))

        # Проверяем, существует ли пользователь с таким telegram_id
        user = User.query.filter_by(telegram_id=telegram_id).first()

        # Проверяем, есть ли пользователь с таким email (если он предоставлен)
        email = None
        if current_user.is_authenticated:
            # Если пользователь уже авторизован, это попытка связать аккаунт
            if user and user.id != current_user.id:
                app.logger.warning(f"Неудачная попытка связать Telegram: аккаунт уже связан с другим пользователем, user_id={current_user.id}, telegram_id={telegram_id}, IP={request.remote_addr}")
                flash('Этот Telegram аккаунт уже связан с другим пользователем', 'error')
                return redirect(url_for('connect_telegram', error='already_linked'))

            # Связываем Telegram с текущим пользователем
            current_user.telegram_id = telegram_id
            current_user.telegram = auth_data.get('username')

            # Если у пользователя нет аватара, но есть в Telegram, используем его
            if not current_user.avatar_url and auth_data.get('photo_url'):
                current_user.avatar_url = auth_data.get('photo_url')

            # Активируем аккаунт, если он не был активирован
            if not current_user.is_activated:
                current_user.is_activated = True

            db.session.commit()

            # Запись в аудит лог
            try:
                audit_log = AuditLog(
                    action="connect_telegram",
                    user_id=current_user.id,
                    ip_address=request.remote_addr,
                    details=f"Подключение Telegram аккаунта: {auth_data.get('username') or telegram_id}"
                )
                db.session.add(audit_log)
                db.session.commit()
            except Exception as e:
                app.logger.error(f"Ошибка при записи в аудит лог: {str(e)}")

            app.logger.info(f"Успешное связывание Telegram: user_id={current_user.id}, telegram_id={telegram_id}, IP={request.remote_addr}")
            flash('Telegram аккаунт успешно подключен!', 'success')
            return redirect(url_for('profile.view_profile'))

        if user:
            # Если пользователь существует, выполняем вход
            login_user(user, remember=True)

            # Обновляем last_seen
            user.last_seen = datetime.now(timezone.utc)
            db.session.commit()

            # Запись в аудит лог
            try:
                audit_log = AuditLog(
                    action="login_telegram",
                    user_id=user.id,
                    ip_address=request.remote_addr,
                    details=f"Вход через Telegram"
                )
                db.session.add(audit_log)
                db.session.commit()
            except Exception as e:
                app.logger.error(f"Ошибка при записи в аудит лог: {str(e)}")

            app.logger.info(f"Успешный вход через Telegram: user_id={user.id}, telegram_id={telegram_id}, IP={request.remote_addr}")
            flash('Вход через Telegram выполнен успешно!', 'success')
            return redirect(url_for('index'))
        else:
            # Если пользователя нет, создаем нового
            username = auth_data.get('username') or f"tg_user_{telegram_id}"
            first_name = auth_data.get('first_name', '')
            last_name = auth_data.get('last_name', '')
            photo_url = auth_data.get('photo_url')

            # Проверяем уникальность username
            base_username = username
            counter = 1
            while User.query.filter_by(username=username).first():
                username = f"{base_username}_{counter}"
                counter += 1

            # Генерируем случайный пароль (пользователь не будет его знать/использовать)
            password = secrets.token_hex(16)

            # Создаем нового пользователя
            new_user = User(
                username=username,
                nickname=f"{first_name} {last_name}".strip(),
                telegram_id=telegram_id,
                telegram=auth_data.get('username'),
                avatar_url=photo_url,
                created_at=datetime.now(timezone.utc),
                is_activated=True  # Аккаунт активирован, так как подтвержден через Telegram
            )
            new_user.password = password

            db.session.add(new_user)
            db.session.commit()

            # Запись в аудит лог
            try:
                audit_log = AuditLog(
                    action="register_telegram",
                    user_id=new_user.id,
                    ip_address=request.remote_addr,
                    details=f"Регистрация через Telegram"
                )
                db.session.add(audit_log)
                db.session.commit()
            except Exception as e:
                app.logger.error(f"Ошибка при записи в аудит лог: {str(e)}")

            login_user(new_user, remember=True)
            app.logger.info(f"Успешная регистрация через Telegram: user_id={new_user.id}, telegram_id={telegram_id}, IP={request.remote_addr}")
            flash('Регистрация через Telegram успешно завершена!', 'success')
            return redirect(url_for('index'))

    # Вспомогательная функция для проверки данных от Telegram
    def validate_telegram_data(auth_data):
        """Проверяет подпись данных, полученных от Telegram Login Widget"""
        if 'hash' not in auth_data:
            app.logger.warning("Отсутствует hash в данных Telegram")
            return False

        # Получаем секретный ключ бота (SHA256 от токена бота)
        import hashlib
        bot_token = os.environ.get('TELEGRAM_BOT_TOKEN')
        if not bot_token:
            app.logger.error("Отсутствует TELEGRAM_BOT_TOKEN в переменных окружения")
            return False

        secret_key = hashlib.sha256(bot_token.encode()).digest()

        # Формируем строку для проверки
        # Исключаем hash из данных и сортируем по ключу
        hash_value = auth_data.pop('hash')

        # Проверяем наличие обязательных полей
        required_fields = ['id', 'first_name', 'auth_date']
        for field in required_fields:
            if field not in auth_data:
                app.logger.warning(f"Отсутствует обязательное поле {field} в данных Telegram")
                auth_data['hash'] = hash_value  # Возвращаем hash обратно
                return False

        # Проверяем, не устарели ли данные (не старше 24 часов)
        import time
        auth_date = int(auth_data.get('auth_date', 0))
        current_time = int(time.time())
        if current_time - auth_date > 86400:  # 24 часа в секундах
            app.logger.warning(f"Устаревшие данные Telegram: {current_time - auth_date} секунд")
            auth_data['hash'] = hash_value  # Возвращаем hash обратно
            return False

        # Формируем строку для проверки
        data_check_array = []
        for k, v in sorted(auth_data.items()):
            data_check_array.append(f"{k}={v}")
        data_check_string = '\n'.join(data_check_array)

        # Создаем HMAC-SHA256 подпись
        import hmac
        signature = hmac.new(
            secret_key,
            data_check_string.encode(),
            hashlib.sha256
        ).hexdigest()

        # Возвращаем hash обратно в auth_data
        auth_data['hash'] = hash_value

        # Сравниваем полученную подпись с переданной
        is_valid = signature == hash_value

        if not is_valid:
            app.logger.warning(f"Неверная подпись Telegram. Ожидалось: {signature}, получено: {hash_value}")

        return is_valid

    with app.app_context():
        db.create_all() # Создаем таблицы в БД, если их нет
        # Можно добавить создание администратора или другие начальные данные здесь
        # Добавить недостающие столбцы
        inspector = db.inspect(db.engine)
        if 'users' in inspector.get_table_names():
            columns = [c['name'] for c in inspector.get_columns('users')]
            with db.engine.connect() as conn:
                if 'nickname' not in columns:
                    conn.execute(db.text('ALTER TABLE users ADD COLUMN nickname TEXT'))
                if 'discord_id' not in columns:
                    conn.execute(db.text('ALTER TABLE users ADD COLUMN discord_id TEXT'))
                if 'telegram_id' not in columns:
                    conn.execute(db.text('ALTER TABLE users ADD COLUMN telegram_id TEXT'))
                if 'avatar_url' not in columns:
                    conn.execute(db.text('ALTER TABLE users ADD COLUMN avatar_url TEXT'))
                conn.commit()

    # Настройка логирования
    import logging
    from logging.handlers import RotatingFileHandler

    log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
    logs_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')

    # Создаем директорию для логов, если её нет
    if not os.path.exists(logs_dir):
        os.makedirs(logs_dir, exist_ok=True)
        print(f"✅ Создана директория для логов: {logs_dir}")

    # Настройка файлового логирования
    log_file = os.path.join(logs_dir, 'playland.log')
    file_handler = RotatingFileHandler(log_file, maxBytes=10*1024*1024, backupCount=5)
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'))
    file_handler.setLevel(getattr(logging, log_level, logging.INFO))

    # Настройка консольного логирования
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s'))
    console_handler.setLevel(getattr(logging, log_level, logging.INFO))

    # Добавляем обработчики
    app.logger.addHandler(file_handler)
    app.logger.addHandler(console_handler)
    app.logger.setLevel(getattr(logging, log_level, logging.INFO))

    print(f"✅ Логирование настроено: {log_file} (уровень: {log_level})")
    app.logger.info('PlayLand startup')

    return app

# Для запуска через flask run или python -m flask run (если app.py)
# Если имя файла __init__.py, то flask run будет работать из папки website
# Либо, если вы запускаете как скрипт (не рекомендуется для продакшена):
if __name__ == '__main__':
    app = create_app()
    app.run(debug=True) # В продакшене используйте WSGI сервер (gunicorn, uwsgi)