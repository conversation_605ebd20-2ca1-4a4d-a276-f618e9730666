#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PlayLand Website Application Factory
Создает и настраивает Flask приложение с поддержкой синхронизации с Telegram ботом.
"""

from flask import Flask
from flask_login import LoginManager
import os
from datetime import timedelta

def create_app(config_name='development'):
    """Фабрика приложений Flask"""
    app = Flask(__name__)

    # Конфигурация приложения
    app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production')
    app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL', 'sqlite:///playland.db')
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(days=7)

    # Конфигурация для синхронизации с Telegram
    app.config['SYNC_SECRET_KEY'] = os.environ.get('SYNC_SECRET_KEY', 'sync-secret-key-change-in-production')
    app.config['TELEGRAM_BOT_TOKEN'] = os.environ.get('TELEGRAM_BOT_TOKEN', '')

    # Инициализация расширений
    from .models import db
    db.init_app(app)

    # Настройка Flask-Login
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'Пожалуйста, войдите в систему для доступа к этой странице.'
    login_manager.login_message_category = 'info'

    @login_manager.user_loader
    def load_user(user_id):
        from .models import User
        return User.query.get(int(user_id))

    # Регистрация Blueprint'ов
    from .views import main
    from .auth import auth
    from .profile import profile
    from .admin import admin
    from .sync_api import sync_api

    app.register_blueprint(main)
    app.register_blueprint(auth, url_prefix='/auth')
    app.register_blueprint(profile, url_prefix='/profile')
    app.register_blueprint(admin, url_prefix='/admin')
    app.register_blueprint(sync_api, url_prefix='/api/sync')

    # Создание таблиц базы данных
    with app.app_context():
        db.create_all()

    return app















































 