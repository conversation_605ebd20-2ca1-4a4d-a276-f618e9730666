{% extends "base.html" %}

{% block title %}PLAYLAND - Часто задаваемые вопросы{% endblock %}

{% block namespace %}faq{% endblock %}

{% block extra_head %}
<style>
    .hero {
        background-image: url("{{ url_for('static', filename='images/grass_background.jpg') }}");
        background-size: cover;
        background-position: center center;
        background-attachment: fixed;
        height: 50vh;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        padding: 20px;
        padding-top: 100px;
        box-sizing: border-box;
        position: relative;
        overflow: hidden;
    }
    
    .hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.6);
        backdrop-filter: blur(2px);
        z-index: 1;
    }
    
    .hero-content {
        position: relative;
        z-index: 2;
        max-width: 900px;
        width: 100%;
    }
    
    .hero h1 {
        font-family: 'Press Start 2P', cursive;
        font-size: 3.5em;
        color: #00ff00;
        text-shadow: 2px 2px 0 #000000, 0 0 15px #00ff00;
        margin-bottom: 20px;
        line-height: 1.3;
        animation: textPopIn 0.8s cubic-bezier(0.68, -0.55, 0.27, 1.55) 0.5s forwards, heroGlow 2.5s infinite alternate 1s;
        opacity: 0;
        transform: scale(0.5);
    }
    
    @keyframes textPopIn {
        to {
            opacity: 1;
            transform: scale(1);
        }
    }
    
    @keyframes heroGlow {
        0% {
            text-shadow: 2px 2px 0 #000000, 0 0 5px rgba(0, 255, 0, 0.5); 
        }
        100% {
            text-shadow: 2px 2px 0 #000000, 0 0 15px #00ff00, 0 0 30px #00ff00, 0 0 40px #00ff00;
        }
    }

    .content-section {
        padding: 80px 20px;
        background-color: rgba(0, 0, 0, 0.8);
    }
    
    .content-section h2 {
        font-family: 'Press Start 2P', cursive;
        font-size: 2.2em;
        color: #00ff00;
        text-align: center;
        margin-bottom: 40px;
        text-shadow: 0 0 10px #00ff00;
    }
    
    .faq-container {
        max-width: 900px;
        margin: 0 auto;
    }
    
    .faq-item {
        background-color: rgba(0, 0, 0, 0.5);
        border: 2px solid #00ff00;
        border-radius: 10px;
        margin-bottom: 20px;
        overflow: hidden;
        box-shadow: 0 0 20px rgba(0, 255, 0, 0.1);
        transition: all 0.3s ease;
    }
    
    .faq-item:hover {
        box-shadow: 0 0 30px rgba(0, 255, 0, 0.3);
        transform: translateY(-5px);
    }
    
    .faq-question {
        padding: 20px;
        font-family: 'Press Start 2P', cursive;
        font-size: 1.1em;
        color: #00ff00;
        background-color: rgba(0, 50, 0, 0.3);
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        transition: all 0.3s ease;
    }
    
    .faq-question:hover {
        background-color: rgba(0, 70, 0, 0.4);
    }
    
    .faq-question::after {
        content: '+';
        font-size: 1.5em;
        transition: transform 0.3s ease;
    }
    
    .faq-question.active::after {
        transform: rotate(45deg);
    }
    
    .faq-answer {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.5s ease;
        padding: 0 20px;
        color: #f0f0f0;
        line-height: 1.6;
    }
    
    .faq-answer.show {
        max-height: 500px;
        padding: 20px;
    }
    
    .faq-answer p {
        margin-bottom: 15px;
    }
    
    .faq-answer p:last-child {
        margin-bottom: 0;
    }
    
    .faq-answer ul {
        padding-left: 20px;
        margin-bottom: 15px;
    }
    
    .faq-answer ul li {
        margin-bottom: 8px;
    }
    
    .faq-category {
        margin-bottom: 50px;
    }
    
    .category-title {
        font-family: 'Press Start 2P', cursive;
        font-size: 1.5em;
        color: #00ff00;
        margin-bottom: 25px;
        text-align: center;
        text-shadow: 0 0 8px rgba(0, 255, 0, 0.5);
    }
    
    .faq-search {
        margin-bottom: 40px;
        display: flex;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }
    
    .faq-search input {
        flex-grow: 1;
        padding: 15px;
        background-color: rgba(0, 0, 0, 0.7);
        border: 2px solid #00ff00;
        color: #f0f0f0;
        font-size: 1em;
        border-radius: 5px 0 0 5px;
        outline: none;
        box-shadow: inset 0 0 10px rgba(0, 255, 0, 0.1);
        transition: all 0.3s;
    }
    
    .faq-search input:focus {
        box-shadow: inset 0 0 10px rgba(0, 255, 0, 0.3), 0 0 15px rgba(0, 255, 0, 0.2);
    }
    
    .faq-search button {
        padding: 15px 25px;
        background-color: #00ff00;
        color: #000;
        border: none;
        font-family: 'Press Start 2P', cursive;
        font-size: 0.9em;
        cursor: pointer;
        border-radius: 0 5px 5px 0;
        transition: all 0.3s;
    }
    
    .faq-search button:hover {
        background-color: #00cc00;
    }
    
    .not-found {
        display: none;
        text-align: center;
        padding: 30px;
        color: #f0f0f0;
        font-size: 1.2em;
    }
    
    .not-found.show {
        display: block;
    }
    
    .more-questions {
        text-align: center;
        margin-top: 60px;
        padding: 30px;
        background-color: rgba(0, 0, 0, 0.5);
        border: 2px solid #00ff00;
        border-radius: 10px;
    }
    
    .more-questions h3 {
        font-family: 'Press Start 2P', cursive;
        font-size: 1.3em;
        color: #00ff00;
        margin-bottom: 20px;
    }
    
    .more-questions p {
        color: #f0f0f0;
        margin-bottom: 25px;
    }
    
    .btn-minecraft {
        display: inline-block;
        padding: 15px 30px;
        background-color: #00ff00;
        color: #000;
        text-decoration: none;
        font-family: 'Press Start 2P', cursive;
        font-size: 0.9em;
        text-transform: uppercase;
        border: none;
        position: relative;
        overflow: hidden;
        transition: all 0.3s;
        box-shadow: 0 4px 0 #006400, 0 0 20px rgba(0, 255, 0, 0.4);
        border-radius: 3px;
    }

    .btn-minecraft:hover {
        transform: translateY(-5px);
        box-shadow: 0 9px 0 #006400, 0 0 30px rgba(0, 255, 0, 0.6);
    }

    @media (max-width: 768px) {
        .hero h1 {
            font-size: 2em;
        }
        
        .content-section h2 {
            font-size: 1.5em;
        }
        
        .faq-question {
            font-size: 0.9em;
            padding: 15px;
        }
        
        .category-title {
            font-size: 1.2em;
        }
        
        .faq-search {
            flex-direction: column;
        }
        
        .faq-search input {
            border-radius: 5px;
            margin-bottom: 10px;
        }
        
        .faq-search button {
            border-radius: 5px;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="hero">
    <div class="particles-container" id="particles"></div>
    <div class="hero-content">
        <h1 class="animate-fade-in-up">ЧАСТО ЗАДАВАЕМЫЕ ВОПРОСЫ</h1>
    </div>
</section>

<section class="content-section">
    <h2 class="animate-fade-in">Ответы на ваши вопросы</h2>
    
    <div class="faq-container">
        <div class="faq-search animate-fade-in">
            <input type="text" id="faq-search-input" placeholder="Введите ваш вопрос...">
            <button id="faq-search-button">Поиск</button>
        </div>
        
        <div id="not-found-message" class="not-found">
            Ничего не найдено по вашему запросу. Попробуйте другие ключевые слова.
        </div>
        
        <div class="faq-category animate-fade-in-up">
            <h3 class="category-title">Основная информация</h3>
            
            <div class="faq-item" data-keywords="сервер ip адрес версия подключиться">
                <div class="faq-question">Как подключиться к серверу?</div>
                <div class="faq-answer">
                    <p>Для подключения к серверу вам понадобится лицензионная копия Minecraft Java Edition версии 1.21.1.</p>
                    <p>IP адрес сервера: <strong>mc.playland.ru</strong></p>
                    <p>Важно: ваш аккаунт должен быть одобрен администрацией. Для этого необходимо подать заявку на вступление через наш сайт.</p>
                </div>
            </div>
            
            <div class="faq-item" data-keywords="заявка вступить форма регистрация">
                <div class="faq-question">Как подать заявку на вступление?</div>
                <div class="faq-answer">
                    <p>Чтобы подать заявку на вступление:</p>
                    <ol>
                        <li>Зарегистрируйтесь на нашем сайте</li>
                        <li>Перейдите в раздел "Подать заявку"</li>
                        <li>Заполните анкету, указав свой никнейм, возраст и отвечая на вопросы</li>
                        <li>Отправьте заявку и ожидайте решения администрации</li>
                    </ol>
                    <p>Рассмотрение заявки обычно занимает не более 24 часов.</p>
                </div>
            </div>
            
            <div class="faq-item" data-keywords="версия игры поддерживаемые версии перейти 1.21.1">
                <div class="faq-question">Какие версии Minecraft поддерживаются?</div>
                <div class="faq-answer">
                    <p>Наш сервер работает на версии Minecraft 1.21.1.</p>
                    <p>Благодаря плагинам ViaVersion, ViaBackwards и ViaRewind вы можете подключаться с версий от 1.8 до 1.21.1, но рекомендуется использовать именно версию 1.21.1 для полноценной игры и доступа ко всем функциям.</p>
                </div>
            </div>
        </div>
        
        <div class="faq-category animate-fade-in-up" style="animation-delay: 0.2s;">
            <h3 class="category-title">Игровой процесс</h3>
            
            <div class="faq-item" data-keywords="плагины моды функции список">
                <div class="faq-question">Какие плагины есть на сервере?</div>
                <div class="faq-answer">
                    <p>На нашем сервере установлены различные плагины для улучшения игрового процесса:</p>
                    <ul>
                        <li>WorldEdit - удобное редактирование мира</li>
                        <li>Essentials - базовые команды и возможности</li>
                        <li>SimpleVoiceChat - голосовой чат в игре</li>
                        <li>LuckPerms - система прав и рангов</li>
                        <li>DiscordSRV - интеграция с Discord</li>
                        <li>Dynmap - интерактивная карта мира</li>
                        <li>BetterSleeping - улучшенный сон (нужно меньше игроков для пропуска ночи)</li>
                        <li>CoreProtect - защита от гриферов</li>
                    </ul>
                    <p>И многие другие.</p>
                </div>
            </div>
            
            <div class="faq-item" data-keywords="пвп pvp убийства разрешено сражения">
                <div class="faq-question">Разрешено ли PvP на сервере?</div>
                <div class="faq-answer">
                    <p>PvP (игрок против игрока) разрешено только по взаимному согласию обеих сторон. Атаковать игроков без их согласия запрещено правилами сервера и карается наказанием.</p>
                    <p>В будущем планируется создание специальных арен для PvP сражений.</p>
                </div>
            </div>
            
            <div class="faq-item" data-keywords="читы хаки бан наказания">
                <div class="faq-question">Можно ли использовать читы или хаки?</div>
                <div class="faq-answer">
                    <p>Использование читов, хаков и любых модификаций, дающих нечестное преимущество в игре, <strong>строго запрещено</strong>.</p>
                    <p>Нарушение этого правила приводит к немедленному и, как правило, постоянному бану.</p>
                    <p>Разрешены только клиентские модификации, не влияющие на игровой баланс: оптифайн, шейдеры, миникарты без отображения сущностей и пещер, и другие косметические моды.</p>
                </div>
            </div>
        </div>
        
        <div class="faq-category animate-fade-in-up" style="animation-delay: 0.4s;">
            <h3 class="category-title">Технические вопросы</h3>
            
            <div class="faq-item" data-keywords="лаги производительность фпс fps задержка">
                <div class="faq-question">Почему у меня лаги на сервере?</div>
                <div class="faq-answer">
                    <p>Лаги могут возникать по разным причинам. Прежде всего, проверьте следующее:</p>
                    <ul>
                        <li>Ваше подключение к интернету - проверьте скорость и пинг</li>
                        <li>Производительность вашего компьютера - попробуйте снизить настройки графики</li>
                        <li>Установленные моды - некоторые из них могут вызывать конфликты</li>
                    </ul>
                    <p>Если проблема не в клиенте, а в серверных лагах (тиках), сообщите об этом администрации в Discord.</p>
                </div>
            </div>
            
            <div class="faq-item" data-keywords="модпак сборка установить">
                <div class="faq-question">Есть ли рекомендуемая сборка модов для сервера?</div>
                <div class="faq-answer">
                    <p>У нас нет официального модпака, поскольку сервер является ванильным с плагинами.</p>
                    <p>Однако мы рекомендуем следующие клиентские моды для улучшения игрового опыта:</p>
                    <ul>
                        <li>Sodium/Iris/Oculus (альтернатива Optifine) - для повышения производительности</li>
                        <li>Simple Voice Chat - для голосового общения на сервере</li>
                        <li>JourneyMap или Xaero's Minimap - для навигации (без функции отображения пещер)</li>
                    </ul>
                </div>
            </div>
            
            <div class="faq-item" data-keywords="вылет краш ошибка подключение">
                <div class="faq-question">Что делать, если я не могу подключиться к серверу?</div>
                <div class="faq-answer">
                    <p>Если у вас возникли проблемы с подключением, выполните следующие шаги:</p>
                    <ol>
                        <li>Убедитесь, что вы используете правильный IP: <strong>mc.playland.ru</strong></li>
                        <li>Проверьте, что ваша заявка на вступление была одобрена</li>
                        <li>Убедитесь, что ваш Minecraft обновлен до актуальной версии (1.21.1)</li>
                        <li>Попробуйте отключить антивирус или файрвол временно</li>
                        <li>Перезапустите игру и маршрутизатор</li>
                    </ol>
                    <p>Если проблема не решена, обратитесь за помощью в наш Discord сервер, предоставив скриншот ошибки.</p>
                </div>
            </div>
        </div>
        
        <div class="more-questions animate-fade-in-up" style="animation-delay: 0.6s;">
            <h3>Не нашли ответ на свой вопрос?</h3>
            <p>Присоединяйтесь к нашему Discord серверу, где вы можете общаться с другими игроками и задать вопрос напрямую администрации.</p>
            <a href="https://discord.gg/49YG5eNXKJ" target="_blank" class="btn-minecraft">Наш Discord</a>
        </div>
    </div>
</section>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Частицы
        const particlesContainer = document.getElementById('particles');
        if (particlesContainer) {
            const colors = [
                'rgba(0, 255, 0, 0.4)',
                'rgba(50, 205, 50, 0.4)',
                'rgba(173, 255, 47, 0.4)',
                'rgba(152, 251, 152, 0.4)'
            ];
            
            for (let i = 0; i < 50; i++) {
                const size = Math.random() * 6 + 3;
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.width = `${size}px`;
                particle.style.height = `${size}px`;
                particle.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
                particle.style.left = `${Math.random() * 100}%`;
                particle.style.setProperty('--rand-x', Math.random());
                particle.style.boxShadow = `0 0 ${size}px ${particle.style.backgroundColor}`;
                particle.style.animationDuration = `${Math.random() * 20 + 10}s`;
                particle.style.animationDelay = `${Math.random() * 5}s`;
                
                particlesContainer.appendChild(particle);
            }
        }
        
        // FAQ аккордеон
        const faqQuestions = document.querySelectorAll('.faq-question');
        
        faqQuestions.forEach(question => {
            question.addEventListener('click', function() {
                this.classList.toggle('active');
                const answer = this.nextElementSibling;
                
                if (this.classList.contains('active')) {
                    answer.classList.add('show');
                } else {
                    answer.classList.remove('show');
                }
            });
        });
        
        // Функционал поиска
        const searchInput = document.getElementById('faq-search-input');
        const searchButton = document.getElementById('faq-search-button');
        const faqItems = document.querySelectorAll('.faq-item');
        const notFoundMessage = document.getElementById('not-found-message');
        
        function performSearch() {
            const searchTerm = searchInput.value.toLowerCase().trim();
            
            if (searchTerm === '') {
                // Показать все вопросы, если поисковый запрос пуст
                faqItems.forEach(item => item.style.display = 'block');
                notFoundMessage.classList.remove('show');
                return;
            }
            
            let found = false;
            
            faqItems.forEach(item => {
                const keywords = item.getAttribute('data-keywords').toLowerCase();
                const question = item.querySelector('.faq-question').textContent.toLowerCase();
                const answer = item.querySelector('.faq-answer').textContent.toLowerCase();
                
                if (keywords.includes(searchTerm) || question.includes(searchTerm) || answer.includes(searchTerm)) {
                    item.style.display = 'block';
                    found = true;
                    
                    // Автоматически открывать найденные вопросы
                    const questionElement = item.querySelector('.faq-question');
                    const answerElement = item.querySelector('.faq-answer');
                    
                    questionElement.classList.add('active');
                    answerElement.classList.add('show');
                } else {
                    item.style.display = 'none';
                }
            });
            
            if (!found) {
                notFoundMessage.classList.add('show');
            } else {
                notFoundMessage.classList.remove('show');
            }
        }
        
        searchButton.addEventListener('click', performSearch);
        
        searchInput.addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                performSearch();
            }
        });
    });
</script>
{% endblock %} 