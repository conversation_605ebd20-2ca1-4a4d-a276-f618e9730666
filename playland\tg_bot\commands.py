#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Модуль с командами Telegram-бота для сервера PlayLand
"""

import os
import sys
import json
import logging
import requests
from datetime import datetime
import telebot
from telebot import types
from mcstatus import JavaServer
import re

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('telebot.log')
    ]
)
logger = logging.getLogger(__name__)

# Константы для сервера Minecraft
MC_SERVER_HOST = os.environ.get('MC_SERVER_HOST', 'localhost')
MC_SERVER_PORT = int(os.environ.get('MC_SERVER_PORT', 25565))
MC_RCON_PASSWORD = os.environ.get('MC_RCON_PASSWORD', '')

# API URL
API_BASE_URL = os.environ.get('API_BASE_URL', 'http://localhost:5000/api')
API_SECRET_KEY = os.environ.get('API_SECRET_KEY', '')

# Путь к файлу с изображением карты сервера
SERVER_MAP_IMAGE = os.environ.get('SERVER_MAP_IMAGE', 'static/images/map.png')


def register_commands(bot):
    """
    Регистрирует все команды бота
    
    Args:
        bot: Экземпляр telebot.TeleBot
    """
    # Базовые команды
    @bot.message_handler(commands=['start'])
    def cmd_start(message):
        """Обработчик команды /start"""
        # Создаем клавиатуру
        markup = types.ReplyKeyboardMarkup(resize_keyboard=True, row_width=2)
        btn_help = types.KeyboardButton('❓ Помощь')
        btn_status = types.KeyboardButton('📊 Статус сервера')
        btn_register = types.KeyboardButton('📝 Подать заявку')
        btn_map = types.KeyboardButton('🗺️ Карта сервера')
        btn_rules = types.KeyboardButton('📜 Правила')
        btn_link = types.KeyboardButton('🔗 Связать аккаунт')
        
        markup.add(btn_status, btn_map, btn_register, btn_rules, btn_help, btn_link)
        
        # Отправляем приветственное сообщение
        bot.send_message(
            message.chat.id,
            f"👋 Привет, {message.from_user.first_name}!\n\n"
            f"Добро пожаловать в бота сервера PlayLand. "
            f"Через этого бота ты можешь узнать статус сервера, подать заявку на вайтлист, "
            f"получать уведомления и многое другое.\n\n"
            f"Используй команду /help, чтобы узнать больше о доступных командах.",
            reply_markup=markup
        )
    
    @bot.message_handler(commands=['help'])
    def cmd_help(message):
        """Обработчик команды /help"""
        help_text = (
            "🤖 *Команды бота PlayLand*\n\n"
            "/start - Начать взаимодействие с ботом\n"
            "/help - Показать список команд\n"
            "/status - Проверить статус сервера\n"
            "/map - Показать карту сервера\n"
            "/register - Подать заявку на вайтлист\n"
            "/rules - Правила сервера\n"
            "/link - Связать аккаунт сайта с Telegram\n"
            "/online - Список игроков онлайн\n"
            "/tps - Показать TPS сервера\n"
            "/uptime - Время работы сервера\n"
            "/donate - Информация о донате\n\n"
            "Также вы можете воспользоваться кнопками внизу экрана для быстрого доступа к функциям."
        )
        
        bot.send_message(message.chat.id, help_text, parse_mode='Markdown')
    
    # Команды для управления сервером
    @bot.message_handler(commands=['status'])
    def cmd_status(message):
        """Обработчик команды /status - показывает статус сервера"""
        bot.send_chat_action(message.chat.id, 'typing')
        
        try:
            # Получаем информацию о сервере
            server = JavaServer(MC_SERVER_HOST, MC_SERVER_PORT)
            status = server.status()
            
            # Формируем сообщение
            online_count = status.players.online
            max_players = status.players.max
            version = status.version.name
            
            # MOTD (может содержать форматирование, которое нужно очистить)
            motd = re.sub(r'§[0-9a-fk-or]', '', status.description)
            
            response = (
                f"🎮 *Статус сервера PlayLand*\n\n"
                f"✅ *Сервер онлайн*\n"
                f"👥 Игроков: *{online_count}/{max_players}*\n"
                f"🔄 Версия: *{version}*\n"
                f"📶 Пинг: *{int(status.latency)}ms*\n"
                f"💬 MOTD: _{motd}_\n\n"
                f"IP: `mc.playland.ru`"
            )
            
            # Дополнительная информация через RCON, если доступно
            if MC_RCON_PASSWORD:
                try:
                    with server.rcon(MC_RCON_PASSWORD) as rcon:
                        # Получаем TPS
                        tps_response = rcon.command("tps")
                        # Парсим TPS из ответа
                        tps_match = re.search(r'TPS: (\d+\.?\d*)', tps_response)
                        if tps_match:
                            tps = tps_match.group(1)
                            response += f"\n⚡ TPS: *{tps}*"
                except Exception as e:
                    logger.error(f"Ошибка RCON: {str(e)}")
            
            # Отправляем сообщение
            bot.send_message(message.chat.id, response, parse_mode='Markdown')
            
        except Exception as e:
            logger.error(f"Ошибка при получении статуса сервера: {str(e)}")
            bot.send_message(
                message.chat.id,
                "❌ *Сервер недоступен*\n\nВозможно, сервер выключен или проводятся технические работы. Попробуйте позже.",
                parse_mode='Markdown'
            )
    
    @bot.message_handler(commands=['online'])
    def cmd_online(message):
        """Обработчик команды /online - показывает список игроков онлайн"""
        bot.send_chat_action(message.chat.id, 'typing')
        
        try:
            # Получаем информацию о сервере
            server = JavaServer(MC_SERVER_HOST, MC_SERVER_PORT)
            status = server.status()
            
            online_count = status.players.online
            max_players = status.players.max
            
            if online_count == 0:
                bot.send_message(
                    message.chat.id,
                    "😢 *На сервере нет игроков*\n\nСтань первым!",
                    parse_mode='Markdown'
                )
                return
            
            # Пытаемся получить список игроков
            try:
                player_list = status.players.sample
                if player_list:
                    player_names = [player.name for player in player_list]
                    players_text = "\n".join([f"👤 *{name}*" for name in player_names])
                    
                    response = (
                        f"👥 *Игроки онлайн: {online_count}/{max_players}*\n\n"
                        f"{players_text}\n\n"
                        f"Присоединяйтесь!\nIP: `mc.playland.ru`"
                    )
                else:
                    response = (
                        f"👥 *Игроки онлайн: {online_count}/{max_players}*\n\n"
                        f"Не удалось получить список игроков.\n\n"
                        f"Присоединяйтесь!\nIP: `mc.playland.ru`"
                    )
            except AttributeError:
                response = (
                    f"👥 *Игроки онлайн: {online_count}/{max_players}*\n\n"
                    f"Не удалось получить список игроков.\n\n"
                    f"Присоединяйтесь!\nIP: `mc.playland.ru`"
                )
                
            # Отправляем сообщение
            bot.send_message(message.chat.id, response, parse_mode='Markdown')
            
        except Exception as e:
            logger.error(f"Ошибка при получении списка игроков: {str(e)}")
            bot.send_message(
                message.chat.id,
                "❌ *Не удалось получить список игроков*\n\nВозможно, сервер выключен или проводятся технические работы.",
                parse_mode='Markdown'
            )
    
    @bot.message_handler(commands=['map'])
    def cmd_map(message):
        """Обработчик команды /map - показывает карту сервера"""
        if os.path.exists(SERVER_MAP_IMAGE):
            with open(SERVER_MAP_IMAGE, 'rb') as photo:
                bot.send_photo(
                    message.chat.id,
                    photo,
                    caption="🗺️ *Карта сервера PlayLand*\n\nДля интерактивной карты посетите сайт: https://map.playland.ru",
                    parse_mode='Markdown'
                )
        else:
            bot.send_message(
                message.chat.id,
                "🗺️ *Карта сервера PlayLand*\n\nДля просмотра интерактивной карты посетите сайт: https://map.playland.ru",
                parse_mode='Markdown'
            )
    
    # Регистрация на сервере
    @bot.message_handler(commands=['register'])
    def cmd_register(message):
        """Обработчик команды /register - начинает процесс подачи заявки"""
        # Создаем инлайн-клавиатуру
        markup = types.InlineKeyboardMarkup()
        btn_website = types.InlineKeyboardButton('🌐 Через сайт', url='https://playland.ru/application')
        btn_telegram = types.InlineKeyboardButton('📱 Через Telegram', callback_data='register_telegram')
        markup.add(btn_website, btn_telegram)
        
        bot.send_message(
            message.chat.id,
            "📝 *Подача заявки на вайтлист*\n\n"
            "Для игры на нашем сервере необходимо пройти вайтлист.\n"
            "Вы можете подать заявку через сайт или прямо здесь, в Telegram.\n\n"
            "Выберите удобный способ подачи заявки:",
            parse_mode='Markdown',
            reply_markup=markup
        )
    
    # Обработчик нажатия на кнопки
    @bot.callback_query_handler(func=lambda call: True)
    def handle_callback_query(call):
        """Обработчик нажатия на инлайн-кнопки"""
        if call.data == 'register_telegram':
            # Сохраняем состояние - начинаем процесс регистрации
            bot.delete_message(call.message.chat.id, call.message.message_id)
            
            # Отправляем первый вопрос - никнейм
            msg = bot.send_message(
                call.message.chat.id,
                "👤 Пожалуйста, укажите ваш *Minecraft никнейм*:",
                parse_mode='Markdown'
            )
            
            # Регистрируем следующий шаг - ввод никнейма
            bot.register_next_step_handler(msg, process_nickname_step)
    
    # Функции для обработки шагов регистрации
    def process_nickname_step(message):
        """Обрабатывает ввод никнейма и запрашивает Discord"""
        # Валидация никнейма
        nickname = message.text.strip()
        if not re.match(r'^[A-Za-z0-9_]{3,16}$', nickname):
            msg = bot.send_message(
                message.chat.id,
                "❌ *Некорректный никнейм*\n\n"
                "Никнейм должен содержать только латинские буквы, цифры и подчеркивания, "
                "и быть длиной от 3 до 16 символов.\n\n"
                "Пожалуйста, введите корректный никнейм:",
                parse_mode='Markdown'
            )
            bot.register_next_step_handler(msg, process_nickname_step)
            return
        
        # Сохраняем никнейм в context бота
        user_id = message.from_user.id
        
        # Запрашиваем Discord
        msg = bot.send_message(
            message.chat.id,
            "🔖 Укажите ваш *Discord tag* (например, username#1234 или просто username):",
            parse_mode='Markdown'
        )
        
        # Регистрируем следующий шаг
        bot.register_next_step_handler(msg, process_discord_step, {"nickname": nickname})
    
    def process_discord_step(message, user_data):
        """Обрабатывает ввод Discord и запрашивает возраст"""
        discord = message.text.strip()
        user_data["discord"] = discord
        
        # Запрашиваем возраст
        msg = bot.send_message(
            message.chat.id,
            "👶 Укажите ваш *возраст* (цифрами):",
            parse_mode='Markdown'
        )
        
        # Регистрируем следующий шаг
        bot.register_next_step_handler(msg, process_age_step, user_data)
    
    def process_age_step(message, user_data):
        """Обрабатывает ввод возраста и запрашивает информацию о себе"""
        # Валидация возраста
        age = message.text.strip()
        if not age.isdigit() or int(age) < 8 or int(age) > 100:
            msg = bot.send_message(
                message.chat.id,
                "❌ *Некорректный возраст*\n\n"
                "Пожалуйста, введите корректный возраст (от 8 до 100 лет):",
                parse_mode='Markdown'
            )
            bot.register_next_step_handler(msg, process_age_step, user_data)
            return
        
        user_data["age"] = age
        
        # Запрашиваем информацию о себе
        msg = bot.send_message(
            message.chat.id,
            "ℹ️ Расскажите *немного о себе*:",
            parse_mode='Markdown'
        )
        
        # Регистрируем следующий шаг
        bot.register_next_step_handler(msg, process_about_step, user_data)
    
    def process_about_step(message, user_data):
        """Обрабатывает ввод информации о себе и запрашивает опыт игры"""
        about = message.text.strip()
        user_data["about"] = about
        
        # Запрашиваем опыт игры
        msg = bot.send_message(
            message.chat.id,
            "👨‍💻 Расскажите о своем *опыте игры в Minecraft*:",
            parse_mode='Markdown'
        )
        
        # Регистрируем следующий шаг
        bot.register_next_step_handler(msg, process_experience_step, user_data)
    
    def process_experience_step(message, user_data):
        """Обрабатывает ввод опыта игры и запрашивает как нашел сервер"""
        experience = message.text.strip()
        user_data["experience"] = experience
        
        # Запрашиваем как нашел сервер
        msg = bot.send_message(
            message.chat.id,
            "🔍 Как вы *нашли наш сервер*?",
            parse_mode='Markdown'
        )
        
        # Регистрируем следующий шаг
        bot.register_next_step_handler(msg, process_found_step, user_data)
    
    def process_found_step(message, user_data):
        """Обрабатывает ввод информации о том, как нашел сервер, и запрашивает подтверждение"""
        found = message.text.strip()
        user_data["found"] = found
        
        # Формируем сводку данных для подтверждения
        summary = (
            "📋 *Проверьте данные вашей заявки:*\n\n"
            f"👤 Никнейм: *{user_data['nickname']}*\n"
            f"🔖 Discord: *{user_data['discord']}*\n"
            f"👶 Возраст: *{user_data['age']}*\n"
            f"ℹ️ О себе: _{user_data['about']}_\n"
            f"👨‍💻 Опыт игры: _{user_data['experience']}_\n"
            f"🔍 Как нашли сервер: _{user_data['found']}_\n\n"
            "Всё верно? Если да, нажмите 'Отправить заявку'."
        )
        
        # Создаем инлайн-клавиатуру для подтверждения
        markup = types.InlineKeyboardMarkup()
        btn_submit = types.InlineKeyboardButton('✅ Отправить заявку', callback_data='submit_application')
        btn_cancel = types.InlineKeyboardButton('❌ Отменить', callback_data='cancel_application')
        markup.add(btn_submit, btn_cancel)
        
        # Отправляем сводку с кнопками
        bot.send_message(
            message.chat.id,
            summary,
            parse_mode='Markdown',
            reply_markup=markup
        )
        
        # Сохраняем данные пользователя
        # В реальном приложении здесь должен быть код для сохранения данных
    
    # Регистрируем обработчики текстовых сообщений (кнопки клавиатуры)
    @bot.message_handler(func=lambda message: message.text == '📊 Статус сервера')
    def text_status(message):
        """Обработчик кнопки 'Статус сервера'"""
        cmd_status(message)
    
    @bot.message_handler(func=lambda message: message.text == '❓ Помощь')
    def text_help(message):
        """Обработчик кнопки 'Помощь'"""
        cmd_help(message)
    
    @bot.message_handler(func=lambda message: message.text == '📝 Подать заявку')
    def text_register(message):
        """Обработчик кнопки 'Подать заявку'"""
        cmd_register(message)
    
    @bot.message_handler(func=lambda message: message.text == '🗺️ Карта сервера')
    def text_map(message):
        """Обработчик кнопки 'Карта сервера'"""
        cmd_map(message)
    
    @bot.message_handler(func=lambda message: message.text == '📜 Правила')
    def text_rules(message):
        """Обработчик кнопки 'Правила'"""
        rules_text = (
            "📜 *Правила сервера PlayLand*\n\n"
            "1️⃣ Уважайте других игроков\n"
            "2️⃣ Запрещен гриферинг и воровство\n"
            "3️⃣ Запрещено использование читов и эксплоитов\n"
            "4️⃣ Запрещена реклама\n"
            "5️⃣ Соблюдайте указания администрации\n\n"
            "Полный список правил доступен на сайте:\n"
            "https://playland.ru/rules"
        )
        
        bot.send_message(message.chat.id, rules_text, parse_mode='Markdown')
    
    @bot.message_handler(func=lambda message: message.text == '🔗 Связать аккаунт')
    def text_link(message):
        """Обработчик кнопки 'Связать аккаунт'"""
        # Генерируем уникальный код для связывания
        link_code = generate_link_code(message.from_user.id)
        
        link_text = (
            "🔗 *Связывание аккаунта*\n\n"
            f"Ваш код для связывания: `{link_code}`\n\n"
            "Для связывания аккаунта сайта с Telegram выполните следующие шаги:\n"
            "1. Войдите на сайт https://playland.ru\n"
            "2. Перейдите в личный кабинет\n"
            "3. Выберите раздел 'Связать с Telegram'\n"
            "4. Введите код, указанный выше\n\n"
            "Код действителен в течение 10 минут."
        )
        
        bot.send_message(message.chat.id, link_text, parse_mode='Markdown')
    
    # Вспомогательная функция для генерации кода связывания
    def generate_link_code(user_id):
        """Генерирует код для связывания аккаунтов и сохраняет его"""
        import random
        import string
        import hashlib
        import time
        
        # Генерируем случайный код
        code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))
        
        # Время истечения кода (10 минут)
        expires_at = int(time.time()) + 600
        
        # Хешируем данные для API
        data = {
            'telegram_id': user_id,
            'link_code': code,
            'expires_at': expires_at
        }
        
        try:
            # Отправляем данные на API сервера
            headers = {'Content-Type': 'application/json'}
            if API_SECRET_KEY:
                # Генерируем HMAC подпись
                signature = generate_hmac(data)
                headers['X-API-Signature'] = signature
                
            response = requests.post(
                f"{API_BASE_URL}/auth/telegram/link",
                json=data,
                headers=headers
            )
            
            if response.status_code == 200:
                logger.info(f"Код связывания создан для пользователя {user_id}: {code}")
            else:
                logger.error(f"Ошибка создания кода связывания: {response.text}")
        except Exception as e:
            logger.error(f"Ошибка при отправке кода связывания: {str(e)}")
        
        return code
    
    # Вспомогательная функция для генерации HMAC подписи
    def generate_hmac(data):
        """Генерирует HMAC подпись для запроса к API"""
        import hmac
        import hashlib
        import json
        
        if not API_SECRET_KEY:
            return None
            
        return hmac.new(
            API_SECRET_KEY.encode('utf-8'),
            json.dumps(data).encode('utf-8'),
            hashlib.sha256
        ).hexdigest() 

@bot.message_handler(commands=['stats', 'статистика'])
def cmd_stats(message):
    """
    Получение статистики игрока
    Использование: /stats <ник_игрока>
    Если ник не указан, показывает общую статистику сервера
    """
    try:
        args = message.text.split()[1:] if len(message.text.split()) > 1 else []
        
        # Отправляем сообщение о начале получения статистики
        wait_message = bot.reply_to(message, "⏳ Получаю статистику...")
        
        if len(args) > 0:
            # Получаем статистику конкретного игрока
            player_name = args[0]
            response = get_player_stats(player_name)
            
            if response.get('success'):
                stats = response.get('stats', {})
                
                # Формируем красивое сообщение со статистикой
                reply_text = f"📊 *Статистика игрока {player_name}*\n\n"
                
                # Основная информация
                if 'online' in stats:
                    status = "🟢 Онлайн" if stats['online'] else "🔴 Оффлайн"
                    reply_text += f"*Статус*: {status}\n"
                
                if 'playtime' in stats:
                    reply_text += f"*Время игры*: {stats['playtime']} часов\n"
                
                if 'last_seen' in stats:
                    reply_text += f"*Последний вход*: {stats['last_seen']}\n"
                
                # Игровая статистика
                reply_text += "\n*Игровая статистика*:\n"
                
                if 'kills' in stats:
                    reply_text += f"⚔️ Убийства: {stats['kills']}\n"
                
                if 'deaths' in stats:
                    reply_text += f"💀 Смерти: {stats['deaths']}\n"
                
                if 'blocks_placed' in stats:
                    reply_text += f"🧱 Блоков поставлено: {stats['blocks_placed']}\n"
                
                if 'blocks_broken' in stats:
                    reply_text += f"⛏️ Блоков сломано: {stats['blocks_broken']}\n"
                
                # Достижения
                if 'achievements' in stats and stats['achievements']:
                    reply_text += "\n*Последние достижения*:\n"
                    for achievement in stats['achievements'][:3]:  # Показываем только 3 последних достижения
                        reply_text += f"🏆 {achievement}\n"
                
                # Редактируем исходное сообщение об ожидании
                bot.edit_message_text(
                    chat_id=message.chat.id,
                    message_id=wait_message.message_id,
                    text=reply_text,
                    parse_mode="Markdown"
                )
            else:
                # Если игрок не найден или другая ошибка
                bot.edit_message_text(
                    chat_id=message.chat.id,
                    message_id=wait_message.message_id,
                    text=f"❌ Ошибка: {response.get('error', 'Не удалось получить статистику игрока')}"
                )
        else:
            # Получаем общую статистику сервера
            response = get_server_stats()
            
            if response.get('success'):
                stats = response.get('stats', {})
                
                # Формируем красивое сообщение с общей статистикой
                reply_text = "📊 *Общая статистика сервера*\n\n"
                
                if 'total_players' in stats:
                    reply_text += f"👥 Всего игроков: {stats['total_players']}\n"
                
                if 'online_players' in stats:
                    reply_text += f"🟢 Онлайн сейчас: {stats['online_players']}\n"
                
                if 'uptime' in stats:
                    reply_text += f"⏱️ Аптайм сервера: {stats['uptime']}\n"
                
                if 'tps' in stats:
                    tps = stats['tps']
                    tps_emoji = "🟢" if tps >= 19 else "🟡" if tps >= 15 else "🔴"
                    reply_text += f"{tps_emoji} TPS: {tps}\n"
                
                # Топ игроков по времени
                if 'top_players' in stats and stats['top_players']:
                    reply_text += "\n*Топ игроков по времени*:\n"
                    for i, player in enumerate(stats['top_players'][:5]):  # Топ-5 игроков
                        reply_text += f"{i+1}. {player['name']} — {player['playtime']} часов\n"
                
                # Последние события
                if 'recent_events' in stats and stats['recent_events']:
                    reply_text += "\n*Последние события на сервере*:\n"
                    for event in stats['recent_events'][:3]:  # 3 последних события
                        reply_text += f"• {event}\n"
                
                # Редактируем исходное сообщение об ожидании
                bot.edit_message_text(
                    chat_id=message.chat.id,
                    message_id=wait_message.message_id,
                    text=reply_text,
                    parse_mode="Markdown"
                )
            else:
                # Если возникла ошибка при получении статистики
                bot.edit_message_text(
                    chat_id=message.chat.id,
                    message_id=wait_message.message_id,
                    text=f"❌ Ошибка: {response.get('error', 'Не удалось получить статистику сервера')}"
                )
    except Exception as e:
        logger.error(f"Ошибка при выполнении команды /stats: {str(e)}")
        bot.reply_to(message, f"❌ Произошла ошибка: {str(e)}")

@bot.message_handler(commands=['vote', 'голосование'])
def cmd_vote(message):
    """
    Создание голосования
    Использование: /vote <вопрос> | <вариант1> | <вариант2> | ...
    Пример: /vote Какой биом лучше? | Лес | Пустыня | Тайга | Горы
    """
    try:
        # Получаем аргументы команды
        args = message.text.split(' ', 1)
        
        if len(args) < 2:
            # Если аргументы не указаны, отправляем инструкцию
            bot.reply_to(message, 
                "ℹ️ *Использование команды:*\n"
                "/vote <вопрос> | <вариант1> | <вариант2> | ...\n\n"
                "*Пример:* /vote Какой биом лучше? | Лес | Пустыня | Тайга | Горы",
                parse_mode="Markdown"
            )
            return
        
        # Разбиваем на вопрос и варианты ответов
        parts = args[1].split('|')
        
        # Проверяем, что есть вопрос и хотя бы 2 варианта ответа
        if len(parts) < 3:
            bot.reply_to(message, 
                "❌ Необходимо указать вопрос и как минимум 2 варианта ответа, разделенных символом |",
                parse_mode="Markdown"
            )
            return
        
        # Получаем вопрос и варианты ответов
        question = parts[0].strip()
        options = [opt.strip() for opt in parts[1:] if opt.strip()]
        
        # Проверяем количество вариантов ответа (Telegram поддерживает до 10 вариантов)
        if len(options) > 10:
            bot.reply_to(message, 
                "❌ Максимальное количество вариантов ответа: 10",
                parse_mode="Markdown"
            )
            return
        
        # Отправляем опрос
        bot.send_poll(
            chat_id=message.chat.id,
            question=question,
            options=options,
            is_anonymous=False  # Чтобы видеть, кто как проголосовал
        )
        
        # Отправляем также голосование в Discord, если настроена интеграция
        try:
            create_discord_poll(question, options, f"Голосование создано пользователем {message.from_user.username or message.from_user.first_name} в Telegram")
        except Exception as e:
            logger.error(f"Ошибка при отправке голосования в Discord: {str(e)}")
    
    except Exception as e:
        logger.error(f"Ошибка при выполнении команды /vote: {str(e)}")
        bot.reply_to(message, f"❌ Произошла ошибка: {str(e)}")

def get_player_stats(player_name):
    """
    Получает статистику игрока с сервера
    
    Args:
        player_name (str): Ник игрока
        
    Returns:
        dict: Статистика игрока или сообщение об ошибке
    """
    try:
        # Формируем URL для запроса к API
        url = f"{API_BASE_URL}/minecraft/player/{player_name}/stats"
        
        # Заголовки для запроса
        headers = {"X-API-Key": API_SECRET_KEY}
        
        # Отправляем запрос
        response = requests.get(url, headers=headers)
        
        # Проверяем статус ответа
        if response.status_code == 200:
            return response.json()
        else:
            logger.error(f"Ошибка при получении статистики игрока: {response.status_code} - {response.text}")
            return {"success": False, "error": f"Ошибка сервера: {response.status_code}"}
    
    except Exception as e:
        logger.error(f"Исключение при получении статистики игрока: {str(e)}")
        return {"success": False, "error": str(e)}

def get_server_stats():
    """
    Получает общую статистику сервера
    
    Returns:
        dict: Общая статистика сервера или сообщение об ошибке
    """
    try:
        # Формируем URL для запроса к API
        url = f"{API_BASE_URL}/minecraft/stats"
        
        # Заголовки для запроса
        headers = {"X-API-Key": API_SECRET_KEY}
        
        # Отправляем запрос
        response = requests.get(url, headers=headers)
        
        # Проверяем статус ответа
        if response.status_code == 200:
            return response.json()
        else:
            logger.error(f"Ошибка при получении статистики сервера: {response.status_code} - {response.text}")
            return {"success": False, "error": f"Ошибка сервера: {response.status_code}"}
    
    except Exception as e:
        logger.error(f"Исключение при получении статистики сервера: {str(e)}")
        return {"success": False, "error": str(e)}

def create_discord_poll(question, options, description=None):
    """
    Создает голосование в Discord
    
    Args:
        question (str): Вопрос для голосования
        options (list): Варианты ответов
        description (str, optional): Дополнительное описание
    
    Returns:
        dict: Результат создания голосования или сообщение об ошибке
    """
    try:
        # Формируем URL для запроса к API
        url = f"{API_BASE_URL}/discord/poll"
        
        # Заголовки для запроса
        headers = {"X-API-Key": API_SECRET_KEY}
        
        # Данные для запроса
        data = {
            "question": question,
            "options": options
        }
        
        if description:
            data["description"] = description
        
        # Отправляем запрос
        response = requests.post(url, headers=headers, json=data)
        
        # Проверяем статус ответа
        if response.status_code == 200:
            return response.json()
        else:
            logger.error(f"Ошибка при создании голосования в Discord: {response.status_code} - {response.text}")
            return {"success": False, "error": f"Ошибка сервера: {response.status_code}"}
    
    except Exception as e:
        logger.error(f"Исключение при создании голосования в Discord: {str(e)}")
        return {"success": False, "error": str(e)}