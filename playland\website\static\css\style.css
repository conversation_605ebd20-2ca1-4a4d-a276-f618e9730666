/* Основные переменные для темы */
:root {
    --primary-color: #4CAF50;
    --secondary-color: #2E7D32;
    --accent-color: #8BC34A;
    --dark-color: #1C1C1C;
    --light-color: #F5F5F5;
    --header-height: 80px;
    --error-color: #F44336;
    --warning-color: #FF9800;
    --success-color: #4CAF50;
    --info-color: #2196F3;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --vh: 1vh;
    --pixel-font: 'Press Start 2P', cursive;
    --glow-green: #00ff00;
}

/* Сброс и базовые стили */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Montserrat', sans-serif;
    background-color: var(--dark-color);
    color: var(--light-color);
    line-height: 1.6;
    position: relative;
    min-height: 100vh;
    min-height: calc(var(--vh, 1vh) * 100);
    overflow-x: hidden;
}

body.menu-open {
    overflow: hidden;
}

.minecraft-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('../images/grass_background.jpg') no-repeat center center/cover;
    opacity: 0.3;
    z-index: -1;
}

/* Частицы (имитация пыли в Minecraft) */
.particles-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: -1;
}

.particle {
    position: absolute;
    border-radius: 50%;
    animation: float 20s infinite linear;
}

@keyframes float {
    0% {
        transform: translateY(100vh) translateX(0vw) scale(0.5);
        opacity: 0;
    }
    10% {
        opacity: 0.7;
    }
    90% {
        opacity: 0.7;
    }
    100% {
        transform: translateY(-20vh) translateX(calc(var(--i, 0.5) * 40vw - 20vw)) scale(1);
        opacity: 0;
    }
}

/* Заголовок */
.header {
    background-color: rgba(0, 0, 0, 0.75);
    padding: 0 2rem;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    height: var(--header-height);
    display: flex;
    justify-content: space-between;
    align-items: center;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.4);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-left {
    display: flex;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    transition: transform 0.3s ease;
}

.logo:hover {
    transform: scale(1.05);
}

.logo-image {
    height: 50px;
    margin-right: 15px;
    vertical-align: middle;
    display: block;
    filter: drop-shadow(0 0 5px rgba(0, 255, 0, 0.5));
    transition: filter 0.3s ease, transform 0.3s ease;
}

.logo:hover .logo-image {
    filter: drop-shadow(0 0 8px rgba(0, 255, 0, 0.8));
    transform: scale(1.1) rotate(5deg);
}

.logo-text-container {
    display: flex;
    flex-direction: column;
}

.site-title {
    font-family: var(--pixel-font);
    color: #00ff00;
    font-size: 1.6rem;
    text-shadow: 2px 2px 0 #000, 0 0 8px #00ff00;
    letter-spacing: 2px;
    margin: 0;
    display: inline-block;
    position: relative;
    overflow: hidden;
    animation: textGlow 2s infinite alternate;
    transition: text-shadow 0.3s ease;
}

.logo:hover .site-title {
    text-shadow: 2px 2px 0 #000, 0 0 12px #00ff00;
}

.site-tagline {
    font-size: 0.7rem;
    color: #ddd;
    letter-spacing: 1px;
    opacity: 0.8;
    margin-top: 2px;
    font-style: italic;
    transition: color 0.3s ease;
}

.logo:hover .site-tagline {
    color: var(--glow-green);
}

@keyframes textGlow {
    0% {
        text-shadow: 2px 2px 0 #000, 0 0 6px #00ff00;
    }
    100% {
        text-shadow: 2px 2px 0 #000, 0 0 10px #00ff00;
    }
}

/* Меню навигации */
.nav {
    display: flex;
    align-items: center;
}

.nav-links {
    display: flex;
    list-style: none;
}

.nav-links li {
    margin: 0 0.25rem;
}

.nav-links a {
    color: var(--light-color);
    text-decoration: none;
    font-weight: 500;
    font-size: 1rem;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem 1.25rem;
}

.nav-links a:hover, .nav-links a.active {
    color: var(--primary-color);
}

.nav-links a:after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-links a:hover:after, .nav-links a.active:after {
    width: 100%;
}

.burger {
    display: none;
    cursor: pointer;
}

.burger div {
    width: 25px;
    height: 3px;
    background-color: var(--light-color);
    margin: 5px;
    transition: all 0.3s ease;
}

/* Overlay для мобильного меню */
.mobile-menu-overlay {
    display: none;
    position: fixed;
    top: var(--header-height);
    left: 0;
    width: 100%;
    height: calc(100% - var(--header-height));
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 990;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.mobile-menu-overlay.active {
    display: block;
    opacity: 1;
}

/* Toast сообщения */
.toast-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 9999;
}

.toast {
    background-color: #333;
    color: white;
    padding: 12px 20px;
    border-radius: 4px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
    transform: translateX(120%);
    transition: transform 0.3s ease;
    max-width: 300px;
}

.toast.show {
    transform: translateX(0);
}

.toast i {
    margin-right: 10px;
    font-size: 1.2rem;
}

.toast-success {
    background-color: var(--success-color);
}

.toast-error {
    background-color: var(--error-color);
}

.toast-warning {
    background-color: var(--warning-color);
}

.toast-info {
    background-color: var(--info-color);
}

/* Главный баннер */
.hero {
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 0 1rem;
    padding-top: var(--header-height);
}

.hero h2 {
    font-family: 'Press Start 2P', cursive;
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    text-shadow: 3px 3px 0 #000;
    color: var(--primary-color);
    animation: glow 2s infinite alternate;
}

@keyframes glow {
    from {
        text-shadow: 2px 2px 0 #000;
    }
    to {
        text-shadow: 2px 2px 10px var(--primary-color), 0 0 20px var(--accent-color);
    }
}

.hero p {
    font-size: 1.2rem;
    max-width: 700px;
    margin-bottom: 2rem;
}

.server-info {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.7);
    padding: 1rem 2rem;
    border-radius: 10px;
    margin-bottom: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(5px);
}

.info-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 1.5rem;
}

.info-item h3 {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    color: var(--accent-color);
    font-weight: 500;
    text-transform: uppercase;
}

.info-item p {
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 0;
}

.online-status {
    color: var(--primary-color);
    font-weight: bold;
}

/* Кнопки и элементы действия */
.cta-buttons {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    display: inline-block;
    padding: 1rem 2rem;
    background-color: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 700;
    margin: 0.5rem;
    transition: all 0.3s ease;
    text-align: center;
    min-width: 200px;
    border: none;
    cursor: pointer;
    font-family: 'Montserrat', sans-serif;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 0 var(--secondary-color);
    position: relative;
    overflow: hidden;
}

.btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 0 var(--secondary-color);
    filter: brightness(1.1);
}

.btn:active {
    transform: translateY(0);
    box-shadow: 0 4px 0 var(--secondary-color);
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    box-shadow: none;
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: white;
    box-shadow: 0 0 15px var(--primary-color);
}

.btn-danger {
    background-color: var(--error-color);
    box-shadow: 0 4px 0 #B71C1C;
}

.btn-danger:hover {
    box-shadow: 0 7px 0 #B71C1C;
}

.btn-warning {
    background-color: var(--warning-color);
    box-shadow: 0 4px 0 #E65100;
}

.btn-warning:hover {
    box-shadow: 0 7px 0 #E65100;
}

.btn-info {
    background-color: var(--info-color);
    box-shadow: 0 4px 0 #0D47A1;
}

.btn-info:hover {
    box-shadow: 0 7px 0 #0D47A1;
}

.copy-btn {
    background-color: rgba(0, 0, 0, 0.5);
    border: none;
    color: var(--light-color);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 5px;
    margin-left: 0.5rem;
    transition: all 0.3s ease;
}

.copy-btn:hover {
    background-color: rgba(0, 0, 0, 0.8);
    color: var(--primary-color);
}

/* Секции */
.section {
    padding: 5rem 2rem;
}

.section-dark {
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.65) 100%);
    backdrop-filter: blur(5px);
}

.section-title {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title h2 {
    font-family: 'Press Start 2P', cursive;
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
    text-shadow: 2px 2px 0 #000;
}

.section-title p {
    max-width: 700px;
    margin: 0 auto;
    color: var(--light-color);
    font-size: 1.1rem;
}

/* Сетки и карточки */
.grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.card {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.4s ease;
    backdrop-filter: blur(8px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.15);
}

.card:hover {
    transform: translateY(-15px) scale(1.03);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
    background-color: rgba(255, 255, 255, 0.2);
}

.card-body {
    padding: 1.5rem;
}

.card-img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.card-title {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--light-color);
}

.card-text {
    color: var(--light-color);
    opacity: 0.8;
    margin-bottom: 1.5rem;
}

.card-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 700;
    display: inline-flex;
    align-items: center;
}

.card-link i {
    margin-left: 0.5rem;
    transition: transform 0.3s ease;
}

.card-link:hover i {
    transform: translateX(5px);
}

/* Иконки и особенности */
.feature-icon {
    font-size: 3.5rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 0 rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.card:hover .feature-icon {
    color: var(--accent-color);
    transform: scale(1.1);
}

/* Формы */
.form-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 10px;
    backdrop-filter: blur(5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.form-control {
    width: 100%;
    padding: 0.8rem 1rem;
    border-radius: 5px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background-color: rgba(0, 0, 0, 0.5);
    color: var(--light-color);
    font-family: 'Montserrat', sans-serif;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.3);
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.form-control[type="file"] {
    padding: 0.5rem;
}

.form-text {
    display: block;
    font-size: 0.85rem;
    margin-top: 0.5rem;
    color: var(--light-color);
    opacity: 0.7;
}

.form-check {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.form-check-input {
    margin-right: 0.5rem;
}

.form-check-label {
    font-size: 1rem;
}

.form-error {
    color: var(--error-color);
    font-size: 0.85rem;
    margin-top: 0.5rem;
}

.flash-message {
    padding: 1rem;
    border-radius: 5px;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
}

.flash-message i {
    margin-right: 0.5rem;
    font-size: 1.2rem;
}

.flash-success {
    background-color: rgba(76, 175, 80, 0.2);
    border: 1px solid var(--success-color);
    color: var(--success-color);
}

.flash-danger {
    background-color: rgba(244, 67, 54, 0.2);
    border: 1px solid var(--error-color);
    color: var(--error-color);
}

.flash-warning {
    background-color: rgba(255, 152, 0, 0.2);
    border: 1px solid var(--warning-color);
    color: var(--warning-color);
}

.flash-info {
    background-color: rgba(33, 150, 243, 0.2);
    border: 1px solid var(--info-color);
    color: var(--info-color);
}

/* Подвал */
.footer {
    background-color: rgba(0, 0, 0, 0.9);
    padding: 3rem 2rem;
    text-align: center;
    backdrop-filter: blur(5px);
}

.footer-logo {
    margin-bottom: 2rem;
}

.footer-logo img {
    height: 60px;
    margin-bottom: 1rem;
}

.footer-logo h2 {
    font-family: 'Press Start 2P', cursive;
    color: var(--primary-color);
    font-size: 1.5rem;
    text-shadow: 2px 2px 0 #000;
}

.footer-links {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 2rem;
}

.footer-section {
    margin: 0 2rem;
    text-align: left;
}

.footer-section h3 {
    color: var(--light-color);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: var(--light-color);
    opacity: 0.8;
    text-decoration: none;
    transition: all 0.3s ease;
}

.footer-section ul li a:hover {
    color: var(--primary-color);
    opacity: 1;
}

.social-links {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;
}

.social-links a {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 45px;
    height: 45px;
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--light-color);
    border-radius: 50%;
    margin: 0 0.7rem;
    transition: all 0.3s ease;
    font-size: 1.1rem;
}

.social-links a:hover {
    background-color: var(--primary-color);
    transform: translateY(-5px) scale(1.1);
    color: white;
}

.copyright {
    color: var(--light-color);
    opacity: 0.6;
    font-size: 0.9rem;
}

/* Таблицы */
.table-container {
    overflow-x: auto;
    margin-bottom: 2rem;
}

.table {
    width: 100%;
    border-collapse: collapse;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    overflow: hidden;
}

.table th, .table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.table th {
    background-color: rgba(0, 0, 0, 0.7);
    font-weight: 700;
    color: var(--primary-color);
    text-transform: uppercase;
    font-size: 0.9rem;
    letter-spacing: 1px;
}

.table tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.table-status {
    display: inline-block;
    padding: 0.3rem 0.8rem;
    border-radius: 30px;
    font-size: 0.8rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.status-pending {
    background-color: rgba(255, 152, 0, 0.2);
    color: var(--warning-color);
}

.status-approved {
    background-color: rgba(76, 175, 80, 0.2);
    color: var(--success-color);
}

.status-rejected {
    background-color: rgba(244, 67, 54, 0.2);
    color: var(--error-color);
}

/* Страница 404 */
.error-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100vh;
    text-align: center;
    padding: 0 2rem;
}

.error-code {
    font-family: 'Press Start 2P', cursive;
    font-size: 8rem;
    margin-bottom: 2rem;
    text-shadow: 5px 5px 0 #000;
    color: var(--error-color);
}

.error-message {
    font-size: 1.5rem;
    margin-bottom: 2rem;
}

/* Адаптивный дизайн */
@media screen and (max-width: 768px) {
    .header {
        padding: 0 1rem;
        height: 60px;
    }
    
    .logo-image {
        height: 40px;
        margin-right: 10px;
    }
    
    .site-title {
        font-size: 1.2rem;
    }
    
    .site-tagline {
        font-size: 0.6rem;
    }
    
    .nav-links {
        position: fixed;
        top: 60px;
        right: -100%;
        width: 80%;
        height: calc(100vh - 60px);
        height: calc((var(--vh, 1vh) * 100) - 60px);
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        background-color: rgba(0, 0, 0, 0.95);
        transform: translateX(100%);
        transition: transform 0.3s ease-in-out;
        z-index: 1000;
        padding: 20px 0;
        overflow-y: auto;
        box-shadow: -5px 0 15px rgba(0, 0, 0, 0.5);
    }
    
    .nav-links.active {
        transform: translateX(0);
        right: 0;
    }
    
    .nav-links li {
        margin: 15px 0;
        width: 100%;
        text-align: center;
    }
    
    .nav-links a {
        font-size: 1rem;
        padding: 10px;
        display: block;
    }
    
    .burger {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        width: 30px;
        height: 22px;
        cursor: pointer;
        z-index: 1001;
    }
    
    .burger div {
        width: 100%;
        height: 3px;
        background-color: var(--light-color);
        transition: all 0.3s ease;
        border-radius: 2px;
    }
    
    .burger.active div:nth-child(1) {
        transform: translateY(9.5px) rotate(45deg);
        background-color: var(--primary-color);
    }
    
    .burger.active div:nth-child(2) {
        opacity: 0;
    }
    
    .burger.active div:nth-child(3) {
        transform: translateY(-9.5px) rotate(-45deg);
        background-color: var(--primary-color);
    }
    
    /* Стили для секций на мобильных устройствах */
    .hero {
        padding-top: 60px;
        min-height: 100vh;
        min-height: calc(var(--vh, 1vh) * 100);
    }
    
    .hero h1 {
        font-size: 2rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
        width: 100%;
        max-width: 300px;
        margin: 20px auto 0;
    }
    
    .hero-buttons a {
        width: 100%;
        margin-bottom: 10px;
        text-align: center;
    }
    
    .features, .news-section {
        padding: 60px 15px;
    }
    
    .features h2, .news-section h2 {
        font-size: 1.8rem;
        margin-bottom: 40px;
    }
    
    .feature-card, .news-card {
        padding: 20px;
    }
    
    .feature-icon {
        font-size: 2.5rem;
    }
    
    .feature-title, .news-title {
        font-size: 1.1rem;
    }
    
    /* Кнопка прокрутки вверх для мобильных */
    .scroll-top {
        bottom: 20px;
        right: 20px;
        width: 40px;
        height: 40px;
        border-radius: 50%;
    }
    
    .scroll-top i {
        font-size: 16px;
    }
}

/* Мелкие мобильные устройства */
@media screen and (max-width: 480px) {
    .scroll-to-top {
        width: 40px;
        height: 40px;
        bottom: 20px;
        right: 20px;
        font-size: 1rem;
    }
    
    .logo-image {
        height: 35px;
    }
    
    .header {
        height: 70px;
    }
    
    .hero {
        height: auto;
        min-height: 100vh;
        padding-top: 90px;
        padding-bottom: 2rem;
    }
    
    .hero h2 {
        font-size: 1.5rem;
    }
    
    .section-title h2 {
        font-size: 1.3rem;
    }
    
    .step-item {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
    
    .step-number {
        margin-right: 0;
        margin-bottom: 1rem;
    }
    
    .social-links a {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
        margin: 0 0.5rem;
    }
    
    .footer {
        padding: 2rem 1rem;
    }
    
    .footer-logo img {
        height: 50px;
    }
    
    .footer-logo h2 {
        font-size: 1.2rem;
    }
}

/* Анимации и эффекты */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.98);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.animate-fade-in {
    animation: fadeIn 0.7s ease-out forwards;
    opacity: 0;
}

/* Minecraft-стиль кнопки */
.minecraft-btn {
    display: inline-block;
    position: relative;
    padding: 0.8rem 1.5rem;
    background-color: #555555;
    color: white;
    text-decoration: none;
    font-family: 'Press Start 2P', cursive;
    font-size: 0.9rem;
    text-align: center;
    border: 2px solid #000;
    box-shadow: inset -2px -4px 0 rgba(0, 0, 0, 0.6), inset 2px 2px 0 rgba(255, 255, 255, 0.3);
    text-shadow: 2px 2px 0 #000;
    margin: 0.5rem;
    cursor: pointer;
    transition: all 0.1s ease;
}

.minecraft-btn:hover {
    background-color: #666666;
}

.minecraft-btn:active {
    transform: translateY(2px);
    box-shadow: inset -1px -2px 0 rgba(0, 0, 0, 0.6), inset 1px 1px 0 rgba(255, 255, 255, 0.3);
}

.minecraft-btn-green {
    background-color: var(--primary-color);
}

.minecraft-btn-red {
    background-color: var(--error-color);
}

.minecraft-btn-blue {
    background-color: var(--info-color);
}

.minecraft-btn-orange {
    background-color: var(--warning-color);
}

/* Блочный стиль Minecraft для шапки */
.minecraft-header {
    position: relative;
    padding: 1.5rem;
    background-color: #8B5A2B;
    border: 5px solid;
    border-color: #A06F3A #8B5A2B #6D442E #A06F3A;
    box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.3);
    text-align: center;
    margin-bottom: 2rem;
}

.minecraft-header h2 {
    font-family: 'Press Start 2P', cursive;
    color: white;
    text-shadow: 2px 2px 0 #000;
    margin: 0;
}

/* Переключатель для темной/светлой темы */
.theme-switch {
    position: relative;
    width: 60px;
    height: 30px;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 30px;
    cursor: pointer;
}

.theme-switch-slider {
    position: absolute;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: var(--primary-color);
    top: 3px;
    left: 3px;
    transition: all 0.3s ease;
}

.theme-switch.active .theme-switch-slider {
    transform: translateX(30px);
}

/* Tooltip */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltip-text {
    visibility: hidden;
    width: 200px;
    background-color: rgba(0, 0, 0, 0.9);
    color: var(--light-color);
    text-align: center;
    border-radius: 5px;
    padding: 0.5rem 1rem;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    transition: opacity 0.3s;
}

.tooltip .tooltip-text::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: rgba(0, 0, 0, 0.9) transparent transparent transparent;
}

.tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* Стили для секции "Как присоединиться?" */
.steps-container {
    display: flex;
    justify-content: space-around;
    max-width: 1100px;
    margin: 3rem auto 0;
    flex-wrap: wrap;
    gap: 2rem;
}

.step-item {
    display: flex;
    align-items: flex-start;
    background-color: rgba(255, 255, 255, 0.05);
    padding: 1.5rem;
    border-radius: 10px;
    flex-basis: calc(33.333% - 2rem);
    min-width: 280px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.step-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-5px);
}

.step-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-right: 1.5rem;
    line-height: 1;
    min-width: 60px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.step-number i {
    font-size: 2rem;
    opacity: 0.8;
}

.step-content h3 {
    font-size: 1.3rem;
    color: var(--light-color);
    margin-bottom: 0.5rem;
}

.step-content p {
    color: var(--light-color);
    opacity: 0.8;
    font-size: 0.95rem;
}

/* Адаптация шагов для мобильных */
@media screen and (max-width: 992px) {
    .step-item {
        flex-basis: calc(50% - 1rem);
    }
}

@media screen and (max-width: 576px) {
    .step-item {
        flex-basis: 100%;
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
    .step-number {
        margin-right: 0;
        margin-bottom: 1rem;
        min-width: auto;
    }
}

/* Стили для секции "Правила" */
.rules-container {
    max-width: 900px;
    margin: 2rem auto 0;
    background-color: rgba(0, 0, 0, 0.3);
    padding: 2rem;
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.rules-list {
    list-style: none;
    padding-left: 0;
}

.rules-list li {
    margin-bottom: 1rem;
    padding-left: 1.8rem;
    position: relative;
    font-size: 1rem;
    line-height: 1.6;
}

.rules-list li i {
    position: absolute;
    left: 0;
    top: 4px;
    color: var(--accent-color);
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

.rules-list li strong {
    color: var(--light-color);
}

.rules-more {
    margin-top: 2rem;
    text-align: center;
    font-style: italic;
    opacity: 0.7;
}

/* Адаптация правил для мобильных */
@media screen and (max-width: 576px) {
    .rules-container {
        padding: 1.5rem;
    }
    .rules-list li {
        padding-left: 1.5rem;
        font-size: 0.95rem;
    }
    .rules-list li i {
         top: 3px;
         font-size: 1rem;
    }
}

/* Кнопка "Вернуться наверх" */
.scroll-to-top, .scroll-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: rgba(26, 26, 26, 0.95);
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    border-radius: 8px;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 0 #006400, 0 0 20px rgba(0, 255, 0, 0.4);
    z-index: 999;
    font-family: 'Press Start 2P', cursive;
}

.scroll-to-top.active, .scroll-top.active {
    opacity: 1;
    visibility: visible;
}

.scroll-to-top:hover, .scroll-top:hover {
    background: var(--primary-color);
    color: #000;
    transform: translateY(-3px);
    box-shadow: 0 7px 0 #006400, 0 0 30px rgba(0, 255, 0, 0.6);
}

.scroll-to-top:active, .scroll-top:active {
    transform: translateY(0);
    box-shadow: 0 2px 0 #006400;
}
    transform: translateY(-5px);
}

/* Стили для профиля */
.profile-info {
    background-color: rgba(0, 0, 0, 0.4);
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
}

.profile-section {
    margin-bottom: 30px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 20px;
}

.profile-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.profile-section h3 {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.profile-section h3 i {
    margin-right: 10px;
    font-size: 1.3rem;
}

.profile-data {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
}

.profile-row {
    display: flex;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    padding-bottom: 10px;
}

.profile-label {
    font-weight: 600;
    min-width: 180px;
    color: rgba(255, 255, 255, 0.8);
}

.profile-value {
    flex: 1;
}

.admin-badge, .user-badge {
    display: inline-block;
    padding: 4px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.admin-badge {
    background-color: var(--warning-color);
    color: #000;
}

.user-badge {
    background-color: var(--primary-color);
    color: white;
}

.security-options {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    margin-top: 15px;
}

/* Адаптация для мобильных */
@media screen and (max-width: 768px) {
    .profile-row {
        flex-direction: column;
    }
    
    .profile-label {
        margin-bottom: 5px;
    }
    
    .security-options {
        flex-direction: column;
    }
    
    .security-options .btn {
        width: 100%;
    }
}

/* Добавим стили для подсказок в форме */
.form-help {
    font-size: 0.85rem;
    color: #777;
    margin-top: 5px;
    display: block;
    font-style: italic;
}

/* Стили для социальных кнопок авторизации */
.social-login {
    margin: 20px 0;
    text-align: center;
}

.social-login-text {
    display: block;
    position: relative;
    margin: 15px 0;
    text-align: center;
}

.social-login-text:before,
.social-login-text:after {
    content: "";
    position: absolute;
    top: 50%;
    width: 35%;
    height: 1px;
    background-color: #ddd;
}

.social-login-text:before {
    left: 0;
}

.social-login-text:after {
    right: 0;
}

.social-buttons {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 15px;
}

.social-button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 15px;
    border-radius: 4px;
    color: white;
    font-weight: 500;
    text-decoration: none;
    transition: opacity 0.2s;
    min-width: 120px;
}

.social-button:hover {
    opacity: 0.9;
}

.social-button i {
    margin-right: 8px;
    font-size: 1.2rem;
}

.discord-button {
    background-color: #5865F2;
}

.telegram-button {
    background-color: #29a9eb;
}

.vk-button {
    background-color: #4C75A3;
}

/* Анимации для перехода между страницами */
.page-transition-overlay {
    position: fixed;
    left: -100%;
    top: 0;
    width: 100%;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.9);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: left 0.3s ease-in-out;
}

.page-transition-image {
    width: 150px;
    height: 150px;
    animation: pulse 1.5s infinite ease-in-out;
}

@keyframes pulse {
    0% {
        transform: scale(0.95);
        opacity: 0.7;
    }
    50% {
        transform: scale(1.05);
        opacity: 1;
    }
    100% {
        transform: scale(0.95);
        opacity: 0.7;
    }
}

/* Анимации для контента страницы */
.animate-fade-in {
    animation: fadeIn 0.8s ease forwards;
    opacity: 0;
}

.animate-fade-in-up {
    animation: fadeInUp 0.8s ease forwards;
    opacity: 0;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.8s ease forwards;
    opacity: 0;
}

.animate-fade-in-right {
    animation: fadeInRight 0.8s ease forwards;
    opacity: 0;
}

/* Задержки для анимаций */
.delay-100 { animation-delay: 0.1s; }
.delay-200 { animation-delay: 0.2s; }
.delay-300 { animation-delay: 0.3s; }
.delay-400 { animation-delay: 0.4s; }
.delay-500 { animation-delay: 0.5s; }

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}