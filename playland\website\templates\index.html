{% extends "base.html" %}

{% block title %}PLAYLAND - Главная{% endblock %}

{% block namespace %}home{% endblock %}

{% block extra_head %}
    <style>
        :root {
        --pixel-font: 'Press Start 2P', cursive;
        --main-font: 'Roboto', sans-serif;
        --glow-green: #00ff00;
        --bright-green: #32cd32;
        --dark-bg: #121212;
    }

    .hero {
        background-image: url("{{ url_for('static', filename='images/grass_background.jpg') }}");
            background-size: cover;
        background-position: center center;
        background-attachment: fixed;
        height: 100vh;
            display: flex;
        flex-direction: column;
            justify-content: center;
            align-items: center;
        text-align: center;
        padding: 20px;
        padding-top: 100px;
        box-sizing: border-box;
            position: relative;
        overflow: hidden;
    }
    
    .hero::before {
        content: '';
        position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(2px);
            z-index: 1;
    }
    
    .hero-content {
        position: relative;
        z-index: 2;
        max-width: 900px;
        width: 100%;
    }
    
    .hero h1 {
        font-family: var(--pixel-font);
        font-size: 5rem;
        color: var(--glow-green);
        text-shadow: 3px 3px 0 #000000, 0 0 20px var(--glow-green);
        margin-bottom: 10px;
        line-height: 1.2;
        animation: heroTitlePopIn 1s cubic-bezier(0.68, -0.55, 0.27, 1.55) 0.5s forwards, heroGlow 3s infinite alternate 1s;
        opacity: 0;
        transform: scale(0.5);
        letter-spacing: 5px;
    }
    
    .hero-subtitle {
        font-family: var(--pixel-font);
        font-size: 1.5rem;
        color: #ffffff;
        text-shadow: 2px 2px 0 #000000;
        margin-bottom: 30px;
        animation: fadeInSlideUp 1s ease-out 0.7s forwards;
                opacity: 0;
    }
    
    @keyframes heroTitlePopIn {
        to {
            opacity: 1;
            transform: scale(1);
        }
    }
    
    @keyframes heroGlow {
        0% {
            text-shadow: 3px 3px 0 #000000, 0 0 10px rgba(0, 255, 0, 0.5); 
        }
        100% {
            text-shadow: 3px 3px 0 #000000, 0 0 20px var(--glow-green), 0 0 40px var(--glow-green), 0 0 60px var(--glow-green);
        }
    }

    .hero p {
        color: white;
        font-size: 1.3em;
        max-width: 800px;
        margin: 0 auto 40px;
        animation: fadeInSlideUp 1s ease-out 0.9s forwards;
                opacity: 0;
        line-height: 1.6;
        text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.8);
            }
    
    @keyframes fadeInSlideUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

    .hero-buttons {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 20px;
        margin-top: 30px;
        animation: fadeInSlideUp 1s ease-out 1.1s forwards;
        opacity: 0;
    }

    .btn-minecraft {
        display: inline-block;
        padding: 18px 35px;
        background-color: var(--glow-green);
        color: #000;
        text-decoration: none;
        font-family: var(--pixel-font);
        font-size: 1em;
        text-transform: uppercase;
        border: none;
        position: relative;
        overflow: hidden;
        transition: all 0.3s;
        box-shadow: 0 4px 0 #006400, 0 0 20px rgba(0, 255, 0, 0.4);
        border-radius: 3px;
    }

    .btn-minecraft::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.2);
        transition: transform 0.5s ease;
        transform: skewX(-15deg);
    }

    .btn-minecraft:hover {
        transform: translateY(-5px);
        box-shadow: 0 9px 0 #006400, 0 0 30px rgba(0, 255, 0, 0.6);
    }

    .btn-minecraft:hover::before {
        transform: skewX(-15deg) translateX(200%);
    }

    .btn-minecraft:active {
        transform: translateY(0);
        box-shadow: 0 2px 0 #006400;
    }

    .btn-outline {
        background-color: transparent;
        border: 2px solid var(--glow-green);
        color: var(--glow-green);
        box-shadow: 0 4px 0 rgba(0, 255, 0, 0.3), 0 0 20px rgba(0, 255, 0, 0.2);
    }

    .btn-outline:hover {
        box-shadow: 0 9px 0 rgba(0, 255, 0, 0.3), 0 0 30px rgba(0, 255, 0, 0.4);
        background-color: rgba(0, 255, 0, 0.1);
    }

    .btn-outline:active {
        box-shadow: 0 2px 0 rgba(0, 255, 0, 0.3);
    }

    /* Секция с особенностями */
    .features {
        padding: 100px 20px;
        background-color: rgba(0, 0, 0, 0.8);
            position: relative;
        overflow: hidden;
        }
        
    .features::before {
            content: '';
            position: absolute;
        top: 0;
            left: 0;
            width: 100%;
        height: 5px;
        background: linear-gradient(90deg, transparent, var(--glow-green), transparent);
    }

    .features h2 {
        text-align: center;
        font-family: var(--pixel-font);
        font-size: 2.5em;
        color: var(--glow-green);
        margin-bottom: 60px;
        text-shadow: 0 0 15px var(--glow-green);
    }

    .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 40px;
        max-width: 1200px;
        margin: 0 auto;
    }

    .feature-card {
        background-color: rgba(26, 26, 26, 0.8);
        border: 2px solid var(--glow-green);
        padding: 35px;
        border-radius: 8px;
        text-align: center;
        transition: transform 0.4s, box-shadow 0.4s;
        position: relative;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    }

    .feature-card::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        border: 2px solid transparent;
        border-radius: 8px;
        background: linear-gradient(45deg, var(--glow-green), transparent, var(--glow-green));
        background-size: 400% 400%;
        opacity: 0;
        transition: opacity 0.6s;
        z-index: -1;
        animation: borderGlow 3s ease infinite;
    }

    @keyframes borderGlow {
        0% {
            background-position: 0% 50%;
        }
        50% {
            background-position: 100% 50%;
        }
        100% {
            background-position: 0% 50%;
        }
    }

    .feature-card:hover {
        transform: translateY(-15px);
        box-shadow: 0 15px 30px rgba(0, 255, 0, 0.2);
    }

    .feature-card:hover::before {
        opacity: 1;
    }

    .feature-icon {
        font-size: 3.5em;
        color: var(--glow-green);
        margin-bottom: 25px;
        text-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
        transition: transform 0.4s, color 0.4s;
    }

    .feature-card:hover .feature-icon {
        transform: scale(1.1);
        color: white;
    }

    .feature-title {
        font-family: var(--pixel-font);
        font-size: 1.3em;
        color: var(--glow-green);
        margin-bottom: 20px;
    }

    .feature-text {
        line-height: 1.8;
        color: #f0f0f0;
    }
    
    /* Секция с новостями */
    .news-section {
        padding: 100px 20px;
        background-color: rgba(0, 0, 0, 0.6);
            position: relative;
    }
    
    .news-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
            width: 100%;
        height: 5px;
        background: linear-gradient(90deg, transparent, var(--glow-green), transparent);
    }

    .news-section h2 {
        text-align: center;
        font-family: var(--pixel-font);
        font-size: 2.5em;
        color: var(--glow-green);
        margin-bottom: 60px;
        text-shadow: 0 0 15px var(--glow-green);
    }

    .news-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 40px;
        max-width: 1200px;
        margin: 0 auto;
    }

    .news-card {
        background-color: rgba(26, 26, 26, 0.8);
        border: 2px solid var(--glow-green);
        overflow: hidden;
        border-radius: 10px;
        transition: transform 0.4s, box-shadow 0.4s;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .news-card:hover {
        transform: translateY(-15px);
        box-shadow: 0 15px 30px rgba(0, 255, 0, 0.2);
    }

    .news-image {
        height: 200px;
        background-size: cover;
        background-position: center;
            position: relative;
        }
        
    .news-image::after {
            content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 30px;
        background: linear-gradient(to top, rgba(26, 26, 26, 0.8), transparent);
    }

    .news-content {
        padding: 25px;
        flex-grow: 1;
        display: flex;
        flex-direction: column;
    }

    .news-date {
        color: #aaa;
        font-size: 0.9em;
        margin-bottom: 15px;
    }

    .news-title {
        font-family: var(--pixel-font);
        font-size: 1.3em;
        color: var(--glow-green);
        margin-bottom: 20px;
    }

    .news-excerpt {
        line-height: 1.8;
        margin-bottom: 25px;
        color: #f0f0f0;
        flex-grow: 1;
    }

    .btn-read-more {
        display: inline-block;
        padding: 10px 20px;
        background-color: transparent;
        border: 2px solid var(--glow-green);
        color: var(--glow-green);
        text-decoration: none;
        font-family: var(--pixel-font);
        font-size: 0.8em;
        transition: all 0.3s;
        border-radius: 5px;
        align-self: flex-start;
        box-shadow: 0 0 10px rgba(0, 255, 0, 0.1);
    }

    .btn-read-more:hover {
        background-color: var(--glow-green);
        color: #000;
        box-shadow: 0 0 15px rgba(0, 255, 0, 0.3);
    }
    
    /* Minecraft частицы */
    .particles-container {
            position: absolute;
            top: 0;
        left: 0;
            width: 100%;
            height: 100%;
        overflow: hidden;
        z-index: 0;
    }
    
    .particle {
        position: absolute;
        opacity: 0;
        animation: floatParticle linear infinite;
    }
    
    @keyframes floatParticle {
        0% {
            transform: translateY(100vh) translateX(0);
            opacity: 0;
        }
        10% {
            opacity: 0.7;
        }
        90% {
            opacity: 0.7;
        }
        100% {
            transform: translateY(-20vh) translateX(calc(var(--rand-x, 0) * 40vw - 20vw));
            opacity: 0;
        }
    }
    
    /* Кнопка прокрутки вверх */
    .scroll-top {
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 50px;
        height: 50px;
        background-color: var(--glow-green);
        color: #000;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 999;
        box-shadow: 0 2px 10px rgba(0, 255, 0, 0.4);
        border: 2px solid transparent;
    }
    
    .scroll-top.active {
        opacity: 0.9;
        visibility: visible;
    }
    
    .scroll-top:hover {
        transform: translateY(-5px);
        background-color: #000;
        color: var(--glow-green);
        border: 2px solid var(--glow-green);
        box-shadow: 0 0 15px rgba(0, 255, 0, 0.6);
    }
    
    .scroll-top i {
        font-size: 20px;
    }

    /* Адаптивная версия */
    @media (max-width: 768px) {
        .hero h1 {
            font-size: 3rem;
            letter-spacing: 3px;
        }
        
        .hero-subtitle {
            font-size: 1.2rem;
        }
        
        .hero p {
            font-size: 1.1rem;
        }
        
        .hero-buttons {
            flex-direction: column;
            gap: 15px;
        }
        
        .features-grid, .news-grid {
            grid-template-columns: 1fr;
        }
        
        .feature-card, .news-card {
            transform: none !important;
        }
        
        .scroll-top {
            width: 40px;
            height: 40px;
            bottom: 20px;
            right: 20px;
        }
        
        .scroll-top i {
            font-size: 16px;
        }
    }
    
    @media (max-width: 480px) {
        .hero h1 {
            font-size: 2.3rem;
            letter-spacing: 2px;
        }
        
        .hero-subtitle {
            font-size: 1rem;
        }
        
        .hero p {
            font-size: 1rem;
        }
    }
    
    /* Анимация печатающегося текста */
    .typing-animation {
        display: inline-block;
        overflow: hidden;
        white-space: nowrap;
        border-right: 2px solid var(--glow-green);
        animation: typing 4s steps(40) 1s both, blinkCursor 0.75s step-end infinite;
    }
    
    @keyframes typing {
        from { width: 0 }
        to { width: 100% }
    }
    
    @keyframes blinkCursor {
        from, to { border-color: transparent }
        50% { border-color: var(--glow-green) }
    }
    
    /* Toast уведомления */
    .toast-container {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 9999;
    }

    .toast {
        background-color: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 12px 25px;
        border-radius: 5px;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        transform: translateX(100%);
        opacity: 0;
        transition: transform 0.3s, opacity 0.3s;
    }

    .toast.show {
        transform: translateX(0);
        opacity: 1;
    }

    .toast i {
        margin-right: 10px;
    }

    .toast-success {
        border-left: 4px solid var(--glow-green);
    }

    /* Стили для информации о сервере */
    .server-info-section {
        padding: 80px 20px;
        background: rgba(0, 0, 0, 0.4);
    }

    .server-info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 30px;
        margin-top: 50px;
        max-width: 1200px;
        margin-left: auto;
        margin-right: auto;
    }

    .info-card {
        background: rgba(0, 0, 0, 0.8);
        border: 2px solid rgba(0, 255, 0, 0.2);
        border-radius: 15px;
        padding: 30px;
        text-align: center;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .info-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(0, 255, 0, 0.1), transparent);
        transition: left 0.5s ease;
    }

    .info-card:hover::before {
        left: 100%;
    }

    .info-card:hover {
        transform: translateY(-10px);
        border-color: rgba(0, 255, 0, 0.5);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 30px rgba(0, 255, 0, 0.2);
    }

    .info-icon {
        font-size: 3rem;
        color: var(--glow-green);
        margin-bottom: 20px;
        animation: pulse 2s infinite;
    }

    .info-title {
        font-size: 1.4rem;
        font-weight: bold;
        color: #fff;
        margin-bottom: 15px;
        font-family: var(--pixel-font);
    }

    .info-value {
        color: var(--glow-green);
        font-size: 1.8rem;
        font-weight: bold;
        margin-bottom: 10px;
        font-family: var(--pixel-font);
    }

    .info-description {
        color: #ccc;
        font-size: 0.9rem;
        line-height: 1.5;
    }

    .server-ip-card {
        grid-column: 1 / -1;
        background: linear-gradient(135deg, rgba(0, 255, 0, 0.1), rgba(0, 0, 0, 0.8));
        border: 2px solid var(--glow-green);
    }

    .server-ip-card .info-value {
        font-size: 2.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .server-ip-card .info-value:hover {
        text-shadow: 0 0 20px var(--glow-green);
        transform: scale(1.05);
    }

    .copy-hint {
        font-size: 0.8rem;
        color: #888;
        margin-top: 10px;
    }

    .section-title {
        text-align: center;
        font-family: var(--pixel-font);
        font-size: 2.5rem;
        color: var(--glow-green);
        margin-bottom: 20px;
        text-shadow: 0 0 15px var(--glow-green);
    }

    .section-subtitle {
        text-align: center;
        color: #ccc;
        font-size: 1.2rem;
        margin-bottom: 50px;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    @media (max-width: 768px) {
        .server-info-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .server-ip-card {
            grid-column: 1;
        }

        .info-card {
            transform: none !important;
        }

        .section-title {
            font-size: 2rem;
        }

        .section-subtitle {
            font-size: 1rem;
        }
    }








    </style>
{% endblock %}

{% block content %}
<section class="hero">
    <div class="particles-container" id="particles"></div>
    <div class="hero-content">
        <h1 class="animate-fade-in-up">PLAYLAND</h1>
        <div class="hero-subtitle">мир безграничных возможностей</div>
        <p class="animate-fade-in-up delay-200">Лучший Minecraft сервер для строительства, выживания и приключений. 
        Присоединяйтесь к нашему сообществу и создавайте свои истории в мире безграничных возможностей!</p>
        
        <div class="hero-buttons">
            <a href="{{ url_for('application') }}" class="btn-minecraft animate-fade-in-up delay-300">Подать заявку</a>
            <a href="{{ url_for('rules') }}" class="btn-minecraft btn-outline animate-fade-in-up delay-400">Правила сервера</a>
        </div>
    </div>
</section>

<section class="features">
    <h2 class="animate-fade-in">Особенности PLAYLAND</h2>
    
    <div class="features-grid">
        <div class="feature-card animate-fade-in-up">
            <div class="feature-icon">
                <i class="fas fa-users"></i>
            </div>
            <h3 class="feature-title">Дружное сообщество</h3>
            <p class="feature-text">Присоединяйтесь к сообществу игроков, где всегда рады новым лицам и помогают друг другу. У нас регулярно проводятся совместные ивенты и мероприятия.</p>
        </div>
        
        <div class="feature-card animate-fade-in-up delay-200">
            <div class="feature-icon">
                <i class="fas fa-shield-alt"></i>
            </div>
            <h3 class="feature-title">Защита от гриферов</h3>
            <p class="feature-text">Наша продвинутая система защиты предотвращает гриферство и сохраняет ваши постройки в безопасности. Модераторы активно следят за порядком.</p>
        </div>
        
        <div class="feature-card animate-fade-in-up delay-300">
            <div class="feature-icon">
                <i class="fas fa-cogs"></i>
            </div>
            <h3 class="feature-title">Уникальные плагины</h3>
            <p class="feature-text">Эксклюзивные плагины и модификации, которые делают игровой процесс более увлекательным. Расширенная экономика, уникальные квесты и многое другое.</p>
        </div>
        
        <div class="feature-card animate-fade-in-up delay-400">
            <div class="feature-icon">
                <i class="fas fa-globe"></i>
            </div>
            <h3 class="feature-title">Огромный мир</h3>
            <p class="feature-text">Исследуйте безграничные просторы нашего сервера с разнообразными биомами, уникальными локациями и скрытыми сокровищами, которые ждут вас.</p>
        </div>

        <div class="feature-card animate-fade-in-up delay-500">
            <div class="feature-icon">
                <i class="fas fa-rocket"></i>
            </div>
            <h3 class="feature-title">Высокая производительность</h3>
            <p class="feature-text">Наш сервер работает на мощном оборудовании с оптимизированными настройками, обеспечивая стабильный FPS и минимальные лаги.</p>
        </div>

        <div class="feature-card animate-fade-in-up delay-600">
            <div class="feature-icon">
                <i class="fas fa-heart"></i>
            </div>
            <h3 class="feature-title">Дружелюбное сообщество</h3>
            <p class="feature-text">Присоединяйтесь к активному и отзывчивому сообществу игроков, где каждый найдет друзей и помощь в любой ситуации.</p>
        </div>
    </div>
</section>

<!-- Секция информации о сервере -->
<section class="server-info-section">
    <div class="container">
        <h2 class="section-title animate-fade-in">Информация о сервере</h2>
        <p class="section-subtitle animate-fade-in delay-100">Все что нужно знать для подключения к PlayLand</p>

        <div class="server-info-grid">
            <!-- IP адрес сервера -->
            <div class="info-card server-ip-card animate-fade-in-up delay-100">
                <div class="info-icon">
                    <i class="fas fa-server"></i>
                </div>
                <h3 class="info-title">IP адрес сервера</h3>
                <div class="info-value" onclick="copyToClipboard('play.playland.ru')">play.playland.ru</div>
                <p class="info-description">Нажмите чтобы скопировать</p>
                <p class="copy-hint">💡 Просто кликните по IP для копирования</p>
            </div>

            <!-- Версия -->
            <div class="info-card animate-fade-in-up delay-200">
                <div class="info-icon">
                    <i class="fas fa-cube"></i>
                </div>
                <h3 class="info-title">Версия Minecraft</h3>
                <div class="info-value">1.21.5</div>
                <p class="info-description">Последняя стабильная версия с поддержкой всех новых возможностей</p>
            </div>

            <!-- Тип сервера -->
            <div class="info-card animate-fade-in-up delay-300">
                <div class="info-icon">
                    <i class="fas fa-heart"></i>
                </div>
                <h3 class="info-title">Тип сервера</h3>
                <div class="info-value">Vanilla</div>
                <p class="info-description">Чистый Minecraft без модификаций, только оригинальный игровой опыт</p>
            </div>

            <!-- Бесплатный доступ -->
            <div class="info-card animate-fade-in-up delay-400">
                <div class="info-icon">
                    <i class="fas fa-gift"></i>
                </div>
                <h3 class="info-title">Бесплатный доступ</h3>
                <div class="info-value">FREE</div>
                <p class="info-description">Сервер полностью бесплатен! Требуется только подача заявки</p>
            </div>

            <!-- Количество игроков -->
            <div class="info-card animate-fade-in-up delay-500">
                <div class="info-icon">
                    <i class="fas fa-infinity"></i>
                </div>
                <h3 class="info-title">Количество игроков</h3>
                <div class="info-value">∞</div>
                <p class="info-description">Без ограничений по количеству игроков</p>
            </div>

            <!-- Режим игры -->
            <div class="info-card animate-fade-in-up delay-600">
                <div class="info-icon">
                    <i class="fas fa-gamepad"></i>
                </div>
                <h3 class="info-title">Режим игры</h3>
                <div class="info-value">Survival</div>
                <p class="info-description">Классическое выживание с добычей ресурсов и строительством</p>
            </div>

            <!-- Время работы -->
            <div class="info-card animate-fade-in-up delay-700">
                <div class="info-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <h3 class="info-title">Время работы</h3>
                <div class="info-value">24/7</div>
                <p class="info-description">Сервер работает круглосуточно без перерывов и выходных</p>
            </div>
        </div>
    </div>
</section>

<!-- Кнопка прокрутки вверх -->
<div class="scroll-top" id="scrollTopBtn">
    <i class="fas fa-arrow-up"></i>
    </div>

    <script>
    // Создаем анимированные частицы
    document.addEventListener('DOMContentLoaded', function() {
        const particlesContainer = document.getElementById('particles');
        if (!particlesContainer) return;
        
        const colors = [
            'rgba(0, 255, 0, 0.4)',
            'rgba(50, 205, 50, 0.4)',
            'rgba(173, 255, 47, 0.4)',
            'rgba(152, 251, 152, 0.4)'
        ];
        
        // Уменьшаем количество частиц на мобильных устройствах
        const isMobile = window.innerWidth <= 768;
        const particleCount = isMobile ? 20 : 50;

        for (let i = 0; i < particleCount; i++) {
            const size = Math.random() * 6 + 3;
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.width = `${size}px`;
            particle.style.height = `${size}px`;
            particle.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
            particle.style.left = `${Math.random() * 100}%`;
            particle.style.setProperty('--rand-x', Math.random());
            particle.style.boxShadow = `0 0 ${size}px ${particle.style.backgroundColor}`;
            particle.style.animationDuration = `${Math.random() * 20 + 10}s`;
            particle.style.animationDelay = `${Math.random() * 5}s`;
            
            particlesContainer.appendChild(particle);
        }
        
        // Логика для кнопки прокрутки вверх
        const scrollTopBtn = document.getElementById('scrollTopBtn');
        
        // Показываем кнопку, когда пользователь прокрутил немного вниз
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                scrollTopBtn.classList.add('active');
            } else {
                scrollTopBtn.classList.remove('active');
            }
        });
        
        // Прокрутка вверх при клике на кнопку
        scrollTopBtn.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    });
    
    // Функция для копирования IP сервера
    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(() => {
            // Показываем уведомление
            showToast('IP адрес скопирован!', 'success');
        }).catch(err => {
            console.error('Ошибка при копировании: ', err);
        });
    }
    
    // Простая функция для отображения тоста
    function showToast(message, type = 'success') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type} show`;
        toast.innerHTML = `<i class="fas fa-${type === 'success' ? 'check' : 'info'}-circle"></i> ${message}`;
        
        // Создаем контейнер для тостов, если его нет
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container';
            document.body.appendChild(toastContainer);
        }
        
        toastContainer.appendChild(toast);
        
        // Автоматически удаляем через 3 секунды
                setTimeout(() => {
            toast.classList.remove('show');
                    setTimeout(() => {
                toast.remove();
                if (toastContainer.children.length === 0) {
                    toastContainer.remove();
                }
            }, 300);
        }, 3000);
    }


    </script>
{% endblock %}
