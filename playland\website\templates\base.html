<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#121212">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <title>{% block title %}PLAYLAND{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='images/favicon.ico') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='images/favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ url_for('static', filename='images/favicon-16x16.png') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ url_for('static', filename='images/apple-touch-icon.png') }}">

    <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&family=Roboto:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <script src="https://unpkg.com/@barba/core"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.11.5/gsap.min.js"></script>
    {% block extra_head %}{% endblock %}
</head>
<body data-barba="wrapper">
    <header class="header">
        <div class="header-left">
            <a href="{{ url_for('index') }}" class="logo">
                <img src="{{ url_for('static', filename='images/playland_logo.png') }}" alt="PLAYLAND Logo" class="logo-image">
                <div class="logo-text-container">
                    <span class="site-title">PLAYLAND</span>
                </div>
            </a>
        </div>
        <nav>
            <ul class="nav-links">
                <li><a href="{{ url_for('index') }}" {% if request.path == url_for('index') %}class="active"{% endif %}>Главная</a></li>
                <li><a href="{{ url_for('application') }}" {% if request.path == url_for('application') %}class="active"{% endif %}>Подать заявку</a></li>
                <li><a href="{{ url_for('tickets') }}" {% if request.path == url_for('tickets') %}class="active"{% endif %}>Поддержка</a></li>
                <li><a href="{{ url_for('rules') }}" {% if request.path == url_for('rules') %}class="active"{% endif %}>Правила</a></li>
                <li><a href="{{ url_for('team') }}" {% if request.path == url_for('team') %}class="active"{% endif %}>Команда</a></li>
                <li><a href="{{ url_for('gallery') }}" {% if request.path == url_for('gallery') %}class="active"{% endif %}>Галерея</a></li>
                <li><a href="{{ url_for('faq') }}" {% if request.path == url_for('faq') %}class="active"{% endif %}>FAQ</a></li>
                {% if current_user.is_authenticated %}
                    <li><a href="{{ url_for('profile.view_profile') }}" {% if request.path == url_for('profile.view_profile') %}class="active"{% endif %}>Профиль</a></li>
                    <li><a href="{{ url_for('logout') }}">Выйти</a></li>
                {% else %}
                    <li><a href="{{ url_for('login') }}" {% if request.path == url_for('login') %}class="active"{% endif %}>Войти</a></li>
                    <li><a href="{{ url_for('register') }}" {% if request.path == url_for('register') %}class="active"{% endif %}>Регистрация</a></li>
                {% endif %}
            </ul>
            <div class="burger">
                <div></div>
                <div></div>
                <div></div>
            </div>
        </nav>
    </header>

    <main data-barba="container" data-barba-namespace="{% block namespace %}default{% endblock %}">
        {% block content %}{% endblock %}
    </main>

    <div class="page-transition-overlay">
        <img class="page-transition-image" src="{{ url_for('static', filename='images/playland_logo.png') }}" alt="Загрузка">
    </div>

    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Инициализация barba.js
            barba.init({
                transitions: [{
                    name: 'fade-transition',
                    leave(data) {
                        const done = this.async();
                        const overlay = document.querySelector('.page-transition-overlay');
                        overlay.style.left = '0';
                        
                        gsap.to(data.current.container, {
                            opacity: 0,
                            duration: 0.5,
                            onComplete: done
                        });
                    },
                    enter(data) {
                        const done = this.async();
                        const overlay = document.querySelector('.page-transition-overlay');
                        
                        gsap.fromTo(
                            data.next.container, 
                            { opacity: 0 },
                            { 
                                opacity: 1, 
                                duration: 0.5,
                                onComplete: function() {
                                    overlay.style.left = '-100%';
                                    done();
                                }
                            }
                        );
                    }
                }]
            });
            
            // Новое мобильное меню
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');
            const mobileNav = document.getElementById('mobileNav');
            const mobileNavLinks = document.querySelectorAll('.mobile-nav-menu a');

            if (mobileMenuToggle && mobileNav) {
                // Функция для открытия/закрытия меню
                function toggleMobileMenu() {
                    mobileMenuToggle.classList.toggle('active');
                    mobileNav.classList.toggle('active');
                    document.body.classList.toggle('mobile-menu-open');
                }

                // Обработчик клика по кнопке меню
                mobileMenuToggle.addEventListener('click', toggleMobileMenu);

                // Закрывать меню при клике на ссылку
                mobileNavLinks.forEach(link => {
                    link.addEventListener('click', function() {
                        toggleMobileMenu();
                    });
                });

                // Закрывать меню при клике вне его
                document.addEventListener('click', function(e) {
                    if (!mobileMenuToggle.contains(e.target) && !mobileNav.contains(e.target)) {
                        if (mobileNav.classList.contains('active')) {
                            toggleMobileMenu();
                        }
                    }
                });

                // Закрывать меню при изменении размера экрана
                window.addEventListener('resize', function() {
                    if (window.innerWidth > 768 && mobileNav.classList.contains('active')) {
                        toggleMobileMenu();
                    }
                });
            }
            
            // Исправление бага с 100vh на мобильных устройствах
            function setVh() {
                let vh = window.innerHeight * 0.01;
                document.documentElement.style.setProperty('--vh', `${vh}px`);
            }
            
            setVh();
            window.addEventListener('resize', setVh);
        });
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html> 