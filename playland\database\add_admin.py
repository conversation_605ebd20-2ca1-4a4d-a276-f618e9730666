#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Скрипт для добавления администратора в базу данных PlayLand
Использование: python add_admin.py <email> <password>
"""

import os
import sys
import sqlite3
from werkzeug.security import generate_password_hash
from datetime import datetime

if len(sys.argv) < 3:
    print("Ошибка: не указаны email и пароль")
    print("Использование: python add_admin.py <email> <password>")
    sys.exit(1)

def add_admin(email, password):
    try:
        # Исправленный путь к базе данных (она находится в подкаталоге instance текущей директории)
        db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'instance', 'playland.db')
        
        if not os.path.exists(db_path):
            print(f"Ошибка: файл базы данных не найден по пути {db_path}")
            sys.exit(1)
            
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Проверяем, существует ли пользователь с указанным email
        cursor.execute("SELECT id FROM users WHERE email = ?", (email,))
        user = cursor.fetchone()
        
        if user:
            # Если пользователь существует, обновляем его до администратора
            user_id = user[0]
            cursor.execute("UPDATE users SET is_admin = 1 WHERE id = ?", (user_id,))
            print(f"Пользователь с email {email} обновлён до администратора!")
        else:
            # Если пользователь не существует, создаём нового администратора
            username = email.split('@')[0]
            nickname = username
            password_hash = generate_password_hash(password)
            now = datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')

            cursor.execute(
                "INSERT INTO users (username, email, password_hash, created_at, is_admin, is_activated, nickname, telegram_id) "
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
                (username, email, password_hash, now, 1, 1, nickname, f"admin_{username}")
            )
            print(f"Администратор с email {email} успешно создан!")
            
        conn.commit()
        conn.close()
        
    except Exception as e:
        print(f"Ошибка при добавлении администратора: {e}")
        sys.exit(1)

if __name__ == "__main__":
    email = sys.argv[1]
    password = sys.argv[2]
    add_admin(email, password) 