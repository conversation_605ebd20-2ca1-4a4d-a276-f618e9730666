#!/usr/bin/env python3
"""
Скрипт развертывания PlayLand на сервере.
Настраивает окружение и запускает приложение.
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

def setup_logging():
    """Настройка логирования"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('deploy.log')
        ]
    )
    return logging.getLogger(__name__)

def check_python_version():
    """Проверка версии Python"""
    if sys.version_info < (3, 8):
        raise RuntimeError("Требуется Python 3.8 или выше")

def install_dependencies():
    """Установка зависимостей"""
    logger.info("📦 Установка зависимостей...")
    
    # Проверяем наличие requirements.txt
    if not Path("requirements.txt").exists():
        logger.error("❌ Файл requirements.txt не найден!")
        return False
    
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], check=True)
        logger.info("✅ Зависимости установлены")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Ошибка установки зависимостей: {e}")
        return False

def setup_environment():
    """Настройка окружения"""
    logger.info("🔧 Настройка окружения...")
    
    # Создаем необходимые папки
    dirs_to_create = [
        "logs",
        "playland/database",
        "playland/website/static/uploads",
        "playland/website/instance"
    ]
    
    for dir_path in dirs_to_create:
        os.makedirs(dir_path, exist_ok=True)
        logger.info(f"📁 Создана папка: {dir_path}")
    
    # Проверяем переменные окружения
    required_env_vars = [
        "TELEGRAM_BOT_TOKEN"
    ]
    
    missing_vars = []
    for var in required_env_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.warning(f"⚠️ Отсутствуют переменные окружения: {', '.join(missing_vars)}")
        logger.info("💡 Создайте файл .env или установите переменные окружения")
        return False
    
    logger.info("✅ Окружение настроено")
    return True

def check_database():
    """Проверка базы данных"""
    logger.info("📊 Проверка базы данных...")
    
    try:
        from playland.database.init_db import init_database
        init_database()
        logger.info("✅ База данных готова")
        return True
    except Exception as e:
        logger.error(f"❌ Ошибка базы данных: {e}")
        return False

def main():
    """Главная функция развертывания"""
    global logger
    logger = setup_logging()
    
    logger.info("🚀 Начало развертывания PlayLand...")
    logger.info("=" * 50)
    
    try:
        # Проверка Python
        logger.info("🐍 Проверка версии Python...")
        check_python_version()
        logger.info(f"✅ Python {sys.version}")
        
        # Установка зависимостей
        if not install_dependencies():
            sys.exit(1)
        
        # Настройка окружения
        if not setup_environment():
            logger.warning("⚠️ Проблемы с окружением, но продолжаем...")
        
        # Проверка базы данных
        if not check_database():
            sys.exit(1)
        
        logger.info("🎉 Развертывание завершено успешно!")
        logger.info("💡 Для запуска используйте: python main.py")
        
    except Exception as e:
        logger.error(f"❌ Ошибка развертывания: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
