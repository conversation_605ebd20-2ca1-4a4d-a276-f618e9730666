{% extends "base.html" %}

{% block title %}Настройка 2FA - PlayLand{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0"><i class="fas fa-mobile-alt"></i> Настройка двухфакторной аутентификации</h4>
                </div>
                <div class="card-body">
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    <div class="row">
                        <div class="col-md-6">
                            <h5>Шаг 1: Установите приложение</h5>
                            <p class="text-muted">Установите одно из приложений для двухфакторной аутентификации:</p>
                            <ul>
                                <li><strong>Google Authenticator</strong> (iOS/Android)</li>
                                <li><strong>Authy</strong> (iOS/Android/Desktop)</li>
                                <li><strong>Microsoft Authenticator</strong> (iOS/Android)</li>
                                <li><strong>1Password</strong> (Premium)</li>
                            </ul>

                            <h5 class="mt-4">Шаг 2: Отсканируйте QR-код</h5>
                            <p class="text-muted">Откройте приложение и отсканируйте этот QR-код:</p>
                            
                            {% if qr_code %}
                                <div class="text-center mb-3">
                                    <img src="data:image/png;base64,{{ qr_code }}" alt="QR Code" class="img-fluid" style="max-width: 200px;">
                                </div>
                            {% endif %}

                            <div class="alert alert-info">
                                <h6><i class="fas fa-key"></i> Секретный ключ (для ручного ввода):</h6>
                                <code>{{ secret }}</code>
                                <button type="button" class="btn btn-sm btn-outline-primary ms-2" onclick="copySecret()">
                                    <i class="fas fa-copy"></i> Копировать
                                </button>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <h5>Шаг 3: Введите код подтверждения</h5>
                            <p class="text-muted">Введите 6-значный код из приложения для подтверждения настройки:</p>

                            <form method="POST" action="{{ url_for('profile.two_factor_setup') }}">
                                {{ form.hidden_tag() }}
                                
                                <div class="mb-3">
                                    {{ form.token.label(class="form-label") }}
                                    {{ form.token(class="form-control text-center", style="font-size: 1.5em; letter-spacing: 0.3em;", placeholder="000000", maxlength="6") }}
                                    {% if form.token.errors %}
                                        <div class="text-danger">
                                            {% for error in form.token.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="d-grid">
                                    {{ form.submit(class="btn btn-success btn-lg") }}
                                </div>
                            </form>

                            <div class="mt-4">
                                <div class="alert alert-warning">
                                    <h6><i class="fas fa-exclamation-triangle"></i> Важно!</h6>
                                    <ul class="mb-0">
                                        <li>Сохраните секретный ключ в безопасном месте</li>
                                        <li>После настройки вы получите резервные коды</li>
                                        <li>Не потеряйте доступ к приложению аутентификации</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <div class="text-center">
                        <a href="{{ url_for('profile.security_settings') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Вернуться к настройкам
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function copySecret() {
    const secretText = "{{ secret }}";
    navigator.clipboard.writeText(secretText).then(function() {
        // Показываем уведомление об успешном копировании
        const button = event.target.closest('button');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i> Скопировано!';
        button.classList.remove('btn-outline-primary');
        button.classList.add('btn-success');
        
        setTimeout(() => {
            button.innerHTML = originalText;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-primary');
        }, 2000);
    }).catch(function(err) {
        console.error('Ошибка копирования: ', err);
        // Fallback для старых браузеров
        const textArea = document.createElement('textarea');
        textArea.value = secretText;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        
        alert('Секретный ключ скопирован в буфер обмена');
    });
}

// Автофокус на поле ввода кода
document.addEventListener('DOMContentLoaded', function() {
    const tokenInput = document.getElementById('token');
    if (tokenInput) {
        tokenInput.focus();
        
        // Автоматическая отправка формы при вводе 6 цифр
        tokenInput.addEventListener('input', function() {
            if (this.value.length === 6 && /^\d{6}$/.test(this.value)) {
                // Небольшая задержка для лучшего UX
                setTimeout(() => {
                    this.form.submit();
                }, 500);
            }
        });
    }
});
</script>

<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.alert {
    border-radius: 0.375rem;
}

.btn {
    border-radius: 0.375rem;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

code {
    background-color: #f8f9fa;
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-family: 'Courier New', monospace;
    word-break: break-all;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.btn-success {
    background-color: #28a745;
    border-color: #28a745;
}

.btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
}
</style>
{% endblock %}
