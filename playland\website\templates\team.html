{% extends "base.html" %}

{% block title %}PLAYLAND - Наша команда{% endblock %}

{% block namespace %}team{% endblock %}

{% block extra_head %}
<style>
    .hero {
        background-image: url("{{ url_for('static', filename='images/grass_background.jpg') }}");
        background-size: cover;
        background-position: center center;
        background-attachment: fixed;
        height: 50vh;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        padding: 20px;
        padding-top: 100px;
        box-sizing: border-box;
        position: relative;
        overflow: hidden;
    }
    
    .hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.6);
        backdrop-filter: blur(2px);
        z-index: 1;
    }
    
    .hero-content {
        position: relative;
        z-index: 2;
        max-width: 900px;
        width: 100%;
    }
    
    .hero h1 {
        font-family: 'Press Start 2P', cursive;
        font-size: 3.5em;
        color: #00ff00;
        text-shadow: 2px 2px 0 #000000, 0 0 15px #00ff00;
        margin-bottom: 20px;
        line-height: 1.3;
        animation: textPopIn 0.8s cubic-bezier(0.68, -0.55, 0.27, 1.55) 0.5s forwards, heroGlow 2.5s infinite alternate 1s;
        opacity: 0;
        transform: scale(0.5);
    }
    
    @keyframes textPopIn {
        to {
            opacity: 1;
            transform: scale(1);
        }
    }
    
    @keyframes heroGlow {
        0% {
            text-shadow: 2px 2px 0 #000000, 0 0 5px rgba(0, 255, 0, 0.5); 
        }
        100% {
            text-shadow: 2px 2px 0 #000000, 0 0 15px #00ff00, 0 0 30px #00ff00, 0 0 40px #00ff00;
        }
    }

    .content-section {
        padding: 80px 20px;
        background-color: rgba(0, 0, 0, 0.8);
    }
    
    .content-section h2 {
        font-family: 'Press Start 2P', cursive;
        font-size: 2.2em;
        color: #00ff00;
        text-align: center;
        margin-bottom: 40px;
        text-shadow: 0 0 10px #00ff00;
    }
    
    .team-container {
        max-width: 1200px;
        margin: 0 auto;
    }
    
    .team-intro {
        max-width: 800px;
        margin: 0 auto 50px;
        text-align: center;
        color: #f0f0f0;
        line-height: 1.6;
    }
    
    .team-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 30px;
        margin-bottom: 50px;
    }
    
    .team-member {
        background-color: rgba(0, 0, 0, 0.5);
        border: 2px solid #00ff00;
        border-radius: 10px;
        overflow: hidden;
        transition: all 0.3s ease;
        display: flex;
        flex-direction: column;
        box-shadow: 0 0 15px rgba(0, 255, 0, 0.1);
    }
    
    .team-member:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 30px rgba(0, 255, 0, 0.2);
    }
    
    .member-header {
        position: relative;
        height: 200px;
        overflow: hidden;
    }
    
    .member-header img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
    }
    
    .team-member:hover .member-header img {
        transform: scale(1.1);
    }
    
    .member-info {
        padding: 20px;
        flex-grow: 1;
        display: flex;
        flex-direction: column;
    }
    
    .member-name {
        font-family: 'Press Start 2P', cursive;
        font-size: 1.2em;
        color: #00ff00;
        margin: 0 0 10px;
        text-shadow: 0 0 5px rgba(0, 255, 0, 0.5);
    }
    
    .member-role {
        color: #f0f0f0;
        font-size: 0.9em;
        margin-bottom: 15px;
        padding-bottom: 15px;
        border-bottom: 1px dashed rgba(0, 255, 0, 0.3);
    }
    
    .member-bio {
        color: #cccccc;
        margin-bottom: 20px;
        line-height: 1.6;
        flex-grow: 1;
    }
    
    .member-social {
        display: flex;
        gap: 10px;
        margin-top: auto;
    }
    
    .social-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        background-color: rgba(0, 0, 0, 0.3);
        color: #00ff00;
        border: 1px solid #00ff00;
        border-radius: 50%;
        transition: all 0.3s;
    }
    
    .social-icon:hover {
        background-color: #00ff00;
        color: #000;
        box-shadow: 0 0 10px rgba(0, 255, 0, 0.6);
    }
    
    .join-team-section {
        text-align: center;
        margin-top: 60px;
        padding: 40px;
        background-color: rgba(0, 0, 0, 0.5);
        border: 2px solid #00ff00;
        border-radius: 10px;
    }
    
    .join-team-section h3 {
        font-family: 'Press Start 2P', cursive;
        font-size: 1.6em;
        color: #00ff00;
        margin-bottom: 20px;
        text-shadow: 0 0 8px rgba(0, 255, 0, 0.5);
    }
    
    .join-team-section p {
        color: #f0f0f0;
        max-width: 700px;
        margin: 0 auto 30px;
        line-height: 1.6;
    }
    
    .btn-minecraft {
        display: inline-block;
        padding: 15px 30px;
        background-color: #00ff00;
        color: #000;
        text-decoration: none;
        font-family: 'Press Start 2P', cursive;
        font-size: 0.9em;
        text-transform: uppercase;
        border: none;
        position: relative;
        overflow: hidden;
        transition: all 0.3s;
        box-shadow: 0 4px 0 #006400, 0 0 20px rgba(0, 255, 0, 0.4);
        border-radius: 3px;
    }

    .btn-minecraft:hover {
        transform: translateY(-5px);
        box-shadow: 0 9px 0 #006400, 0 0 30px rgba(0, 255, 0, 0.6);
    }

    .empty-section {
        text-align: center;
        padding: 40px 20px;
        color: #888;
        font-size: 1.2rem;
        font-style: italic;
        grid-column: 1 / -1;
    }

    .empty-section p {
        margin: 0;
        color: #666;
    }

    @media (max-width: 768px) {
        .hero h1 {
            font-size: 2.2em;
        }
        
        .content-section h2 {
            font-size: 1.8em;
        }
        
        .team-grid {
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        }
        
        .join-team-section {
            padding: 20px;
        }
        
        .join-team-section h3 {
            font-size: 1.3em;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="hero">
    <div class="particles-container" id="particles"></div>
    <div class="hero-content">
        <h1 class="animate-fade-in-up">НАША КОМАНДА</h1>
    </div>
</section>

<section class="content-section">
    <div class="team-container">
        <div class="team-intro animate-fade-in">
            <p>Познакомьтесь с командой PlayLand - это увлеченные игроки и профессионалы, которые обеспечивают работу нашего сервера и создают уникальную атмосферу для всех игроков.</p>
        </div>
        
        <h2 class="animate-fade-in">Администрация</h2>
        <div class="team-grid">
            {% for member in team_data.admins %}
            <div class="team-member animate-fade-in-up" style="--delay: {{ loop.index }}">
                <div class="member-header">
                    <img src="{{ url_for('static', filename=member.avatar) }}" alt="{{ member.name }}">
                </div>
                <div class="member-info">
                    <h3 class="member-name">{{ member.name }}</h3>
                    <div class="member-role">{{ member.role }}</div>
                    <div class="member-bio">
                        <p>{{ member.description }}</p>
                    </div>
                    <div class="member-social">
                        {% if member.socials is defined and member.socials %}
                            {% for social in member.socials %}
                                <a href="{{ social.url }}" class="social-icon" target="_blank" title="{{ social.platform }}">
                                    <i class="{{ social.icon }}"></i>
                                </a>
                            {% endfor %}
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <h2 class="animate-fade-in">Модераторы</h2>
        <div class="team-grid">
            {% if team_data.moderators %}
                {% for member in team_data.moderators %}
                <div class="team-member animate-fade-in-up" style="--delay: {{ loop.index }}">
                    <div class="member-header">
                        <img src="{{ url_for('static', filename=member.avatar) }}" alt="{{ member.name }}">
                    </div>
                    <div class="member-info">
                        <h3 class="member-name">{{ member.name }}</h3>
                        <div class="member-role">{{ member.role }}</div>
                        <div class="member-bio">
                            <p>{{ member.description }}</p>
                        </div>
                        <div class="member-social">
                            {% if member.socials is defined and member.socials %}
                                {% for social in member.socials %}
                                    <a href="{{ social.url }}" class="social-icon" target="_blank" title="{{ social.platform }}">
                                        <i class="{{ social.icon }}"></i>
                                    </a>
                                {% endfor %}
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="empty-section animate-fade-in">
                    <p>Нету</p>
                </div>
            {% endif %}
        </div>
        
        <h2 class="animate-fade-in">Хелперы</h2>
        <div class="team-grid">
            {% if team_data.helpers %}
                {% for member in team_data.helpers %}
                <div class="team-member animate-fade-in-up" style="--delay: {{ loop.index }}">
                    <div class="member-header">
                        <img src="{{ url_for('static', filename=member.avatar) }}" alt="{{ member.name }}">
                    </div>
                    <div class="member-info">
                        <h3 class="member-name">{{ member.name }}</h3>
                        <div class="member-role">{{ member.role }}</div>
                        <div class="member-bio">
                            <p>{{ member.description }}</p>
                        </div>
                        <div class="member-social">
                            {% if member.socials is defined and member.socials %}
                                {% for social in member.socials %}
                                    <a href="{{ social.url }}" class="social-icon" target="_blank" title="{{ social.platform }}">
                                        <i class="{{ social.icon }}"></i>
                                    </a>
                                {% endfor %}
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="empty-section animate-fade-in">
                    <p>Нету</p>
                </div>
            {% endif %}
        </div>
        
        <div class="join-team-section animate-fade-in-up">
            <h3>Хотите присоединиться к нашей команде?</h3>
            <p>Мы всегда в поиске активных и ответственных игроков, готовых помогать развитию сервера и сообщества. Если вы любите Minecraft, хорошо общаетесь с людьми и готовы уделять время серверу - мы будем рады видеть вас в нашей команде!</p>
            <a href="{{ url_for('team_application') }}" class="btn-minecraft">Подать заявку</a>
        </div>
    </div>
</section>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Создаем частицы
        const particlesContainer = document.getElementById('particles');
        if (!particlesContainer) return;
        
        const colors = [
            'rgba(0, 255, 0, 0.4)',
            'rgba(50, 205, 50, 0.4)',
            'rgba(173, 255, 47, 0.4)',
            'rgba(152, 251, 152, 0.4)'
        ];
        
        for (let i = 0; i < 50; i++) {
            const size = Math.random() * 6 + 3;
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.width = `${size}px`;
            particle.style.height = `${size}px`;
            particle.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
            particle.style.left = `${Math.random() * 100}%`;
            particle.style.setProperty('--rand-x', Math.random());
            particle.style.boxShadow = `0 0 ${size}px ${particle.style.backgroundColor}`;
            particle.style.animationDuration = `${Math.random() * 20 + 10}s`;
            particle.style.animationDelay = `${Math.random() * 5}s`;
            
            particlesContainer.appendChild(particle);
        }
    });
</script>
{% endblock %} 