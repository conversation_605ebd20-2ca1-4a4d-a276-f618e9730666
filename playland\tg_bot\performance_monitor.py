#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Модуль для мониторинга производительности Telegram бота
"""

import os
import sys
import time
import json
import logging
import datetime
import threading
from collections import defaultdict, deque
from functools import wraps

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('performance.log')
    ]
)
logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """
    Класс для мониторинга производительности Telegram бота
    """
    def __init__(self, max_records=1000, stats_interval=300):
        """
        Инициализация монитора производительности
        
        Args:
            max_records (int): Максимальное количество записей для хранения
            stats_interval (int): Интервал для логирования статистики в секундах
        """
        self.command_times = defaultdict(lambda: deque(maxlen=max_records))
        self.api_call_times = defaultdict(lambda: deque(maxlen=max_records))
        self.errors = defaultdict(lambda: deque(maxlen=max_records))
        self.requests_per_second = defaultdict(int)
        self.requests_timestamps = deque(maxlen=max_records * 10)
        self.lock = threading.Lock()
        self.stats_interval = stats_interval
        self.start_time = time.time()
        
        # Запускаем поток для периодического сохранения статистики
        self.running = True
        self.stats_thread = threading.Thread(target=self._stats_thread)
        self.stats_thread.daemon = True
        self.stats_thread.start()
    
    def record_command(self, command_name, execution_time, success=True, user_id=None):
        """
        Записывает статистику выполнения команды
        
        Args:
            command_name (str): Название команды
            execution_time (float): Время выполнения в секундах
            success (bool): Успешно ли выполнена команда
            user_id (int): ID пользователя
        """
        with self.lock:
            timestamp = time.time()
            self.command_times[command_name].append((timestamp, execution_time, success))
            self.requests_timestamps.append(timestamp)
            
            # Обновляем RPS (запросы в секунду)
            current_second = int(timestamp)
            self.requests_per_second[current_second] += 1
    
    def record_api_call(self, method_name, execution_time, success=True):
        """
        Записывает статистику вызова API Telegram
        
        Args:
            method_name (str): Название метода API
            execution_time (float): Время выполнения в секундах
            success (bool): Успешно ли выполнен вызов
        """
        with self.lock:
            timestamp = time.time()
            self.api_call_times[method_name].append((timestamp, execution_time, success))
    
    def record_error(self, error_type, details=None):
        """
        Записывает информацию об ошибке
        
        Args:
            error_type (str): Тип ошибки
            details (str): Подробная информация об ошибке
        """
        with self.lock:
            timestamp = time.time()
            self.errors[error_type].append((timestamp, details))
    
    def get_command_stats(self, command_name=None):
        """
        Возвращает статистику выполнения команд
        
        Args:
            command_name (str, optional): Название конкретной команды или None для всех команд
            
        Returns:
            dict: Статистика выполнения команд
        """
        with self.lock:
            result = {}
            
            if command_name:
                # Статистика для конкретной команды
                if command_name in self.command_times:
                    times = [t for _, t, s in self.command_times[command_name] if s]
                    if not times:
                        return {"count": 0, "avg_time": 0, "min_time": 0, "max_time": 0, "success_rate": 0}
                    
                    success_count = sum(1 for _, _, s in self.command_times[command_name] if s)
                    total_count = len(self.command_times[command_name])
                    
                    result = {
                        "count": total_count,
                        "avg_time": sum(times) / len(times) if times else 0,
                        "min_time": min(times) if times else 0,
                        "max_time": max(times) if times else 0,
                        "success_rate": success_count / total_count if total_count > 0 else 0
                    }
            else:
                # Статистика для всех команд
                for cmd, data in self.command_times.items():
                    times = [t for _, t, s in data if s]
                    if not times:
                        result[cmd] = {"count": 0, "avg_time": 0, "min_time": 0, "max_time": 0, "success_rate": 0}
                        continue
                    
                    success_count = sum(1 for _, _, s in data if s)
                    total_count = len(data)
                    
                    result[cmd] = {
                        "count": total_count,
                        "avg_time": sum(times) / len(times) if times else 0,
                        "min_time": min(times) if times else 0,
                        "max_time": max(times) if times else 0,
                        "success_rate": success_count / total_count if total_count > 0 else 0
                    }
            
            return result
    
    def get_api_stats(self, method_name=None):
        """
        Возвращает статистику вызовов API
        
        Args:
            method_name (str, optional): Название конкретного метода API или None для всех методов
            
        Returns:
            dict: Статистика вызовов API
        """
        with self.lock:
            result = {}
            
            if method_name:
                # Статистика для конкретного метода
                if method_name in self.api_call_times:
                    times = [t for _, t, s in self.api_call_times[method_name] if s]
                    if not times:
                        return {"count": 0, "avg_time": 0, "min_time": 0, "max_time": 0, "success_rate": 0}
                    
                    success_count = sum(1 for _, _, s in self.api_call_times[method_name] if s)
                    total_count = len(self.api_call_times[method_name])
                    
                    result = {
                        "count": total_count,
                        "avg_time": sum(times) / len(times) if times else 0,
                        "min_time": min(times) if times else 0,
                        "max_time": max(times) if times else 0,
                        "success_rate": success_count / total_count if total_count > 0 else 0
                    }
            else:
                # Статистика для всех методов
                for method, data in self.api_call_times.items():
                    times = [t for _, t, s in data if s]
                    if not times:
                        result[method] = {"count": 0, "avg_time": 0, "min_time": 0, "max_time": 0, "success_rate": 0}
                        continue
                    
                    success_count = sum(1 for _, _, s in data if s)
                    total_count = len(data)
                    
                    result[method] = {
                        "count": total_count,
                        "avg_time": sum(times) / len(times) if times else 0,
                        "min_time": min(times) if times else 0,
                        "max_time": max(times) if times else 0,
                        "success_rate": success_count / total_count if total_count > 0 else 0
                    }
            
            return result
    
    def get_error_stats(self):
        """
        Возвращает статистику по ошибкам
        
        Returns:
            dict: Статистика по ошибкам
        """
        with self.lock:
            result = {}
            
            for error_type, errors in self.errors.items():
                result[error_type] = {
                    "count": len(errors),
                    "last_occurrence": errors[-1][0] if errors else None,
                    "last_details": errors[-1][1] if errors else None
                }
            
            return result
    
    def get_requests_per_second(self, window=60):
        """
        Возвращает среднее количество запросов в секунду за последние window секунд
        
        Args:
            window (int): Окно в секундах
            
        Returns:
            float: Среднее количество запросов в секунду
        """
        with self.lock:
            current_time = time.time()
            # Считаем запросы за последние window секунд
            count = sum(1 for t in self.requests_timestamps if current_time - t <= window)
            return count / window
    
    def get_uptime(self):
        """
        Возвращает время работы бота
        
        Returns:
            float: Время работы в секундах
        """
        return time.time() - self.start_time
    
    def save_stats(self, file_path='performance_stats.json'):
        """
        Сохраняет статистику в файл JSON
        
        Args:
            file_path (str): Путь к файлу для сохранения статистики
        """
        stats = {
            'timestamp': datetime.datetime.now().isoformat(),
            'uptime': self.get_uptime(),
            'requests_per_second': self.get_requests_per_second(),
            'command_stats': self.get_command_stats(),
            'api_stats': self.get_api_stats(),
            'error_stats': self.get_error_stats()
        }
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(stats, f, indent=2)
            logger.info(f"Статистика сохранена в {file_path}")
        except Exception as e:
            logger.error(f"Ошибка при сохранении статистики: {str(e)}")
    
    def _stats_thread(self):
        """Поток для периодического логирования и сохранения статистики"""
        while self.running:
            time.sleep(self.stats_interval)
            try:
                self._log_stats()
                self.save_stats()
            except Exception as e:
                logger.error(f"Ошибка в потоке статистики: {str(e)}")
    
    def _log_stats(self):
        """Логирует текущую статистику"""
        rps = self.get_requests_per_second()
        uptime = self.get_uptime()
        uptime_str = str(datetime.timedelta(seconds=int(uptime)))
        
        logger.info(f"=== Статистика бота ===")
        logger.info(f"Время работы: {uptime_str}")
        logger.info(f"Запросов в секунду: {rps:.2f}")
        
        # Логируем топ-5 самых медленных команд
        command_stats = self.get_command_stats()
        if command_stats:
            slow_commands = sorted(
                [(cmd, data['avg_time']) for cmd, data in command_stats.items()],
                key=lambda x: x[1],
                reverse=True
            )[:5]
            
            logger.info("Самые медленные команды:")
            for cmd, avg_time in slow_commands:
                count = command_stats[cmd]['count']
                logger.info(f"  {cmd}: {avg_time*1000:.2f} мс (вызвано {count} раз)")
        
        # Логируем ошибки
        error_stats = self.get_error_stats()
        if error_stats:
            logger.info("Статистика ошибок:")
            for error_type, data in error_stats.items():
                count = data['count']
                last_time = datetime.datetime.fromtimestamp(data['last_occurrence']).strftime('%Y-%m-%d %H:%M:%S') if data['last_occurrence'] else 'N/A'
                logger.info(f"  {error_type}: {count} ошибок (последняя: {last_time})")
    
    def stop(self):
        """Останавливает монитор производительности"""
        self.running = False
        if self.stats_thread.is_alive():
            self.stats_thread.join(timeout=1)
        self.save_stats()

# Создаем глобальный экземпляр монитора
monitor = PerformanceMonitor()

def measure_command(command_name):
    """
    Декоратор для измерения времени выполнения команды
    
    Args:
        command_name (str): Название команды
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(message, *args, **kwargs):
            start_time = time.time()
            success = True
            
            try:
                result = await func(message, *args, **kwargs)
                return result
            except Exception as e:
                success = False
                monitor.record_error(f"command_error_{command_name}", str(e))
                raise
            finally:
                execution_time = time.time() - start_time
                monitor.record_command(
                    command_name,
                    execution_time,
                    success=success,
                    user_id=message.from_user.id if hasattr(message, 'from_user') else None
                )
        
        return wrapper
    
    return decorator

def measure_api_call(method_name):
    """
    Декоратор для измерения времени выполнения API-вызова
    
    Args:
        method_name (str): Название метода API
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            success = True
            
            try:
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                monitor.record_error(f"api_error_{method_name}", str(e))
                raise
            finally:
                execution_time = time.time() - start_time
                monitor.record_api_call(method_name, execution_time, success=success)
        
        return wrapper
    
    return decorator

# Функции для интеграции с Flask
def init_flask_monitoring(app):
    """
    Добавляет маршруты для мониторинга в Flask-приложение
    
    Args:
        app: Экземпляр Flask
    """
    @app.route('/monitoring/stats', methods=['GET'])
    def get_stats():
        """Возвращает текущую статистику производительности"""
        from flask import jsonify
        
        stats = {
            'timestamp': datetime.datetime.now().isoformat(),
            'uptime': monitor.get_uptime(),
            'requests_per_second': monitor.get_requests_per_second(),
            'command_stats': monitor.get_command_stats(),
            'api_stats': monitor.get_api_stats(),
            'error_stats': monitor.get_error_stats()
        }
        
        return jsonify(stats)
    
    @app.route('/monitoring/dashboard', methods=['GET'])
    def show_dashboard():
        """Отображает простую HTML-страницу с дашбордом"""
        from flask import render_template_string
        
        # Готовим данные для дашборда
        uptime = monitor.get_uptime()
        uptime_str = str(datetime.timedelta(seconds=int(uptime)))
        rps = monitor.get_requests_per_second()
        
        # Получаем статистику команд
        command_stats = monitor.get_command_stats()
        sorted_commands = sorted(
            [(cmd, data) for cmd, data in command_stats.items()],
            key=lambda x: x[1]['count'],
            reverse=True
        )
        
        # Получаем статистику ошибок
        error_stats = monitor.get_error_stats()
        
        # HTML-шаблон для дашборда
        template = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Telegram Bot Monitoring</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; line-height: 1.6; }
                h1 { color: #333; }
                .stats-container { display: flex; flex-wrap: wrap; gap: 20px; }
                .stats-box { 
                    flex: 1; 
                    min-width: 250px; 
                    padding: 15px; 
                    border: 1px solid #ddd; 
                    border-radius: 4px; 
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1); 
                }
                table { width: 100%; border-collapse: collapse; margin-top: 10px; }
                th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
                th { background-color: #f2f2f2; }
                tr:hover { background-color: #f5f5f5; }
                .error { color: #d32f2f; }
                .good { color: #388e3c; }
                .refresh-btn {
                    background-color: #4CAF50;
                    border: none;
                    color: white;
                    padding: 10px 20px;
                    text-align: center;
                    text-decoration: none;
                    display: inline-block;
                    font-size: 16px;
                    margin: 10px 0;
                    cursor: pointer;
                    border-radius: 4px;
                }
            </style>
        </head>
        <body>
            <h1>Мониторинг Telegram бота</h1>
            <button class="refresh-btn" onclick="window.location.reload()">Обновить</button>
            
            <div class="stats-container">
                <div class="stats-box">
                    <h2>Общая статистика</h2>
                    <p>Время работы: <strong>{{ uptime }}</strong></p>
                    <p>Запросов в секунду: <strong>{{ rps }}{% if rps > 10 %}<span class="error"> (Высокая нагрузка)</span>{% endif %}</strong></p>
                </div>
                
                <div class="stats-box">
                    <h2>Топ команд</h2>
                    <table>
                        <tr>
                            <th>Команда</th>
                            <th>Вызовов</th>
                            <th>Среднее время (мс)</th>
                            <th>Успешность</th>
                        </tr>
                        {% for cmd, data in commands %}
                        <tr>
                            <td>{{ cmd }}</td>
                            <td>{{ data.count }}</td>
                            <td>{{ "%.2f"|format(data.avg_time * 1000) }}{% if data.avg_time > 0.5 %}<span class="error">*</span>{% endif %}</td>
                            <td>{{ "%.1f%%"|format(data.success_rate * 100) }}{% if data.success_rate < 0.9 %}<span class="error">*</span>{% endif %}</td>
                        </tr>
                        {% endfor %}
                    </table>
                </div>
                
                <div class="stats-box">
                    <h2>Ошибки</h2>
                    {% if errors %}
                    <table>
                        <tr>
                            <th>Тип ошибки</th>
                            <th>Количество</th>
                            <th>Последнее появление</th>
                        </tr>
                        {% for error_type, data in errors.items() %}
                        <tr>
                            <td class="error">{{ error_type }}</td>
                            <td>{{ data.count }}</td>
                            <td>{{ data.last_occurrence }}</td>
                        </tr>
                        {% endfor %}
                    </table>
                    {% else %}
                    <p class="good">Ошибок не обнаружено</p>
                    {% endif %}
                </div>
            </div>
        </body>
        </html>
        """
        
        # Форматируем ошибки для отображения
        formatted_errors = {}
        for error_type, data in error_stats.items():
            formatted_errors[error_type] = {
                'count': data['count'],
                'last_occurrence': datetime.datetime.fromtimestamp(data['last_occurrence']).strftime('%Y-%m-%d %H:%M:%S') if data['last_occurrence'] else 'N/A'
            }
        
        # Отображаем дашборд
        return render_template_string(
            template,
            uptime=uptime_str,
            rps=round(rps, 2),
            commands=sorted_commands[:10],  # Только топ-10 команд
            errors=formatted_errors
        )

if __name__ == "__main__":
    # Пример использования
    async def test_function():
        print("Test function")
        await asyncio.sleep(0.1)
    
    @measure_command("test_command")
    async def test_command(message):
        print(f"Test command from {message.from_user.id}")
        await asyncio.sleep(0.2)
    
    @measure_api_call("sendMessage")
    async def test_api_call():
        print("Test API call")
        await asyncio.sleep(0.3)
    
    async def main():
        # Тестируем команды и API-вызовы
        for i in range(10):
            # Создаем имитацию сообщения
            class DummyUser:
                id = 123
                
            class DummyMessage:
                from_user = DummyUser()
            
            await test_command(DummyMessage())
            await test_api_call()
        
        # Ждем, чтобы статистика обновилась
        await asyncio.sleep(2)
        
        # Выводим статистику
        print("\nСтатистика команд:")
        print(json.dumps(monitor.get_command_stats(), indent=2))
        
        print("\nСтатистика API:")
        print(json.dumps(monitor.get_api_stats(), indent=2))
        
        # Останавливаем монитор
        monitor.stop()
    
    import asyncio
    asyncio.run(main()) 