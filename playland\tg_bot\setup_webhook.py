#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Скрипт для настройки вебхука и SSL сертификатов для Telegram бота
"""

import os
import sys
import argparse
import logging
import requests
import subprocess
from dotenv import load_dotenv

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Загрузка переменных окружения
load_dotenv()

def create_self_signed_cert(cert_path, key_path, domain):
    """
    Создает самоподписанный SSL сертификат для вебхука
    
    Args:
        cert_path (str): Путь для сохранения сертификата
        key_path (str): Путь для сохранения приватного ключа
        domain (str): Домен для сертификата
    
    Returns:
        bool: True в случае успеха, False в случае ошибки
    """
    try:
        cmd = [
            'openssl', 'req', '-newkey', 'rsa:2048', '-sha256', '-nodes',
            '-keyout', key_path, '-x509', '-days', '365', '-out', cert_path,
            '-subj', f'/CN={domain}'
        ]
        
        logger.info(f"Создание самоподписанного сертификата для {domain}...")
        result = subprocess.run(cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        if os.path.exists(cert_path) and os.path.exists(key_path):
            logger.info(f"Сертификат успешно создан. Сертификат: {cert_path}, Приватный ключ: {key_path}")
            return True
        else:
            logger.error("Не удалось создать сертификат")
            return False
    except subprocess.CalledProcessError as e:
        logger.error(f"Ошибка при создании сертификата: {e.stderr.decode('utf-8')}")
        return False
    except Exception as e:
        logger.error(f"Ошибка при создании сертификата: {str(e)}")
        return False

def set_webhook(token, webhook_url, cert_path=None):
    """
    Устанавливает вебхук для Telegram бота
    
    Args:
        token (str): Токен Telegram бота
        webhook_url (str): URL для вебхука
        cert_path (str, optional): Путь к сертификату
    
    Returns:
        bool: True в случае успеха, False в случае ошибки
    """
    try:
        # Формируем URL для API Telegram
        api_url = f"https://api.telegram.org/bot{token}/setWebhook"
        
        # Подготавливаем параметры запроса
        params = {
            'url': webhook_url
        }
        
        files = None
        if cert_path and os.path.exists(cert_path):
            files = {
                'certificate': open(cert_path, 'rb')
            }
        
        # Отправляем запрос к API Telegram
        logger.info(f"Установка вебхука для бота на URL: {webhook_url}")
        response = requests.post(api_url, data=params, files=files)
        
        if response.status_code == 200 and response.json().get('ok'):
            logger.info("Вебхук успешно установлен!")
            logger.info(f"Ответ API Telegram: {response.json()}")
            return True
        else:
            logger.error(f"Ошибка при установке вебхука: {response.text}")
            return False
    except Exception as e:
        logger.error(f"Ошибка при установке вебхука: {str(e)}")
        return False

def delete_webhook(token):
    """
    Удаляет вебхук для Telegram бота
    
    Args:
        token (str): Токен Telegram бота
    
    Returns:
        bool: True в случае успеха, False в случае ошибки
    """
    try:
        # Формируем URL для API Telegram
        api_url = f"https://api.telegram.org/bot{token}/deleteWebhook"
        
        # Отправляем запрос к API Telegram
        logger.info("Удаление текущего вебхука...")
        response = requests.get(api_url)
        
        if response.status_code == 200 and response.json().get('ok'):
            logger.info("Вебхук успешно удален!")
            return True
        else:
            logger.error(f"Ошибка при удалении вебхука: {response.text}")
            return False
    except Exception as e:
        logger.error(f"Ошибка при удалении вебхука: {str(e)}")
        return False

def get_webhook_info(token):
    """
    Получает информацию о текущем вебхуке
    
    Args:
        token (str): Токен Telegram бота
    
    Returns:
        dict: Информация о вебхуке или None в случае ошибки
    """
    try:
        # Формируем URL для API Telegram
        api_url = f"https://api.telegram.org/bot{token}/getWebhookInfo"
        
        # Отправляем запрос к API Telegram
        logger.info("Получение информации о текущем вебхуке...")
        response = requests.get(api_url)
        
        if response.status_code == 200 and response.json().get('ok'):
            webhook_info = response.json().get('result', {})
            
            if webhook_info.get('url'):
                logger.info(f"Текущий вебхук: {webhook_info['url']}")
                logger.info(f"Последняя ошибка: {webhook_info.get('last_error_message', 'Нет ошибок')}")
                logger.info(f"Ожидающие обновления: {webhook_info.get('pending_update_count', 0)}")
            else:
                logger.info("Вебхук не установлен")
                
            return webhook_info
        else:
            logger.error(f"Ошибка при получении информации о вебхуке: {response.text}")
            return None
    except Exception as e:
        logger.error(f"Ошибка при получении информации о вебхуке: {str(e)}")
        return None

def update_env_file(webhook_host, webhook_port, cert_path, key_path, bot_token=None):
    """
    Обновляет файл .env с параметрами вебхука
    
    Args:
        webhook_host (str): Домен или IP для вебхука
        webhook_port (int): Порт для вебхука
        cert_path (str): Путь к сертификату
        key_path (str): Путь к приватному ключу
        bot_token (str, optional): Токен бота (если нужно обновить)
    
    Returns:
        bool: True в случае успеха, False в случае ошибки
    """
    try:
        # Путь к файлу .env
        env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
        
        # Читаем текущие значения из .env
        env_vars = {}
        if os.path.exists(env_path):
            with open(env_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        env_vars[key.strip()] = value.strip()
        
        # Обновляем значения
        env_vars['WEBHOOK_HOST'] = webhook_host
        env_vars['WEBHOOK_PORT'] = str(webhook_port)
        env_vars['WEBHOOK_SSL_CERT'] = cert_path
        env_vars['WEBHOOK_SSL_PRIV'] = key_path
        
        if bot_token:
            env_vars['TELEGRAM_BOT_TOKEN'] = bot_token
        
        # Записываем обновленные значения в .env
        with open(env_path, 'w', encoding='utf-8') as f:
            for key, value in env_vars.items():
                f.write(f"{key}={value}\n")
        
        logger.info(f"Файл .env обновлен с параметрами вебхука. Путь: {env_path}")
        return True
    except Exception as e:
        logger.error(f"Ошибка при обновлении файла .env: {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Настройка вебхука для Telegram бота')
    
    parser.add_argument('--host', type=str, help='Домен или IP для вебхука')
    parser.add_argument('--port', type=int, default=8443, help='Порт для вебхука (по умолчанию: 8443)')
    parser.add_argument('--token', type=str, help='Токен Telegram бота')
    parser.add_argument('--cert', type=str, default='./webhook_cert.pem', help='Путь для сохранения сертификата')
    parser.add_argument('--key', type=str, default='./webhook_key.pem', help='Путь для сохранения приватного ключа')
    parser.add_argument('--info', action='store_true', help='Получить информацию о текущем вебхуке')
    parser.add_argument('--delete', action='store_true', help='Удалить текущий вебхук')
    
    args = parser.parse_args()
    
    # Получаем токен бота из аргументов или переменной окружения
    token = args.token or os.environ.get('TELEGRAM_BOT_TOKEN')
    
    if not token:
        logger.error("Не указан токен Telegram бота. Используйте --token или установите переменную окружения TELEGRAM_BOT_TOKEN")
        return 1
    
    # Получение информации о вебхуке
    if args.info:
        get_webhook_info(token)
        return 0
    
    # Удаление вебхука
    if args.delete:
        delete_webhook(token)
        return 0
    
    # Для настройки вебхука нужно указать хост
    if not args.host:
        logger.error("Не указан хост для вебхука. Используйте --host")
        return 1
    
    # Формируем URL вебхука
    webhook_url = f"https://{args.host}:{args.port}/webhook/{token}"
    
    # Создаем сертификат
    create_self_signed_cert(args.cert, args.key, args.host)
    
    # Удаляем текущий вебхук
    delete_webhook(token)
    
    # Устанавливаем новый вебхук
    set_webhook(token, webhook_url, args.cert)
    
    # Обновляем файл .env
    update_env_file(args.host, args.port, args.cert, args.key, token)
    
    # Получаем информацию о новом вебхуке
    get_webhook_info(token)
    
    return 0

if __name__ == '__main__':
    sys.exit(main()) 