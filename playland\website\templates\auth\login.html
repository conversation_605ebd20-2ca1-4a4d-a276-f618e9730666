{% extends "base.html" %}

{% block title %}Вход в систему - PlayLand{% endblock %}

{% block namespace %}auth{% endblock %}

{% block extra_head %}
<style>
    :root {
        --pixel-font: 'Press Start 2P', cursive;
        --main-font: 'Roboto', sans-serif;
        --glow-green: #00ff00;
        --bright-green: #32cd32;
        --dark-bg: #121212;
    }
</style>
{% endblock %}

{% block content %}
<div class="auth-page">
    <div class="particles-container">
        <!-- Частицы будут добавлены через JavaScript -->
    </div>

    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <h1>Вход в систему</h1>
                <p>Войдите в свой аккаунт PlayLand</p>
            </div>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else 'success' if category == 'success' else 'info' }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <form method="POST" class="auth-form">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>

                <div class="form-group">
                    <label for="username">Имя пользователя или Email</label>
                    <input type="text" id="username" name="username" required>
                </div>

                <div class="form-group">
                    <label for="password">Пароль</label>
                    <input type="password" id="password" name="password" required>
                </div>

                <div class="form-group checkbox-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="remember" value="1">
                        <span class="checkmark"></span>
                        Запомнить меня
                    </label>
                </div>

                <button type="submit" class="btn-minecraft btn-full">Войти</button>
            </form>

            <div class="auth-divider">
                <span>или</span>
            </div>

            <div class="social-auth">
                <button class="btn-outline social-btn" onclick="alert('Discord авторизация будет доступна в полной версии')">
                    <i class="fab fa-discord"></i>
                    Войти через Discord
                </button>
                <button class="btn-outline social-btn" onclick="alert('Telegram авторизация будет доступна в полной версии')">
                    <i class="fab fa-telegram"></i>
                    Войти через Telegram
                </button>
            </div>

            <div class="auth-footer">
                <p>Нет аккаунта? <a href="{{ url_for('register') }}">Зарегистрироваться</a></p>
                <p><a href="#" class="forgot-password" onclick="alert('Восстановление пароля будет доступно в полной версии')">Забыли пароль?</a></p>
            </div>
        </div>
    </div>
</div>

<style>
/* Основная страница авторизации */
.auth-page {
    min-height: 100vh;
    background-image: url("{{ url_for('static', filename='images/grass_background.jpg') }}");
    background-size: cover;
    background-position: center center;
    background-attachment: fixed;
    position: relative;
    overflow: hidden;
}

.auth-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(2px);
    z-index: 1;
}

.auth-container {
    position: relative;
    z-index: 2;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    padding-top: 100px;
}

.auth-card {
    background: rgba(26, 26, 26, 0.95);
    border: 2px solid var(--glow-green);
    border-radius: 15px;
    padding: 40px;
    width: 100%;
    max-width: 450px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5), 0 0 30px rgba(0, 255, 0, 0.2);
    position: relative;
    overflow: hidden;
    animation: cardSlideIn 0.8s cubic-bezier(0.68, -0.55, 0.27, 1.55) forwards;
    opacity: 0;
    transform: translateY(50px);
}

@keyframes cardSlideIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-card::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 2px solid transparent;
    border-radius: 15px;
    background: linear-gradient(45deg, var(--glow-green), transparent, var(--glow-green));
    background-size: 400% 400%;
    opacity: 0.3;
    z-index: -1;
    animation: borderGlow 3s ease infinite;
}

@keyframes borderGlow {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.auth-header {
    text-align: center;
    margin-bottom: 30px;
}

.auth-header h1 {
    font-family: var(--pixel-font);
    font-size: 1.8rem;
    color: var(--glow-green);
    margin-bottom: 15px;
    text-shadow: 0 0 15px rgba(0, 255, 0, 0.5);
    animation: titleGlow 2s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    from { text-shadow: 0 0 10px rgba(0, 255, 0, 0.5); }
    to { text-shadow: 0 0 20px var(--glow-green), 0 0 30px var(--glow-green); }
}

.auth-header p {
    color: #f0f0f0;
    font-size: 1rem;
    font-family: var(--main-font);
}

.alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 8px;
    font-size: 0.9rem;
    border: 1px solid;
    animation: alertSlideIn 0.5s ease-out;
}

@keyframes alertSlideIn {
    from { opacity: 0; transform: translateX(-20px); }
    to { opacity: 1; transform: translateX(0); }
}

.alert-danger {
    background: rgba(220, 53, 69, 0.1);
    border-color: #dc3545;
    color: #dc3545;
}

.alert-success {
    background: rgba(40, 167, 69, 0.1);
    border-color: #28a745;
    color: #28a745;
}

.alert-info {
    background: rgba(23, 162, 184, 0.1);
    border-color: #17a2b8;
    color: #17a2b8;
}

.auth-form {
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    color: #fff;
    font-weight: 600;
    margin-bottom: 10px;
    font-size: 0.95rem;
    font-family: var(--main-font);
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="password"] {
    width: 100%;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(0, 255, 0, 0.3);
    border-radius: 8px;
    color: #fff;
    font-size: 1rem;
    font-family: var(--main-font);
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.form-group input:focus {
    outline: none;
    border-color: var(--glow-green);
    box-shadow: 0 0 15px rgba(0, 255, 0, 0.3);
    background: rgba(255, 255, 255, 0.08);
}

.checkbox-group {
    display: flex;
    align-items: center;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #ccc;
    font-size: 0.9rem;
    font-family: var(--main-font);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 24px;
    height: 24px;
    border: 2px solid rgba(0, 255, 0, 0.5);
    border-radius: 4px;
    margin-right: 12px;
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.checkbox-label input:checked + .checkmark {
    background: var(--glow-green);
    border-color: var(--glow-green);
    box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
}

.checkbox-label input:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #000;
    font-weight: bold;
    font-size: 14px;
}

/* Кнопки в стиле PlayLand */
.btn-minecraft {
    display: inline-block;
    padding: 18px 35px;
    background-color: var(--glow-green);
    color: #000;
    text-decoration: none;
    font-family: var(--pixel-font);
    font-size: 0.9em;
    text-transform: uppercase;
    border: none;
    position: relative;
    overflow: hidden;
    transition: all 0.3s;
    box-shadow: 0 4px 0 #006400, 0 0 20px rgba(0, 255, 0, 0.4);
    border-radius: 5px;
    cursor: pointer;
    letter-spacing: 1px;
}

.btn-minecraft::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    transition: transform 0.5s ease;
    transform: skewX(-15deg);
}

.btn-minecraft:hover {
    transform: translateY(-3px);
    box-shadow: 0 7px 0 #006400, 0 0 30px rgba(0, 255, 0, 0.6);
}

.btn-minecraft:hover::before {
    transform: skewX(-15deg) translateX(200%);
}

.btn-minecraft:active {
    transform: translateY(0);
    box-shadow: 0 2px 0 #006400;
}

.btn-full {
    width: 100%;
}

.btn-outline {
    background-color: transparent;
    border: 2px solid var(--glow-green);
    color: var(--glow-green);
    box-shadow: 0 4px 0 rgba(0, 255, 0, 0.3), 0 0 20px rgba(0, 255, 0, 0.2);
    padding: 15px 25px;
    font-family: var(--pixel-font);
    font-size: 0.8em;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.btn-outline:hover {
    box-shadow: 0 7px 0 rgba(0, 255, 0, 0.3), 0 0 30px rgba(0, 255, 0, 0.4);
    background-color: rgba(0, 255, 0, 0.1);
    transform: translateY(-3px);
}

.btn-outline:active {
    box-shadow: 0 2px 0 rgba(0, 255, 0, 0.3);
    transform: translateY(0);
}

.social-btn {
    width: 100%;
    margin-bottom: 15px;
}

.auth-divider {
    text-align: center;
    margin: 30px 0;
    position: relative;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--glow-green), transparent);
}

.auth-divider span {
    background: rgba(26, 26, 26, 0.95);
    color: #ccc;
    padding: 0 20px;
    font-size: 0.9rem;
    font-family: var(--pixel-font);
    text-transform: uppercase;
    letter-spacing: 2px;
}

.social-auth {
    margin-bottom: 30px;
}

.auth-footer {
    text-align: center;
}

.auth-footer p {
    color: #ccc;
    font-size: 0.9rem;
    margin: 15px 0;
    font-family: var(--main-font);
}

.auth-footer a {
    color: var(--glow-green);
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 600;
}

.auth-footer a:hover {
    color: var(--bright-green);
    text-decoration: underline;
    text-shadow: 0 0 5px rgba(0, 255, 0, 0.5);
}

.forgot-password {
    font-size: 0.8rem;
    opacity: 0.8;
}

/* Частицы */
.particles-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 0;
}

.particle {
    position: absolute;
    opacity: 0;
    animation: floatParticle linear infinite;
}

@keyframes floatParticle {
    0% {
        transform: translateY(100vh) translateX(0);
        opacity: 0;
    }
    10% {
        opacity: 0.7;
    }
    90% {
        opacity: 0.7;
    }
    100% {
        transform: translateY(-20vh) translateX(calc(var(--rand-x, 0) * 40vw - 20vw));
        opacity: 0;
    }
}

/* Адаптивность */
@media (max-width: 768px) {
    .auth-container {
        padding: 15px;
        padding-top: 80px;
    }

    .auth-card {
        padding: 30px 25px;
        max-width: 100%;
    }

    .auth-header h1 {
        font-size: 1.4rem;
    }

    .btn-minecraft, .btn-outline {
        padding: 15px 25px;
        font-size: 0.8em;
    }

    .form-group input {
        padding: 12px;
    }
}

@media (max-width: 480px) {
    .auth-header h1 {
        font-size: 1.2rem;
    }

    .auth-card {
        padding: 25px 20px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Создание частиц
    function createParticles() {
        const container = document.querySelector('.particles-container');
        if (!container) return;

        const particleCount = window.innerWidth < 768 ? 15 : 30;

        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.innerHTML = '⬛';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
            particle.style.animationDelay = Math.random() * 10 + 's';
            particle.style.fontSize = (Math.random() * 10 + 10) + 'px';
            particle.style.color = `rgba(0, 255, 0, ${Math.random() * 0.5 + 0.2})`;
            particle.style.setProperty('--rand-x', Math.random());

            container.appendChild(particle);
        }
    }

    createParticles();
});
</script>
{% endblock %}
