{% extends "base.html" %}

{% block title %}Профиль {{ user.nickname }} - PlayLand{% endblock %}

{% block head_extra %}
<style>
    :root {
        --minecraft-grass: #5B9C3B;
        --minecraft-dirt: #8C5E39;
        --minecraft-stone: #919191;
        --minecraft-wood: #73553C;
        --minecraft-planks: #B88E5F;
    }

    .profile-page {
        max-width: 900px;
        margin: 30px auto;
        padding: 0;
        animation: fadeIn 0.8s ease-out forwards;
        position: relative;
    }

    .profile-header {
        background-color: rgba(0, 0, 0, 0.7);
        padding: 25px;
        position: relative;
        border: 4px solid var(--minecraft-stone);
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        overflow: hidden;
    }

    .profile-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: linear-gradient(90deg, transparent, var(--glow-green), transparent);
        animation: headerGlow 3s infinite;
    }

    @keyframes headerGlow {
        0%, 100% { opacity: 0.5; }
        50% { opacity: 1; }
    }

    .profile-header h1 {
        font-family: var(--pixel-font);
        font-size: 2em;
        color: var(--glow-green);
        text-shadow: 2px 2px 0 #000, 0 0 10px var(--glow-green);
        margin-bottom: 15px;
        text-align: center;
        text-transform: uppercase;
        letter-spacing: 2px;
    }

    .profile-card {
        display: flex;
        flex-wrap: wrap;
        background-color: rgba(0, 0, 0, 0.6);
        border: 4px solid var(--minecraft-dirt);
        padding: 20px;
        margin-bottom: 30px;
        box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.7);
        position: relative;
        overflow: hidden;
        transform-style: preserve-3d;
        transition: all 0.3s ease;
    }

    .profile-card:hover {
        transform: translateY(-5px);
        box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.7), 0 10px 30px rgba(0, 0, 0, 0.5);
    }

    .profile-card::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: repeating-linear-gradient(
            45deg,
            rgba(255,255,255,0.03),
            rgba(255,255,255,0.03) 10px,
            rgba(0,0,0,0.05) 10px,
            rgba(0,0,0,0.05) 20px
        );
        pointer-events: none;
    }

    .profile-avatar-section {
        flex: 0 0 200px;
        text-align: center;
        margin-right: 30px;
        margin-bottom: 20px;
        position: relative;
    }

    .profile-avatar {
        width: 180px;
        height: 180px;
        object-fit: cover;
        border: 3px solid var(--glow-green);
        box-shadow: 0 0 15px rgba(0, 255, 0, 0.3);
        transition: all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
        image-rendering: pixelated;
        transform-style: preserve-3d;
        position: relative;
    }

    .profile-avatar::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: var(--glow-green);
        opacity: 0;
        z-index: -1;
        transition: opacity 0.3s ease;
    }

    .profile-avatar:hover {
        transform: scale(1.1) rotate(2deg);
        box-shadow: 0 0 30px rgba(0, 255, 0, 0.5);
    }

    .profile-avatar:hover::before {
        opacity: 0.1;
        animation: avatarPulse 1.5s infinite alternate;
    }

    @keyframes avatarPulse {
        0% { opacity: 0.1; }
        100% { opacity: 0.3; }
    }

    .profile-info-section {
        flex: 1;
        min-width: 300px;
        padding: 10px;
        background-color: rgba(0, 0, 0, 0.3);
        border-left: 3px solid var(--minecraft-wood);
    }

    .profile-info-title {
        font-family: var(--pixel-font);
        color: var(--minecraft-grass);
        font-size: 1.2em;
        margin-bottom: 15px;
        text-shadow: 1px 1px 0 #000;
        padding-bottom: 5px;
        border-bottom: 2px dashed rgba(91, 156, 59, 0.3);
    }

    .profile-info-section p {
        margin: 12px 0;
        font-size: 1.1em;
        border-bottom: 1px dashed rgba(255, 255, 255, 0.1);
        padding-bottom: 12px;
        display: flex;
        align-items: center;
        transition: all 0.2s ease;
    }

    .profile-info-section p:hover {
        background-color: rgba(91, 156, 59, 0.05);
        transform: translateX(5px);
    }

    .profile-info-section p strong {
        color: var(--glow-green);
        font-family: var(--pixel-font);
        font-size: 0.8em;
        display: inline-block;
        width: 150px;
        text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.8);
    }

    .profile-info-section p i {
        margin-right: 10px;
        color: var(--minecraft-grass);
    }

    .status-approved {
        color: #4CAF50;
        text-shadow: 0 0 5px rgba(76, 175, 80, 0.5);
    }

    .status-pending {
        color: #FFC107;
        text-shadow: 0 0 5px rgba(255, 193, 7, 0.5);
    }

    .status-rejected {
        color: #F44336;
        text-shadow: 0 0 5px rgba(244, 67, 54, 0.5);
    }

    .admin-badge {
        display: inline-block;
        background-color: rgba(255, 193, 7, 0.2);
        border: 2px solid #FFC107;
        color: #FFC107;
        padding: 3px 8px;
        font-size: 0.75em;
        border-radius: 3px;
        margin-left: 10px;
        text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.8);
        animation: badgePulse 2s infinite alternate;
    }
    
    .badge-minecraft {
        display: inline-block;
        background-color: rgba(91, 156, 59, 0.3);
        border: 1px solid var(--minecraft-grass);
        color: white;
        padding: 4px 8px;
        font-size: 0.8em;
        margin-right: 5px;
        margin-bottom: 5px;
        border-radius: 0;
        box-shadow: inset -2px -2px 0 rgba(0, 0, 0, 0.2), inset 2px 2px 0 rgba(255, 255, 255, 0.1);
    }

    @keyframes badgePulse {
        0% { box-shadow: 0 0 5px rgba(255, 193, 7, 0.3); }
        100% { box-shadow: 0 0 15px rgba(255, 193, 7, 0.7); }
    }

    .balance-block {
        margin-top: 25px;
        padding: 15px;
        background-color: rgba(0, 0, 0, 0.3);
        border-left: 3px solid var(--glow-green);
    }

    .balance-block h3 {
        font-family: var(--pixel-font);
        font-size: 1.3em;
        margin-bottom: 15px;
        color: white;
    }

    .text-success {
        color: var(--glow-green);
    }

    .minecraft-btn {
        display: inline-block;
        padding: 12px 20px;
        background-color: var(--minecraft-grass);
        color: white;
        border: none;
        text-decoration: none;
        font-family: var(--pixel-font);
        font-size: 0.9rem;
        position: relative;
        text-align: center;
        text-shadow: 2px 2px 0px rgba(0, 0, 0, 0.8);
        box-shadow: inset -4px -4px 0px 0px rgba(0, 0, 0, 0.3), 
                    inset 4px 4px 0px 0px rgba(255, 255, 255, 0.3);
        cursor: pointer;
        transition: all 0.1s;
        transform-style: preserve-3d;
        image-rendering: pixelated;
        margin-top: 10px;
    }

    .minecraft-btn::before {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        top: 6px;
        left: 0;
        background-color: rgba(0, 0, 0, 0.3);
        z-index: -1;
        transform: translateZ(-1px);
        transition: all 0.2s;
    }

    .minecraft-btn:hover {
        transform: translate(0, -3px);
        color: white;
    }

    .minecraft-btn:hover::before {
        top: 9px;
    }

    .minecraft-btn:active {
        transform: translate(0, 3px);
    }

    .minecraft-btn:active::before {
        top: 3px;
    }

    .minecraft-btn i {
        margin-right: 8px;
    }

    .minecraft-btn-wood {
        background-color: var(--minecraft-wood);
        margin-top: 15px;
    }

    .minecraft-block {
        width: 50px;
        height: 50px;
        display: inline-block;
        margin: 0 10px;
        image-rendering: pixelated;
        cursor: pointer;
        position: relative;
        transition: all 0.2s ease;
    }

    .minecraft-block:hover {
        transform: translateY(-5px) rotate(5deg);
    }

    .grass-block {
        background-image: url('/static/images/grass_block.png');
        background-size: cover;
    }

    .wood-block {
        background-image: url('/static/images/wood_block.png');
        background-size: cover;
    }

    @keyframes blockDestroy {
        0% { opacity: 1; transform: scale(1); }
        80% { opacity: 0.5; transform: scale(0.8); }
        100% { opacity: 0; transform: scale(0); }
    }

    .fade-in-section {
        opacity: 0;
        transform: translateY(20px);
        transition: opacity 0.6s ease-out, transform 0.6s ease-out;
    }

    .fade-in-section.is-visible {
        opacity: 1;
        transform: translateY(0);
    }

    .fade-in-right {
        opacity: 0;
        transform: translateX(-20px);
        transition: opacity 0.4s ease-out, transform 0.4s ease-out;
    }

    .fade-in-right.is-visible {
        opacity: 1;
        transform: translateX(0);
    }
    
    /* Профильные секции */
    .profile-section {
        background-color: rgba(0, 0, 0, 0.7);
        padding: 25px;
        position: relative;
        border: 4px solid var(--minecraft-stone);
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    }
    
    .profile-section h2 {
        font-family: var(--pixel-font);
        font-size: 1.5em;
        color: var(--glow-green);
        margin-bottom: 20px;
        text-shadow: 2px 2px 0 #000;
        text-align: center;
        border-bottom: 2px dashed rgba(91, 156, 59, 0.3);
        padding-bottom: 10px;
    }
    
    /* Социальные сети */
    .social-links-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        justify-content: center;
    }
    
    .social-link-item {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 12px 20px;
        background-color: rgba(0, 0, 0, 0.5);
        color: white;
        text-decoration: none;
        transition: all 0.2s ease;
        min-width: 160px;
        border: 2px solid transparent;
    }
    
    .social-link-item i {
        margin-right: 10px;
        font-size: 1.2em;
    }
    
    .social-link-item.youtube {
        border-color: #FF0000;
        background-color: rgba(255, 0, 0, 0.1);
    }
    
    .social-link-item.youtube:hover {
        background-color: rgba(255, 0, 0, 0.2);
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(255, 0, 0, 0.3);
    }
    
    .social-link-item.twitch {
        border-color: #9146FF;
        background-color: rgba(145, 70, 255, 0.1);
    }
    
    .social-link-item.twitch:hover {
        background-color: rgba(145, 70, 255, 0.2);
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(145, 70, 255, 0.3);
    }
    
    .social-link-item.vk {
        border-color: #4C75A3;
        background-color: rgba(76, 117, 163, 0.1);
    }
    
    .social-link-item.vk:hover {
        background-color: rgba(76, 117, 163, 0.2);
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(76, 117, 163, 0.3);
    }
    
    .social-link-item.steam {
        border-color: #1b2838;
        background-color: rgba(27, 40, 56, 0.1);
    }
    
    .social-link-item.steam:hover {
        background-color: rgba(27, 40, 56, 0.2);
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(27, 40, 56, 0.3);
    }
    
    /* Достижения */
    .achievements-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 20px;
    }
    
    .achievement-item {
        display: flex;
        align-items: center;
        background-color: rgba(0, 0, 0, 0.4);
        padding: 15px;
        border: 2px solid var(--minecraft-stone);
        transition: all 0.2s ease;
    }
    
    .achievement-item.unlocked {
        border-color: var(--minecraft-grass);
    }
    
    .achievement-item.locked {
        opacity: 0.7;
        filter: grayscale(0.8);
    }
    
    .achievement-icon {
        width: 48px;
        height: 48px;
        background-color: rgba(0, 0, 0, 0.6);
        border: 2px solid var(--minecraft-dirt);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        flex-shrink: 0;
        font-size: 1.5em;
        color: var(--glow-green);
    }
    
    .achievement-item.unlocked .achievement-icon {
        color: gold;
        text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
    }
    
    .achievement-info {
        flex: 1;
    }
    
    .achievement-info h3 {
        font-family: var(--pixel-font);
        font-size: 1.1em;
        margin: 0 0 5px;
    }
    
    .achievement-info p {
        font-size: 0.85em;
        color: #aaa;
        margin: 0 0 5px;
    }
    
    .achievement-date {
        font-size: 0.75em;
        color: var(--minecraft-grass);
    }
    
    /* Статистика */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 20px;
    }
    
    .stat-card {
        background-color: rgba(0, 0, 0, 0.4);
        padding: 20px;
        text-align: center;
        border: 2px solid var(--minecraft-dirt);
        transition: all 0.2s ease;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        border-color: var(--glow-green);
    }
    
    .stat-card i {
        font-size: 2em;
        color: var(--glow-green);
        margin-bottom: 10px;
    }
    
    .stat-card h3 {
        font-family: var(--pixel-font);
        font-size: 1em;
        margin: 0 0 10px;
    }
    
    .stat-value {
        font-size: 1.5em;
        font-family: var(--pixel-font);
        color: white;
        margin: 10px 0;
    }
    
    .stat-label {
        font-size: 0.8em;
        color: #aaa;
        margin: 0;
    }
    
    .stats-note {
        text-align: center;
        font-size: 0.8em;
        color: #777;
        margin-top: 20px;
        font-style: italic;
    }
    
    /* Транзакции */
    .transactions-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .transactions-list li {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.2s ease;
    }
    
    .transactions-list li:hover {
        background-color: rgba(0, 0, 0, 0.3);
    }
    
    .transaction-date {
        flex: 0 0 150px;
        font-size: 0.9em;
        color: #aaa;
    }
    
    .transaction-date i {
        margin-right: 5px;
    }
    
    .transaction-desc {
        flex: 1;
        padding: 0 15px;
    }
    
    .transaction-amount {
        font-family: var(--pixel-font);
        font-size: 1.1em;
        text-align: right;
        flex: 0 0 120px;
    }
    
    .transaction-amount.positive {
        color: #4CAF50;
    }
    
    .transaction-amount.negative {
        color: #F44336;
    }
    
    .empty-state {
        text-align: center;
        padding: 30px;
        color: #777;
    }
    
    .empty-state i {
        font-size: 3em;
        margin-bottom: 15px;
        opacity: 0.5;
    }
    
    /* Адаптивность */
    @media (max-width: 768px) {
        .profile-page {
            padding: 0 15px;
            margin: 20px 15px;
        }
        
        .profile-avatar-section {
            flex: 0 0 100%;
            margin-right: 0;
        }
        
        .profile-info-section {
            border-left: none;
            border-top: 3px solid var(--minecraft-wood);
            margin-top: 20px;
        }
        
        .stats-grid,
        .achievements-grid {
            grid-template-columns: 1fr;
        }
        
        .transactions-list li {
            flex-direction: column;
            align-items: flex-start;
        }
        
        .transaction-date,
        .transaction-desc,
        .transaction-amount {
            flex: 0 0 100%;
            padding: 5px 0;
        }
        
        .transaction-amount {
            text-align: left;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="profile-page">
    <div class="profile-header">
        <h1>Профиль игрока</h1>
        <div class="minecraft-blocks">
            <div class="minecraft-block grass-block"></div>
            <div class="minecraft-block dirt-block"></div>
            <div class="minecraft-block stone-block"></div>
            <div class="minecraft-block wood-block"></div>
        </div>
    </div>
    
    <div class="profile-card fade-in-section">
        <div class="profile-avatar-section">
            <img src="{{ user.avatar_url or url_for('static', filename='images/default_avatar.png') }}" 
                 alt="Аватар {{ user.nickname or user.username }}" class="profile-avatar">
                 
            <div class="social-connections">
                <a href="{{ url_for('connect_discord') }}" class="minecraft-btn minecraft-btn-sm social-connect-btn discord-btn">
                    <i class="fab fa-discord"></i> Discord
                    {% if user.discord_id %}
                        <span class="connection-status connected" title="Подключено"><i class="fas fa-check"></i></span>
                    {% else %}
                        <span class="connection-status not-connected" title="Не подключено"><i class="fas fa-times"></i></span>
                    {% endif %}
                </a>
                
                <a href="{{ url_for('connect_telegram') }}" class="minecraft-btn minecraft-btn-sm social-connect-btn telegram-btn">
                    <i class="fab fa-telegram-plane"></i> Telegram
                    {% if user.telegram_id and user.telegram_id[:7] != 'pending_' %}
                        <span class="connection-status connected" title="Подключено"><i class="fas fa-check"></i></span>
                    {% else %}
                        <span class="connection-status not-connected" title="Не подключено"><i class="fas fa-times"></i></span>
                    {% endif %}
                </a>
            </div>
        </div>
        
        <div class="profile-info-section">
            <h3 class="profile-info-title">Информация об игроке</h3>
            <p><i class="fas fa-user"></i> <strong>Никнейм:</strong> {{ user.nickname or user.username }} 
                {% if user.is_admin %}<span class="admin-badge">Администратор</span>{% endif %}
            </p>
            <p><i class="fas fa-envelope"></i> <strong>Email:</strong> {{ user.email }}</p>
            <p><i class="fab fa-discord"></i> <strong>Discord:</strong> {{ user.discord or 'Не указан' }}</p>
            <p><i class="fas fa-calendar-alt"></i> <strong>Дата регистрации:</strong> {{ user.created_at.strftime('%d.%m.%Y %H:%M') if user.created_at else 'Неизвестно' }}</p>
            
            {% if user.bio %}
            <p><i class="fas fa-quote-left"></i> <strong>О себе:</strong> {{ user.bio }}</p>
            {% endif %}
            
            {% if user.minecraft_experience %}
            <p><i class="fas fa-gamepad"></i> <strong>Опыт в Minecraft:</strong> {{ user.minecraft_experience }}</p>
            {% endif %}
            
            {% if user.interests %}
            <p><i class="fas fa-heart"></i> <strong>Интересы:</strong>
                {% for interest in user.get_interests() %}
                <span class="badge badge-minecraft">{{ interest }}</span>
                {% endfor %}
            </p>
            {% endif %}
            
            {% if user.status %}
            <p><i class="fas fa-clipboard-check"></i> <strong>Статус заявки:</strong> 
                <span class="status-{{ user.status|lower }}">
                    {% if user.status == 'pending' %}⏳ Ожидает рассмотрения
                    {% elif user.status == 'approved' %}✅ Одобрена
                    {% elif user.status == 'rejected' %}❌ Отклонена
                    {% else %}{{ user.status }}{% endif %}
                </span>
            </p>
            {% endif %}
            
            <div class="balance-block">
                <h3>Баланс: <span class="text-success">{{ "{:,}".format(user.balance or 0).replace(",", " ") }} ₽</span></h3>
                <a href="{{ url_for('add_balance') }}" class="minecraft-btn">
                    <i class="fas fa-coins"></i> Пополнить баланс
                </a>
            </div>
            
            <a href="{{ url_for('profile.edit_profile') }}" class="minecraft-btn minecraft-btn-wood">
                <i class="fas fa-user-edit"></i> Редактировать профиль
            </a>
        </div>
    </div>

    {% if user.get_social_links() %}
    <div class="profile-section social-links fade-in-section">
        <h2>Социальные сети</h2>
        <div class="social-links-grid">
            {% set social_links = user.get_social_links() %}
            {% for platform, url in social_links.items() %}
            <a href="{{ url }}" target="_blank" class="social-link-item {{ platform }}">
                {% if platform == 'youtube' %}
                    <i class="fab fa-youtube"></i> YouTube
                {% elif platform == 'twitch' %}
                    <i class="fab fa-twitch"></i> Twitch
                {% elif platform == 'vk' %}
                    <i class="fab fa-vk"></i> ВКонтакте
                {% elif platform == 'steam' %}
                    <i class="fab fa-steam"></i> Steam
                {% else %}
                    <i class="fas fa-link"></i> {{ platform|capitalize }}
                {% endif %}
            </a>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    <!-- Секция для достижений пользователя -->
    {% if user.achievements %}
    {% set achievements = user.get_achievements() %}
    <div class="profile-section achievements-section fade-in-section">
        <h2>Достижения</h2>
        <div class="achievements-grid">
            {% for achievement_id, data in achievements.items() %}
            <div class="achievement-item {{ 'unlocked' if data.unlocked else 'locked' }}">
                <div class="achievement-icon">
                    <i class="{{ data.icon|default('fas fa-trophy') }}"></i>
                </div>
                <div class="achievement-info">
                    <h3>{{ data.title }}</h3>
                    <p>{{ data.description }}</p>
                    {% if data.unlocked %}
                    <span class="achievement-date">Получено: {{ data.unlocked_at }}</span>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    <!-- Секция для статистики игрока -->
    <div class="profile-section stats-section fade-in-section">
        <h2>Игровая статистика</h2>
        <div class="stats-grid">
            <div class="stat-card">
                <i class="fas fa-clock"></i>
                <h3>Время в игре</h3>
                <p class="stat-value">{{ user.last_seen_on_server.strftime('%d.%m.%Y %H:%M') if user.last_seen_on_server else 'Нет данных' }}</p>
                <p class="stat-label">Последний вход</p>
            </div>
            
            <div class="stat-card">
                <i class="fas fa-walking"></i>
                <h3>Расстояние</h3>
                <p class="stat-value">0 км</p>
                <p class="stat-label">Пройдено пешком</p>
            </div>
            
            <div class="stat-card">
                <i class="fas fa-skull"></i>
                <h3>Сражения</h3>
                <p class="stat-value">0 / 0</p>
                <p class="stat-label">Убийства / Смерти</p>
            </div>
            
            <div class="stat-card">
                <i class="fas fa-gem"></i>
                <h3>Добыча</h3>
                <p class="stat-value">0</p>
                <p class="stat-label">Добыто блоков</p>
            </div>
        </div>
        
        <p class="stats-note">Статистика обновляется каждые 24 часа</p>
    </div>

    <div class="transactions-section fade-in-section">
        <h2>История операций</h2>
        {% if user.transactions %}
            <ul class="transactions-list">
            {% for tx in user.transactions %}
                <li class="fade-in-right" style="transition-delay: {{ loop.index0 * 0.1 }}s">
                    <div class="transaction-date">
                        <i class="far fa-clock"></i> {{ tx.timestamp.strftime('%d.%m.%Y %H:%M') }}
                    </div>
                    <div class="transaction-desc">{{ tx.description }}</div>
                    <div class="transaction-amount {{ 'positive' if tx.amount > 0 else 'negative' }}">
                        {{ tx.amount }} ₽ {% if tx.amount > 0 %}⬆️{% else %}⬇️{% endif %}
                    </div>
                </li>
            {% endfor %}
            </ul>
        {% else %}
            <div class="empty-state">
                <i class="fas fa-scroll"></i>
                <p>История операций пуста.</p>
            </div>
        {% endif %}
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Инициализация анимаций при прокрутке
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('is-visible');
                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.1
    });
    
    document.querySelectorAll('.fade-in-section, .fade-in-right').forEach(el => {
        observer.observe(el);
    });
    
    // Анимация для блоков Minecraft
    const blocks = document.querySelectorAll('.minecraft-block');
    blocks.forEach((block, index) => {
        block.addEventListener('click', function() {
            this.style.animation = 'blockDestroy 1.5s steps(10)';
            setTimeout(() => {
                this.style.animation = '';
            }, 1500);
        });
        
        // Добавляем задержку для анимации появления
        block.style.opacity = '0';
        block.style.transform = 'scale(0)';
        setTimeout(() => {
            block.style.transition = 'all 0.5s cubic-bezier(0.68, -0.55, 0.27, 1.55)';
            block.style.opacity = '1';
            block.style.transform = 'scale(1)';
        }, 100 + index * 150);
    });
});
</script>
{% endblock %} 