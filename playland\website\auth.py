#!/usr/bin/env python3
"""
Система авторизации для PlayLand
Поддерживает: обычную авторизацию, Telegram, Discord
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, session, jsonify
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.security import check_password_hash, generate_password_hash
from urllib.parse import urlencode
import requests
import secrets
import hashlib
from datetime import datetime, timezone, timedelta

from .models import db, User, UserRole
from .forms import LoginForm, RegisterForm, TelegramLinkForm
from .utils import is_safe_url, send_email, generate_csrf_token

auth_bp = Blueprint('auth', __name__, url_prefix='/auth')

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """Страница входа"""
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))
    
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter(
            (User.username == form.username.data) | 
            (User.email == form.username.data)
        ).first()
        
        if user and user.check_password(form.password.data):
            if not user.is_active:
                flash('Ваш аккаунт заблокирован. Обратитесь к администратору.', 'error')
                return render_template('auth/login.html', form=form)
            
            # Успешный вход
            login_user(user, remember=form.remember_me.data)
            user.last_login = datetime.now(timezone.utc)
            db.session.commit()
            
            # Перенаправление на запрошенную страницу
            next_page = request.args.get('next')
            if not next_page or not is_safe_url(next_page):
                next_page = url_for('main.index')
            
            flash(f'Добро пожаловать, {user.get_display_name()}!', 'success')
            return redirect(next_page)
        else:
            flash('Неверное имя пользователя или пароль', 'error')
    
    return render_template('auth/login.html', form=form)

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    """Страница регистрации"""
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))
    
    form = RegisterForm()
    if form.validate_on_submit():
        # Проверяем, не существует ли уже пользователь
        existing_user = User.query.filter(
            (User.username == form.username.data) | 
            (User.email == form.email.data)
        ).first()
        
        if existing_user:
            flash('Пользователь с таким именем или email уже существует', 'error')
            return render_template('auth/register.html', form=form)
        
        # Создаем нового пользователя
        user = User(
            username=form.username.data,
            email=form.email.data,
            minecraft_username=form.minecraft_username.data
        )
        user.set_password(form.password.data)
        
        db.session.add(user)
        db.session.commit()
        
        flash('Регистрация успешна! Теперь вы можете войти в систему.', 'success')
        return redirect(url_for('auth.login'))
    
    return render_template('auth/register.html', form=form)

@auth_bp.route('/logout')
@login_required
def logout():
    """Выход из системы"""
    logout_user()
    flash('Вы успешно вышли из системы', 'info')
    return redirect(url_for('main.index'))

@auth_bp.route('/profile')
@login_required
def profile():
    """Профиль пользователя"""
    return render_template('auth/profile.html', user=current_user)

@auth_bp.route('/telegram/link')
@login_required
def telegram_link():
    """Привязка Telegram аккаунта"""
    # Генерируем уникальный токен для привязки
    link_token = secrets.token_urlsafe(32)
    session['telegram_link_token'] = link_token
    session['telegram_link_expires'] = (datetime.now(timezone.utc) + timedelta(minutes=10)).isoformat()
    
    # URL для Telegram бота
    bot_username = "playlandserver_bot"  # Замените на ваш бот
    telegram_url = f"https://t.me/{bot_username}?start=link_{link_token}"
    
    return render_template('auth/telegram_link.html', 
                         telegram_url=telegram_url, 
                         link_token=link_token)

@auth_bp.route('/telegram/callback', methods=['POST'])
def telegram_callback():
    """Callback для привязки Telegram"""
    data = request.get_json()
    
    if not data or 'token' not in data or 'telegram_data' not in data:
        return jsonify({'success': False, 'error': 'Неверные данные'}), 400
    
    # Проверяем токен
    link_token = session.get('telegram_link_token')
    link_expires = session.get('telegram_link_expires')
    
    if not link_token or not link_expires:
        return jsonify({'success': False, 'error': 'Токен не найден'}), 400
    
    if datetime.now(timezone.utc) > datetime.fromisoformat(link_expires):
        return jsonify({'success': False, 'error': 'Токен истек'}), 400
    
    if data['token'] != link_token:
        return jsonify({'success': False, 'error': 'Неверный токен'}), 400
    
    # Привязываем Telegram
    telegram_data = data['telegram_data']
    current_user.telegram_id = telegram_data.get('id')
    current_user.telegram_username = telegram_data.get('username')
    current_user.telegram_first_name = telegram_data.get('first_name')
    current_user.telegram_last_name = telegram_data.get('last_name')
    
    db.session.commit()
    
    # Очищаем сессию
    session.pop('telegram_link_token', None)
    session.pop('telegram_link_expires', None)
    
    return jsonify({'success': True, 'message': 'Telegram успешно привязан!'})

@auth_bp.route('/discord/login')
def discord_login():
    """Авторизация через Discord"""
    from flask import current_app
    
    client_id = current_app.config.get('DISCORD_CLIENT_ID')
    redirect_uri = current_app.config.get('DISCORD_REDIRECT_URI')
    
    if not client_id or not redirect_uri:
        flash('Discord авторизация не настроена', 'error')
        return redirect(url_for('auth.login'))
    
    # Генерируем state для защиты от CSRF
    state = secrets.token_urlsafe(32)
    session['discord_state'] = state
    
    # Параметры для Discord OAuth
    params = {
        'client_id': client_id,
        'redirect_uri': redirect_uri,
        'response_type': 'code',
        'scope': 'identify email',
        'state': state
    }
    
    discord_url = f"https://discord.com/api/oauth2/authorize?{urlencode(params)}"
    return redirect(discord_url)

@auth_bp.route('/discord/callback')
def discord_callback():
    """Callback для Discord OAuth"""
    from flask import current_app
    
    # Проверяем state
    state = request.args.get('state')
    if not state or state != session.get('discord_state'):
        flash('Ошибка авторизации Discord: неверный state', 'error')
        return redirect(url_for('auth.login'))
    
    # Получаем код авторизации
    code = request.args.get('code')
    if not code:
        flash('Ошибка авторизации Discord: код не получен', 'error')
        return redirect(url_for('auth.login'))
    
    # Обмениваем код на токен
    client_id = current_app.config.get('DISCORD_CLIENT_ID')
    client_secret = current_app.config.get('DISCORD_CLIENT_SECRET')
    redirect_uri = current_app.config.get('DISCORD_REDIRECT_URI')
    
    token_data = {
        'client_id': client_id,
        'client_secret': client_secret,
        'grant_type': 'authorization_code',
        'code': code,
        'redirect_uri': redirect_uri
    }
    
    try:
        # Получаем токен
        token_response = requests.post(
            'https://discord.com/api/oauth2/token',
            data=token_data,
            headers={'Content-Type': 'application/x-www-form-urlencoded'}
        )
        token_response.raise_for_status()
        token_json = token_response.json()
        
        access_token = token_json.get('access_token')
        if not access_token:
            raise ValueError('Токен не получен')
        
        # Получаем информацию о пользователе
        user_response = requests.get(
            'https://discord.com/api/users/@me',
            headers={'Authorization': f'Bearer {access_token}'}
        )
        user_response.raise_for_status()
        discord_user = user_response.json()
        
        # Ищем существующего пользователя
        user = User.query.filter_by(discord_id=discord_user['id']).first()
        
        if user:
            # Пользователь существует, входим
            login_user(user)
            user.last_login = datetime.now(timezone.utc)
            db.session.commit()
            flash(f'Добро пожаловать, {user.get_display_name()}!', 'success')
        else:
            # Создаем нового пользователя
            username = discord_user.get('username', f"discord_{discord_user['id']}")
            email = discord_user.get('email')
            
            # Проверяем уникальность username
            counter = 1
            original_username = username
            while User.query.filter_by(username=username).first():
                username = f"{original_username}_{counter}"
                counter += 1
            
            user = User(
                username=username,
                email=email,
                discord_id=discord_user['id'],
                discord_username=discord_user.get('username'),
                is_verified=True  # Discord аккаунты считаем верифицированными
            )
            
            db.session.add(user)
            db.session.commit()
            
            login_user(user)
            flash('Аккаунт создан через Discord! Добро пожаловать!', 'success')
        
        # Очищаем сессию
        session.pop('discord_state', None)
        
        return redirect(url_for('main.index'))
        
    except Exception as e:
        flash(f'Ошибка авторизации Discord: {str(e)}', 'error')
        return redirect(url_for('auth.login'))

@auth_bp.route('/admin/required')
@login_required
def admin_required():
    """Проверка прав администратора"""
    if not current_user.is_admin:
        flash('Доступ запрещен. Требуются права администратора.', 'error')
        return redirect(url_for('main.index'))
    return jsonify({'success': True, 'message': 'Доступ разрешен'})

# Декоратор для проверки прав администратора
def admin_required(f):
    """Декоратор для проверки прав администратора"""
    from functools import wraps
    
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            return redirect(url_for('auth.login'))
        if not current_user.is_admin:
            flash('Доступ запрещен. Требуются права администратора.', 'error')
            return redirect(url_for('main.index'))
        return f(*args, **kwargs)
    return decorated_function
