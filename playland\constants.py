#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Константы проекта PlayLand.
"""

# Статусы заявок
APPLICATION_STATUS_PENDING = 'pending'
APPLICATION_STATUS_APPROVED = 'approved'
APPLICATION_STATUS_REJECTED = 'rejected'
APPLICATION_STATUS_REVIEWING = 'reviewing'

# Статусы тикетов
TICKET_STATUS_OPEN = 'open'
TICKET_STATUS_CLOSED = 'closed'
TICKET_STATUS_ANSWERED = 'answered'
TICKET_STATUS_PENDING = 'pending'

# Типы тикетов
TICKET_TYPE_SERVER = 'server'
TICKET_TYPE_PAYMENT = 'payment'
TICKET_TYPE_SUPPORT = 'support'
TICKET_TYPE_OTHER = 'other'

# Статусы пользователей
USER_STATUS_ACTIVE = 'active'
USER_STATUS_INACTIVE = 'inactive'
USER_STATUS_BANNED = 'banned'
USER_STATUS_PENDING = 'pending'

# Статусы вайтлиста
WHITELIST_STATUS_PENDING = 'pending'
WHITELIST_STATUS_APPROVED = 'approved'
WHITELIST_STATUS_REJECTED = 'rejected'

# Роли пользователей
USER_ROLE_ADMIN = 'admin'
USER_ROLE_MODERATOR = 'moderator'
USER_ROLE_USER = 'user'
USER_ROLE_VIP = 'vip'
USER_ROLE_BANNED = 'banned'

# Типы событий сервера
SERVER_EVENT_TYPE_APPLICATION = 'application'
SERVER_EVENT_TYPE_WHITELIST_ADD = 'whitelist_add'
SERVER_EVENT_TYPE_WHITELIST_REMOVE = 'whitelist_remove'
SERVER_EVENT_TYPE_SERVER_START = 'server_start'
SERVER_EVENT_TYPE_SERVER_STOP = 'server_stop'
SERVER_EVENT_TYPE_PLAYER_JOIN = 'player_join'
SERVER_EVENT_TYPE_PLAYER_LEAVE = 'player_leave'
SERVER_EVENT_TYPE_PLAYER_CHAT = 'player_chat'
SERVER_EVENT_TYPE_PLAYER_COMMAND = 'player_command'
SERVER_EVENT_TYPE_PLAYER_DEATH = 'player_death'
SERVER_EVENT_TYPE_PLAYER_ACHIEVEMENT = 'player_achievement'

# Типы транзакций
TRANSACTION_TYPE_DEPOSIT = 'deposit'
TRANSACTION_TYPE_WITHDRAWAL = 'withdrawal'
TRANSACTION_TYPE_PURCHASE = 'purchase'
TRANSACTION_TYPE_REFUND = 'refund'
TRANSACTION_TYPE_TRANSFER = 'transfer'

# Статусы транзакций
TRANSACTION_STATUS_PENDING = 'pending'
TRANSACTION_STATUS_COMPLETED = 'completed'
TRANSACTION_STATUS_FAILED = 'failed'
TRANSACTION_STATUS_CANCELLED = 'cancelled'

# Типы уведомлений
NOTIFICATION_TYPE_APPLICATION = 'application'
NOTIFICATION_TYPE_TICKET = 'ticket'
NOTIFICATION_TYPE_PAYMENT = 'payment'
NOTIFICATION_TYPE_SYSTEM = 'system'
NOTIFICATION_TYPE_WHITELIST = 'whitelist'

# Статусы уведомлений
NOTIFICATION_STATUS_UNREAD = 'unread'
NOTIFICATION_STATUS_READ = 'read'
NOTIFICATION_STATUS_ARCHIVED = 'archived'

# Типы команд для ботов
BOT_COMMAND_TYPE_ADMIN = 'admin'
BOT_COMMAND_TYPE_USER = 'user'
BOT_COMMAND_TYPE_SYSTEM = 'system'

# Типы интеграций
INTEGRATION_TYPE_DISCORD = 'discord'
INTEGRATION_TYPE_TELEGRAM = 'telegram'
INTEGRATION_TYPE_MINECRAFT = 'minecraft'

# Статусы интеграций
INTEGRATION_STATUS_ACTIVE = 'active'
INTEGRATION_STATUS_INACTIVE = 'inactive'
INTEGRATION_STATUS_ERROR = 'error'

# Коды ошибок
ERROR_CODE_INVALID_REQUEST = 'invalid_request'
ERROR_CODE_UNAUTHORIZED = 'unauthorized'
ERROR_CODE_FORBIDDEN = 'forbidden'
ERROR_CODE_NOT_FOUND = 'not_found'
ERROR_CODE_INTERNAL_ERROR = 'internal_error'
ERROR_CODE_RATE_LIMIT = 'rate_limit'
ERROR_CODE_INVALID_TOKEN = 'invalid_token'
ERROR_CODE_INVALID_CREDENTIALS = 'invalid_credentials'
ERROR_CODE_INVALID_PARAMETERS = 'invalid_parameters'
ERROR_CODE_DUPLICATE_ENTRY = 'duplicate_entry'
ERROR_CODE_DATABASE_ERROR = 'database_error'
ERROR_CODE_API_ERROR = 'api_error'
ERROR_CODE_INTEGRATION_ERROR = 'integration_error'
ERROR_CODE_TIMEOUT = 'timeout'
