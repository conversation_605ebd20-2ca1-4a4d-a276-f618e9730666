#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Скрипт миграции базы данных для добавления полей безопасности.
"""

import os
import sys
import sqlite3
import logging
from datetime import datetime

# Добавляем корневую директорию проекта в sys.path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../.."))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Настройка логирования
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SecurityMigration:
    """Класс для миграции полей безопасности"""

    def __init__(self, db_path="instance/playland.db"):
        self.db_path = db_path
        self.backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

    def create_backup(self):
        """Создание резервной копии базы данных"""
        try:
            import shutil
            shutil.copy2(self.db_path, self.backup_path)
            logger.info(f"Создана резервная копия: {self.backup_path}")
            return True
        except Exception as e:
            logger.error(f"Ошибка при создании резервной копии: {e}")
            return False

    def check_column_exists(self, cursor, table_name, column_name):
        """Проверяет, существует ли колонка в таблице"""
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = [column[1] for column in cursor.fetchall()]
        return column_name in columns

    def add_security_fields(self):
        """Добавление полей безопасности в таблицу users"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Список новых полей для добавления
            security_fields = [
                ("failed_login_attempts", "INTEGER DEFAULT 0"),
                ("last_failed_login", "DATETIME"),
                ("account_locked_until", "DATETIME"),
                ("password_reset_token", "VARCHAR(128)"),
                ("password_reset_expires", "DATETIME"),
                ("email_verification_token", "VARCHAR(128)"),
                ("email_verified", "BOOLEAN DEFAULT 0"),
                ("two_factor_enabled", "BOOLEAN DEFAULT 0"),
                ("two_factor_secret", "VARCHAR(32)"),
                ("backup_codes", "TEXT"),
                ("session_token", "VARCHAR(128)"),
                ("session_expires", "DATETIME")
            ]

            # Добавляем поля, которых еще нет
            for field_name, field_type in security_fields:
                if not self.check_column_exists(cursor, "users", field_name):
                    try:
                        cursor.execute(f"ALTER TABLE users ADD COLUMN {field_name} {field_type}")
                        logger.info(f"Добавлено поле: {field_name}")
                    except sqlite3.Error as e:
                        logger.error(f"Ошибка при добавлении поля {field_name}: {e}")

            conn.commit()
            logger.info("Миграция полей безопасности завершена успешно")
            return True

        except Exception as e:
            logger.error(f"Ошибка при миграции: {e}")
            return False
        finally:
            if conn:
                conn.close()

    def create_trusted_devices_table(self):
        """Создание таблицы доверенных устройств"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trusted_devices (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    device_fingerprint VARCHAR(128) NOT NULL,
                    device_name VARCHAR(100),
                    ip_address VARCHAR(50),
                    user_agent TEXT,
                    location VARCHAR(100),
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    last_used DATETIME DEFAULT CURRENT_TIMESTAMP,
                    expires_at DATETIME,
                    is_active BOOLEAN DEFAULT 1,
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
                )
            ''')

            # Создаем индексы
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_trusted_devices_user_id ON trusted_devices(user_id)",
                "CREATE INDEX IF NOT EXISTS idx_trusted_devices_fingerprint ON trusted_devices(device_fingerprint)",
                "CREATE INDEX IF NOT EXISTS idx_trusted_devices_active ON trusted_devices(is_active)"
            ]

            for index_sql in indexes:
                cursor.execute(index_sql)

            conn.commit()
            logger.info("Таблица trusted_devices создана")
            return True

        except Exception as e:
            logger.error(f"Ошибка при создании таблицы trusted_devices: {e}")
            return False
        finally:
            if conn:
                conn.close()

    def create_login_history_table(self):
        """Создание таблицы истории входов"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                CREATE TABLE IF NOT EXISTS login_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    ip_address VARCHAR(50),
                    user_agent TEXT,
                    location VARCHAR(100),
                    device_fingerprint VARCHAR(128),
                    login_method VARCHAR(50) DEFAULT 'password',
                    success BOOLEAN DEFAULT 1,
                    failure_reason VARCHAR(100),
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    session_duration INTEGER,
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
                )
            ''')

            # Создаем индексы
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_login_history_user_id ON login_history(user_id)",
                "CREATE INDEX IF NOT EXISTS idx_login_history_timestamp ON login_history(timestamp)",
                "CREATE INDEX IF NOT EXISTS idx_login_history_success ON login_history(success)",
                "CREATE INDEX IF NOT EXISTS idx_login_history_ip ON login_history(ip_address)"
            ]

            for index_sql in indexes:
                cursor.execute(index_sql)

            conn.commit()
            logger.info("Таблица login_history создана")
            return True

        except Exception as e:
            logger.error(f"Ошибка при создании таблицы login_history: {e}")
            return False
        finally:
            if conn:
                conn.close()

    def create_security_indexes(self):
        """Создание индексов для полей безопасности"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Создаем индексы для быстрого поиска
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_users_password_reset_token ON users(password_reset_token)",
                "CREATE INDEX IF NOT EXISTS idx_users_email_verification_token ON users(email_verification_token)",
                "CREATE INDEX IF NOT EXISTS idx_users_session_token ON users(session_token)",
                "CREATE INDEX IF NOT EXISTS idx_users_account_locked_until ON users(account_locked_until)",
                "CREATE INDEX IF NOT EXISTS idx_users_email_verified ON users(email_verified)"
            ]

            for index_sql in indexes:
                try:
                    cursor.execute(index_sql)
                    logger.info(f"Создан индекс: {index_sql.split('idx_')[1].split(' ')[0]}")
                except sqlite3.Error as e:
                    logger.error(f"Ошибка при создании индекса: {e}")

            conn.commit()
            logger.info("Создание индексов завершено")
            return True

        except Exception as e:
            logger.error(f"Ошибка при создании индексов: {e}")
            return False
        finally:
            if conn:
                conn.close()

    def update_existing_users(self):
        """Обновление существующих пользователей"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Устанавливаем email_verified = 1 для существующих пользователей
            cursor.execute("UPDATE users SET email_verified = 1 WHERE email_verified IS NULL")

            # Сбрасываем счетчики неудачных попыток входа
            cursor.execute("UPDATE users SET failed_login_attempts = 0 WHERE failed_login_attempts IS NULL")

            conn.commit()
            logger.info("Обновление существующих пользователей завершено")
            return True

        except Exception as e:
            logger.error(f"Ошибка при обновлении пользователей: {e}")
            return False
        finally:
            if conn:
                conn.close()

    def verify_migration(self):
        """Проверка успешности миграции"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Проверяем наличие всех новых полей
            cursor.execute("PRAGMA table_info(users)")
            columns = [column[1] for column in cursor.fetchall()]

            required_fields = [
                "failed_login_attempts", "last_failed_login", "account_locked_until",
                "password_reset_token", "password_reset_expires", "email_verification_token",
                "email_verified", "two_factor_enabled", "two_factor_secret",
                "backup_codes", "session_token", "session_expires"
            ]

            missing_fields = [field for field in required_fields if field not in columns]

            if missing_fields:
                logger.error(f"Отсутствуют поля: {missing_fields}")
                return False
            else:
                logger.info("Все поля безопасности успешно добавлены")
                return True

        except Exception as e:
            logger.error(f"Ошибка при проверке миграции: {e}")
            return False
        finally:
            if conn:
                conn.close()

    def run_migration(self):
        """Запуск полной миграции"""
        logger.info("Начало миграции полей безопасности...")

        # Проверяем существование файла БД
        if not os.path.exists(self.db_path):
            logger.error(f"Файл базы данных не найден: {self.db_path}")
            return False

        # Создаем резервную копию
        if not self.create_backup():
            logger.error("Не удалось создать резервную копию. Миграция отменена.")
            return False

        # Выполняем миграцию
        success = True
        success &= self.add_security_fields()
        success &= self.create_trusted_devices_table()
        success &= self.create_login_history_table()
        success &= self.create_security_indexes()
        success &= self.update_existing_users()
        success &= self.verify_migration()

        if success:
            logger.info("Миграция завершена успешно!")
        else:
            logger.error("Миграция завершена с ошибками!")

        return success

def main():
    """Главная функция"""
    import argparse

    parser = argparse.ArgumentParser(description='Миграция полей безопасности')
    parser.add_argument('--db-path', default='instance/playland.db',
                       help='Путь к файлу базы данных')

    args = parser.parse_args()

    migration = SecurityMigration(args.db_path)
    success = migration.run_migration()

    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
