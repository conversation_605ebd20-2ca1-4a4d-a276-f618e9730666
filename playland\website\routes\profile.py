from flask import Blueprint, render_template, redirect, url_for, request, flash, jsonify, current_app
from flask_login import login_required, current_user
from playland.website.models import db, User, Ticket, TicketMessage, TeamApplication
from playland.website.forms import (
    ProfileForm, ChangePasswordForm, TwoFactorSetupForm, DeleteAccountForm, SecuritySettingsForm
)
from playland.website.security import (
    SecurityValidator, rate_limit, log_security_event, require_verified_email
)
from datetime import datetime
from werkzeug.utils import secure_filename
import os
from werkzeug.security import check_password_hash, generate_password_hash

profile_bp = Blueprint('profile', __name__, url_prefix='/profile')

@profile_bp.route('/')
@login_required
def view_profile():
    # current_user будет доступен благодаря Flask-Login
    return render_template('profile.html', user=current_user)

@profile_bp.route('/edit', methods=['GET', 'POST'])
@login_required
def edit_profile():
    form = ProfileForm()

    # Предзаполнение формы текущими данными пользователя
    if request.method == 'GET':
        form.nickname.data = current_user.nickname
        form.email.data = current_user.email
        form.bio.data = current_user.bio
        form.minecraft_experience.data = current_user.minecraft_experience
        form.discord.data = current_user.discord

        # Загрузка интересов из JSON-строки
        if current_user.interests:
            interests = current_user.get_interests()
            form.interests.data = ', '.join(interests)

        # Загрузка социальных сетей из JSON-строки
        social_links = current_user.get_social_links()
        form.youtube.data = social_links.get('youtube', '')
        form.twitch.data = social_links.get('twitch', '')
        form.vk.data = social_links.get('vk', '')
        form.steam.data = social_links.get('steam', '')

    if form.validate_on_submit():
        # Проверка изменения критичных данных, требуется пароль
        is_critical_change = False

        # Проверка изменения email
        if form.email.data != current_user.email:
            is_critical_change = True

        # Проверка изменения пароля
        if form.new_password.data:
            is_critical_change = True

        # Если есть критичные изменения, проверяем пароль
        if is_critical_change and not form.current_password.data:
            flash('Для изменения email или пароля необходимо указать текущий пароль', 'danger')
            return render_template('edit_profile.html', form=form)

        if is_critical_change and not check_password_hash(current_user.password_hash, form.current_password.data):
            flash('Неверный текущий пароль', 'danger')
            return render_template('edit_profile.html', form=form)

        # Проверка уникальности никнейма
        if form.nickname.data != current_user.nickname:
            existing_user = User.query.filter_by(nickname=form.nickname.data).first()
            if existing_user and existing_user.id != current_user.id:
                flash('Этот никнейм уже занят', 'danger')
                return render_template('edit_profile.html', form=form)

        # Обновляем основные поля профиля
        current_user.nickname = form.nickname.data
        if is_critical_change and form.email.data != current_user.email:
            current_user.email = form.email.data
        current_user.bio = form.bio.data
        current_user.minecraft_experience = form.minecraft_experience.data
        current_user.discord = form.discord.data

        # Обрабатываем интересы
        if form.interests.data:
            interests = [interest.strip() for interest in form.interests.data.split(',') if interest.strip()]
            current_user.set_interests(interests)
        else:
            current_user.interests = None

        # Обрабатываем социальные сети
        social_links = {}
        if form.youtube.data:
            social_links['youtube'] = form.youtube.data
        if form.twitch.data:
            social_links['twitch'] = form.twitch.data
        if form.vk.data:
            social_links['vk'] = form.vk.data
        if form.steam.data:
            social_links['steam'] = form.steam.data

        current_user.set_social_links(social_links)

        # Обрабатываем смену пароля
        if is_critical_change and form.new_password.data:
            current_user.password_hash = generate_password_hash(form.new_password.data)

        # Обрабатываем загрузку аватара
        avatar_file = request.files.get('avatar')
        if avatar_file and avatar_file.filename:
            try:
                # Проверяем допустимые расширения
                allowed_extensions = {'png', 'jpg', 'jpeg', 'gif'}
                filename = secure_filename(avatar_file.filename)
                ext = filename.rsplit('.', 1)[1].lower()

                if ext in allowed_extensions:
                    # Создаем уникальное имя файла
                    unique_filename = f"avatar_{current_user.id}_{int(datetime.utcnow().timestamp())}.{ext}"

                    # Определяем путь для сохранения файла
                    upload_folder = os.path.join(current_app.static_folder, 'uploads/avatars')
                    os.makedirs(upload_folder, exist_ok=True)

                    # Сохраняем файл
                    avatar_path = os.path.join(upload_folder, unique_filename)
                    avatar_file.save(avatar_path)

                    # Обновляем URL аватара в профиле
                    current_user.avatar_url = f"/static/uploads/avatars/{unique_filename}"
                else:
                    flash('Недопустимый формат файла. Разрешены только PNG, JPG, JPEG и GIF', 'danger')
            except Exception as e:
                flash(f'Ошибка при загрузке аватара: {str(e)}', 'danger')

        try:
            db.session.commit()
            flash('Профиль успешно обновлен!', 'success')
            return redirect(url_for('profile.view_profile'))
        except Exception as e:
            db.session.rollback()
            flash(f'Ошибка при обновлении профиля: {str(e)}', 'danger')
    elif request.method == 'POST':
        # Если форма не прошла валидацию
        for field, errors in form.errors.items():
            for error in errors:
                flash(f'Ошибка в поле "{getattr(form, field).label.text}": {error}', 'danger')

    return render_template('edit_profile.html', form=form)

# Маршрут для отображения всех тикетов пользователя
@profile_bp.route('/tickets')
@login_required
def view_tickets():
    tickets = Ticket.query.filter_by(user_id=current_user.id).order_by(Ticket.updated_at.desc()).all()
    return render_template('tickets.html', tickets=tickets)

# Маршрут для просмотра конкретного тикета
@profile_bp.route('/tickets/<int:ticket_id>')
@login_required
def view_ticket(ticket_id):
    ticket = Ticket.query.filter_by(id=ticket_id, user_id=current_user.id).first_or_404()
    messages = TicketMessage.query.filter_by(ticket_id=ticket_id).order_by(TicketMessage.created_at.asc()).all()

    # Если это заявка в команду, получаем соответствующую заявку
    team_application = None
    if ticket.application_type == 'team':
        team_application = TeamApplication.query.filter_by(ticket_id=ticket_id).first()

    return render_template('ticket_details.html', ticket=ticket, messages=messages, team_application=team_application)

# Маршрут для добавления сообщения в тикет
@profile_bp.route('/tickets/<int:ticket_id>/reply', methods=['POST'])
@login_required
def reply_to_ticket(ticket_id):
    ticket = Ticket.query.filter_by(id=ticket_id, user_id=current_user.id).first_or_404()

    if ticket.status == 'closed':
        flash('Этот тикет закрыт. Вы не можете добавлять сообщения.', 'warning')
        return redirect(url_for('profile.view_ticket', ticket_id=ticket_id))

    content = request.form.get('content', '').strip()

    if not content:
        flash('Сообщение не может быть пустым.', 'danger')
        return redirect(url_for('profile.view_ticket', ticket_id=ticket_id))

    message = TicketMessage(
        ticket_id=ticket_id,
        user_id=current_user.id,
        content=content,
        is_admin_reply=current_user.is_admin
    )

    # Обновляем статус тикета
    ticket.status = 'answered' if current_user.is_admin else 'open'
    ticket.updated_at = datetime.utcnow()

    try:
        db.session.add(message)
        db.session.commit()

        # Уведомление в Telegram, если ответил админ
        if current_user.is_admin:
            try:
                from telegram_integration import send_ticket_reply_notification
                send_ticket_reply_notification(ticket_id, content)
            except Exception as e:
                # Логирование ошибки
                print(f"Ошибка при отправке уведомления в Telegram: {str(e)}")

        flash('Сообщение успешно добавлено.', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Ошибка при добавлении сообщения: {str(e)}', 'danger')

    return redirect(url_for('profile.view_ticket', ticket_id=ticket_id))

# Маршрут для закрытия тикета
@profile_bp.route('/tickets/<int:ticket_id>/close', methods=['POST'])
@login_required
def close_ticket(ticket_id):
    ticket = Ticket.query.filter_by(id=ticket_id, user_id=current_user.id).first_or_404()

    ticket.status = 'closed'
    ticket.updated_at = datetime.utcnow()

    try:
        db.session.commit()
        flash('Тикет успешно закрыт.', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Ошибка при закрытии тикета: {str(e)}', 'danger')

    return redirect(url_for('profile.view_ticket', ticket_id=ticket_id))

# Маршрут для повторного открытия тикета
@profile_bp.route('/tickets/<int:ticket_id>/reopen', methods=['POST'])
@login_required
def reopen_ticket(ticket_id):
    ticket = Ticket.query.filter_by(id=ticket_id, user_id=current_user.id).first_or_404()

    if ticket.status != 'closed':
        flash('Этот тикет уже открыт.', 'info')
        return redirect(url_for('profile.view_ticket', ticket_id=ticket_id))

    ticket.status = 'open'
    ticket.updated_at = datetime.utcnow()

    try:
        db.session.commit()
        flash('Тикет успешно открыт заново.', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Ошибка при открытии тикета: {str(e)}', 'danger')

    return redirect(url_for('profile.view_ticket', ticket_id=ticket_id))

# Маршрут для отображения заявок в команду
@profile_bp.route('/team-applications')
@login_required
def view_team_applications():
    applications = TeamApplication.query.filter_by(user_id=current_user.id).order_by(TeamApplication.created_at.desc()).all()
    return render_template('team_applications.html', applications=applications)

# Новые маршруты для безопасности

@profile_bp.route('/security')
@login_required
def security_settings():
    """Страница настроек безопасности"""
    return render_template('profile/security.html')

@profile_bp.route('/change-password', methods=['GET', 'POST'])
@login_required
@rate_limit(limit=5, window=3600, action="change_password")
def change_password():
    """Изменение пароля"""
    form = ChangePasswordForm()

    if form.validate_on_submit():
        current_password = form.current_password.data
        new_password = form.new_password.data

        # Проверяем текущий пароль
        if not current_user.verify_password(current_password):
            flash('Неверный текущий пароль', 'error')
            return render_template('profile/change_password.html', form=form)

        # Валидируем новый пароль
        password_valid, password_error = SecurityValidator.validate_password(new_password)
        if not password_valid:
            flash(password_error, 'error')
            return render_template('profile/change_password.html', form=form)

        # Проверяем, что новый пароль отличается от текущего
        if current_user.verify_password(new_password):
            flash('Новый пароль должен отличаться от текущего', 'error')
            return render_template('profile/change_password.html', form=form)

        # Устанавливаем новый пароль
        current_user.password_hash = generate_password_hash(new_password)
        db.session.commit()

        log_security_event("password_changed", f"User {current_user.id} changed password",
                         user_id=current_user.id, ip_address=request.remote_addr, severity="INFO")

        flash('Пароль успешно изменен', 'success')
        return redirect(url_for('profile.security_settings'))

    return render_template('profile/change_password.html', form=form)

@profile_bp.route('/two-factor-setup', methods=['GET', 'POST'])
@login_required
@require_verified_email
def two_factor_setup():
    """Настройка двухфакторной аутентификации"""
    if current_user.two_factor_enabled:
        flash('Двухфакторная аутентификация уже включена', 'info')
        return redirect(url_for('profile.security_settings'))

    form = TwoFactorSetupForm()

    # Генерируем секрет для 2FA
    secret = current_user.enable_two_factor()
    qr_code = current_user.generate_qr_code()

    if form.validate_on_submit():
        token = form.token.data.strip()

        # Проверяем введенный код
        if current_user.verify_totp(token):
            # Генерируем резервные коды
            backup_codes = current_user.generate_backup_codes()
            db.session.commit()

            log_security_event("2fa_enabled", f"User {current_user.id} enabled 2FA",
                             user_id=current_user.id, ip_address=request.remote_addr, severity="INFO")

            flash('Двухфакторная аутентификация успешно включена!', 'success')
            return render_template('profile/backup_codes.html', codes=backup_codes)
        else:
            flash('Неверный код. Попробуйте еще раз.', 'error')

    return render_template('profile/two_factor_setup.html', form=form,
                         secret=secret, qr_code=qr_code)

@profile_bp.route('/two-factor-disable', methods=['POST'])
@login_required
@rate_limit(limit=3, window=3600, action="disable_2fa")
def two_factor_disable():
    """Отключение двухфакторной аутентификации"""
    if not current_user.two_factor_enabled:
        flash('Двухфакторная аутентификация не включена', 'info')
        return redirect(url_for('profile.security_settings'))

    password = request.form.get('password')
    if not password or not current_user.verify_password(password):
        flash('Неверный пароль', 'error')
        return redirect(url_for('profile.security_settings'))

    current_user.disable_two_factor()
    db.session.commit()

    log_security_event("2fa_disabled", f"User {current_user.id} disabled 2FA",
                     user_id=current_user.id, ip_address=request.remote_addr, severity="WARNING")

    flash('Двухфакторная аутентификация отключена', 'success')
    return redirect(url_for('profile.security_settings'))

@profile_bp.route('/regenerate-backup-codes', methods=['POST'])
@login_required
@rate_limit(limit=3, window=3600, action="regenerate_backup_codes")
def regenerate_backup_codes():
    """Генерация новых резервных кодов"""
    if not current_user.two_factor_enabled:
        flash('Двухфакторная аутентификация не включена', 'error')
        return redirect(url_for('profile.security_settings'))

    password = request.form.get('password')
    if not password or not current_user.verify_password(password):
        flash('Неверный пароль', 'error')
        return redirect(url_for('profile.security_settings'))

    backup_codes = current_user.generate_backup_codes()
    db.session.commit()

    log_security_event("backup_codes_regenerated", f"User {current_user.id} regenerated backup codes",
                     user_id=current_user.id, ip_address=request.remote_addr, severity="INFO")

    return render_template('profile/backup_codes.html', codes=backup_codes)

@profile_bp.route('/delete-account', methods=['GET', 'POST'])
@login_required
@rate_limit(limit=3, window=86400, action="delete_account")  # 3 попытки в день
def delete_account():
    """Удаление аккаунта"""
    form = DeleteAccountForm()

    if form.validate_on_submit():
        password = form.password.data

        # Проверяем пароль
        if not current_user.verify_password(password):
            flash('Неверный пароль', 'error')
            return render_template('profile/delete_account.html', form=form)

        user_id = current_user.id
        username = current_user.username

        # Логируем удаление аккаунта
        log_security_event("account_deleted", f"User {user_id} ({username}) deleted account",
                         user_id=user_id, ip_address=request.remote_addr, severity="HIGH")

        # Удаляем пользователя
        db.session.delete(current_user)
        db.session.commit()

        # Выходим из системы
        from flask_login import logout_user
        logout_user()

        flash('Ваш аккаунт был успешно удален', 'success')
        return redirect(url_for('index'))

    return render_template('profile/delete_account.html', form=form)