<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 Предварительный просмотр - PlayLand</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .preview-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 20px;
            padding: 30px;
        }
        
        .page-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .page-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            border-color: #667eea;
        }
        
        .page-title {
            color: #333;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .page-description {
            color: #666;
            font-size: 14px;
            margin-bottom: 15px;
        }
        
        .btn-preview {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 8px 20px;
            border-radius: 25px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .btn-preview:hover {
            transform: scale(1.05);
            color: white;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .category-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 15px 25px;
            border-radius: 15px;
            margin: 30px 0 20px 0;
            text-align: center;
            font-weight: 600;
            font-size: 18px;
        }
        
        .header-title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-weight: 700;
        }
        
        .header-subtitle {
            text-align: center;
            color: #666;
            margin-bottom: 40px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 10px;
        }
        
        .status-ready {
            background: #d4edda;
            color: #155724;
        }
        
        .status-dev {
            background: #fff3cd;
            color: #856404;
        }
        
        .iframe-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 9999;
            display: none;
        }
        
        .iframe-content {
            position: relative;
            width: 90%;
            height: 90%;
            margin: 5% auto;
            background: white;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .iframe-header {
            background: #333;
            color: white;
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .close-iframe {
            background: #ff4757;
            color: white;
            border: none;
            padding: 5px 15px;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .preview-iframe {
            width: 100%;
            height: calc(100% - 60px);
            border: none;
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <h1 class="header-title">🎨 Предварительный просмотр PlayLand</h1>
        <p class="header-subtitle">Быстрый доступ ко всем страницам сайта для оценки дизайна и функциональности</p>
        
        <!-- Основные страницы -->
        <div class="category-header">🏠 Основные страницы</div>
        <div class="row">
            <div class="col-md-6 col-lg-4">
                <div class="page-card">
                    <h5 class="page-title">🏠 Главная страница <span class="status-badge status-ready">Готово</span></h5>
                    <p class="page-description">Основная страница сайта с информацией о сервере</p>
                    <a href="/" class="btn-preview" target="_blank">Открыть</a>
                    <button onclick="openInFrame('/', 'Главная страница')" class="btn-preview ms-2">Превью</button>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="page-card">
                    <h5 class="page-title">📝 Подача заявки <span class="status-badge status-ready">Готово</span></h5>
                    <p class="page-description">Форма для подачи заявки на вступление</p>
                    <a href="/application" class="btn-preview" target="_blank">Открыть</a>
                    <button onclick="openInFrame('/application', 'Подача заявки')" class="btn-preview ms-2">Превью</button>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="page-card">
                    <h5 class="page-title">📋 Правила <span class="status-badge status-ready">Готово</span></h5>
                    <p class="page-description">Правила сервера и сообщества</p>
                    <a href="/rules" class="btn-preview" target="_blank">Открыть</a>
                    <button onclick="openInFrame('/rules', 'Правила')" class="btn-preview ms-2">Превью</button>
                </div>
            </div>
        </div>
        
        <!-- Информационные страницы -->
        <div class="category-header">ℹ️ Информационные страницы</div>
        <div class="row">
            <div class="col-md-6 col-lg-4">
                <div class="page-card">
                    <h5 class="page-title">👥 Команда <span class="status-badge status-ready">Готово</span></h5>
                    <p class="page-description">Информация о команде администрации</p>
                    <a href="/team" class="btn-preview" target="_blank">Открыть</a>
                    <button onclick="openInFrame('/team', 'Команда')" class="btn-preview ms-2">Превью</button>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="page-card">
                    <h5 class="page-title">🖼️ Галерея <span class="status-badge status-ready">Готово</span></h5>
                    <p class="page-description">Скриншоты и изображения с сервера</p>
                    <a href="/gallery" class="btn-preview" target="_blank">Открыть</a>
                    <button onclick="openInFrame('/gallery', 'Галерея')" class="btn-preview ms-2">Превью</button>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="page-card">
                    <h5 class="page-title">❓ FAQ <span class="status-badge status-ready">Готово</span></h5>
                    <p class="page-description">Часто задаваемые вопросы</p>
                    <a href="/faq" class="btn-preview" target="_blank">Открыть</a>
                    <button onclick="openInFrame('/faq', 'FAQ')" class="btn-preview ms-2">Превью</button>
                </div>
            </div>
        </div>
        
        <!-- Административные страницы -->
        <div class="category-header">🛠️ Административные страницы</div>
        <div class="row">
            <div class="col-md-6 col-lg-4">
                <div class="page-card">
                    <h5 class="page-title">🛠️ Админ панель <span class="status-badge status-dev">В разработке</span></h5>
                    <p class="page-description">Панель управления заявками</p>
                    <a href="/admin" class="btn-preview" target="_blank">Открыть</a>
                    <button onclick="openInFrame('/admin', 'Админ панель')" class="btn-preview ms-2">Превью</button>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="page-card">
                    <h5 class="page-title">👤 Профиль <span class="status-badge status-dev">В разработке</span></h5>
                    <p class="page-description">Личный кабинет пользователя</p>
                    <a href="/profile" class="btn-preview" target="_blank">Открыть</a>
                    <button onclick="openInFrame('/profile', 'Профиль')" class="btn-preview ms-2">Превью</button>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="page-card">
                    <h5 class="page-title">🔐 Авторизация <span class="status-badge status-dev">В разработке</span></h5>
                    <p class="page-description">Вход и регистрация</p>
                    <a href="/auth/login" class="btn-preview" target="_blank">Открыть</a>
                    <button onclick="openInFrame('/auth/login', 'Авторизация')" class="btn-preview ms-2">Превью</button>
                </div>
            </div>
        </div>
        
        <!-- Специальные страницы -->
        <div class="category-header">🔧 Специальные страницы</div>
        <div class="row">
            <div class="col-md-6 col-lg-4">
                <div class="page-card">
                    <h5 class="page-title">💼 Заявка в команду <span class="status-badge status-ready">Готово</span></h5>
                    <p class="page-description">Форма для подачи заявки в команду</p>
                    <a href="/team_application" class="btn-preview" target="_blank">Открыть</a>
                    <button onclick="openInFrame('/team_application', 'Заявка в команду')" class="btn-preview ms-2">Превью</button>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="page-card">
                    <h5 class="page-title">❤️ Health Check <span class="status-badge status-ready">Готово</span></h5>
                    <p class="page-description">Проверка состояния сервера</p>
                    <a href="/health" class="btn-preview" target="_blank">Открыть</a>
                    <button onclick="openInFrame('/health', 'Health Check')" class="btn-preview ms-2">Превью</button>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-4">
            <p class="text-muted">
                💡 <strong>Совет:</strong> Используйте кнопку "Превью" для быстрого просмотра в модальном окне, 
                или "Открыть" для открытия в новой вкладке.
            </p>
        </div>
    </div>
    
    <!-- Модальное окно для iframe -->
    <div class="iframe-container" id="iframeContainer">
        <div class="iframe-content">
            <div class="iframe-header">
                <span id="iframeTitle">Предварительный просмотр</span>
                <button class="close-iframe" onclick="closeFrame()">✕ Закрыть</button>
            </div>
            <iframe class="preview-iframe" id="previewFrame" src=""></iframe>
        </div>
    </div>
    
    <script>
        function openInFrame(url, title) {
            document.getElementById('previewFrame').src = url;
            document.getElementById('iframeTitle').textContent = title;
            document.getElementById('iframeContainer').style.display = 'block';
        }
        
        function closeFrame() {
            document.getElementById('iframeContainer').style.display = 'none';
            document.getElementById('previewFrame').src = '';
        }
        
        // Закрытие по клику вне iframe
        document.getElementById('iframeContainer').addEventListener('click', function(e) {
            if (e.target === this) {
                closeFrame();
            }
        });
        
        // Закрытие по ESC
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeFrame();
            }
        });
    </script>
</body>
</html>
