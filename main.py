#!/usr/bin/env python3
"""
Главный файл запуска PlayLand сервера.
Запускает Telegram бот и веб-сайт на одном сервере.
"""

import os
import sys
import threading
import logging
from pathlib import Path

# Добавляем путь к модулям
sys.path.insert(0, str(Path(__file__).parent))

# Настройка кодировки для Windows консоли
if sys.platform == "win32":
    import locale
    try:
        # Пытаемся установить UTF-8 кодировку
        locale.setlocale(locale.LC_ALL, 'ru_RU.UTF-8')
    except locale.Error:
        try:
            locale.setlocale(locale.LC_ALL, 'Russian_Russia.1251')
        except locale.Error:
            pass  # Используем системную кодировку по умолчанию

# Импортируем только нужные настройки
WEBSITE_HOST = os.getenv('WEBSITE_HOST', '0.0.0.0')
WEBSITE_PORT = int(os.getenv('WEBSITE_PORT', 5000))
DEBUG = os.getenv('DEBUG', 'false').lower() == 'true'
TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')

# Создаем папки для логов
os.makedirs('logs', exist_ok=True)

# Создаем специальный форматтер для безопасного логирования
class SafeFormatter(logging.Formatter):
    def format(self, record):
        try:
            return super().format(record)
        except UnicodeEncodeError:
            # Заменяем проблемные символы на безопасные
            record.msg = str(record.msg).encode('ascii', 'replace').decode('ascii')
            return super().format(record)

# Настройка логирования
console_handler = logging.StreamHandler()
console_handler.setFormatter(SafeFormatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))

file_handler = logging.FileHandler('logs/main.log', encoding='utf-8')
file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))

logging.basicConfig(
    level=logging.INFO,
    handlers=[console_handler, file_handler]
)
logger = logging.getLogger('main')

def run_telegram_bot():
    """Запускает Telegram бота"""
    try:
        import asyncio
        logger.info("🤖 Запуск Telegram бота...")

        # Создаем новый event loop для потока
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        from playland.tg_bot.bot import main as bot_main
        bot_main()
    except Exception as e:
        logger.error(f"❌ Ошибка запуска Telegram бота: {e}")
        # Не поднимаем исключение, чтобы веб-сайт продолжал работать

def run_website():
    """Запускает веб-сайт"""
    try:
        logger.info("🌐 Запуск веб-сайта...")
        from playland.website.app import create_app
        app = create_app()
        app.run(
            host=WEBSITE_HOST,
            port=WEBSITE_PORT,
            debug=DEBUG
        )
    except Exception as e:
        logger.error(f"❌ Ошибка запуска веб-сайта: {e}")
        raise

def main():
    """Главная функция запуска"""
    logger.info("🚀 Запуск PlayLand сервера...")
    logger.info("=" * 50)
    
    # Проверяем конфигурацию
    if not TELEGRAM_BOT_TOKEN:
        logger.error("❌ TELEGRAM_BOT_TOKEN не установлен!")
        sys.exit(1)
    
    # База данных инициализируется автоматически в app_simple.py
    logger.info("📊 База данных будет инициализирована автоматически")
    
    # Папка logs уже создана выше
    
    # Запускаем компоненты
    try:
        # Запускаем Telegram бота в отдельном потоке
        bot_thread = threading.Thread(target=run_telegram_bot, daemon=True)
        bot_thread.start()
        logger.info("✅ Telegram бот запущен в фоне")
        
        # Запускаем веб-сайт в основном потоке
        logger.info("🌐 Запуск веб-сайта на основном потоке...")
        run_website()
        
    except KeyboardInterrupt:
        logger.info("⏹️ Получен сигнал остановки")
    except Exception as e:
        logger.error(f"❌ Критическая ошибка: {e}")
        sys.exit(1)
    finally:
        logger.info("🛑 PlayLand сервер остановлен")

if __name__ == "__main__":
    main()
