{% extends "base.html" %}

{% block title %}PLAYLAND - Подать заявку{% endblock %}

{% block namespace %}application{% endblock %}

{% block extra_head %}
    <style>
    .hero {
        background-image: url("{{ url_for('static', filename='images/grass_background.jpg') }}");
            background-size: cover;
        background-position: center center;
        background-attachment: fixed;
        height: 50vh;
            display: flex;
        flex-direction: column;
            justify-content: center;
            align-items: center;
        text-align: center;
        padding: 20px;
        padding-top: 100px;
        box-sizing: border-box;
            position: relative;
        overflow: hidden;
    }
    
    .hero::before {
        content: '';
        position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        background-color: rgba(0, 0, 0, 0.6);
        backdrop-filter: blur(2px);
            z-index: 1;
    }
    
    .hero-content {
        position: relative;
        z-index: 2;
        max-width: 900px;
        width: 100%;
    }
    
    .hero h1 {
        font-family: 'Press Start 2P', cursive;
        font-size: 3.5em;
        color: #00ff00;
        text-shadow: 2px 2px 0 #000000, 0 0 15px #00ff00;
        margin-bottom: 20px;
        line-height: 1.3;
        animation: textPopIn 0.8s cubic-bezier(0.68, -0.55, 0.27, 1.55) 0.5s forwards, heroGlow 2.5s infinite alternate 1s;
                opacity: 0;
        transform: scale(0.5);
    }
    
    @keyframes textPopIn {
            to {
                opacity: 1;
            transform: scale(1);
        }
    }
    
    @keyframes heroGlow {
        0% {
            text-shadow: 2px 2px 0 #000000, 0 0 5px rgba(0, 255, 0, 0.5); 
        }
        100% {
            text-shadow: 2px 2px 0 #000000, 0 0 15px #00ff00, 0 0 30px #00ff00, 0 0 40px #00ff00;
        }
    }

    .content-section {
        padding: 80px 20px;
        background-color: rgba(0, 0, 0, 0.8);
    }
    
    .content-section h2 {
        font-family: 'Press Start 2P', cursive;
        font-size: 2.2em;
        color: #00ff00;
        text-align: center;
        margin-bottom: 40px;
        text-shadow: 0 0 10px #00ff00;
    }
    
    .application-container {
        max-width: 800px;
        margin: 0 auto;
        background-color: rgba(0, 0, 0, 0.5);
        border: 2px solid #00ff00;
        border-radius: 10px;
        padding: 40px;
        box-shadow: 0 0 20px rgba(0, 255, 0, 0.2);
    }
    
    .form-group {
        margin-bottom: 25px;
    }
    
    .form-group label {
            display: block;
        margin-bottom: 10px;
        font-family: 'Press Start 2P', cursive;
        font-size: 0.9em;
        color: #00ff00;
    }
    
    .form-control {
            width: 100%;
        padding: 12px 15px;
        background-color: rgba(0, 0, 0, 0.7);
        border: 2px solid rgba(0, 255, 0, 0.5);
            border-radius: 5px;
        color: #ffffff;
        font-family: 'Roboto', sans-serif;
        font-size: 1em;
        transition: all 0.3s ease;
        box-sizing: border-box;
    }
    
    .form-control:focus {
        border-color: #00ff00;
            outline: none;
        box-shadow: 0 0 15px rgba(0, 255, 0, 0.3);
    }
    
    textarea.form-control {
        min-height: 120px;
        resize: vertical;
    }
    
    .form-check {
        display: flex;
        align-items: center;
        margin-top: 5px;
    }
    
    .form-check-input {
        margin-right: 10px;
        width: 18px;
        height: 18px;
        accent-color: #00ff00;
    }
    
    .form-check-label {
        font-size: 0.9em;
        color: #f0f0f0;
    }
    
    .error-message {
        color: #ff6b6b;
        margin-top: 5px;
        font-size: 0.9em;
    }
    
    .btn-minecraft {
            display: inline-block;
        padding: 12px 24px;
        background-color: #00ff00;
        color: #000;
        text-decoration: none;
        font-family: 'Press Start 2P', cursive;
        font-size: 0.9em;
        text-transform: uppercase;
        border: none;
        position: relative;
        overflow: hidden;
        transition: all 0.3s;
        box-shadow: 0 4px 0 #006400, 0 0 20px rgba(0, 255, 0, 0.4);
        border-radius: 3px;
        cursor: pointer;
            margin-top: 20px;
    }

    .btn-minecraft:hover {
        transform: translateY(-5px);
        box-shadow: 0 9px 0 #006400, 0 0 30px rgba(0, 255, 0, 0.6);
    }

    .form-submit {
        text-align: center;
        margin-top: 40px;
    }

    /* Стили для блока авторизации */
    .auth-required-container {
        max-width: 700px;
        margin: 0 auto;
    }

    .auth-required-card {
        background: rgba(26, 26, 26, 0.95);
        border: 2px solid #00ff00;
        border-radius: 15px;
        padding: 50px 40px;
        text-align: center;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5), 0 0 30px rgba(0, 255, 0, 0.2);
        position: relative;
        overflow: hidden;
        animation: cardSlideIn 0.8s cubic-bezier(0.68, -0.55, 0.27, 1.55) forwards;
        opacity: 0;
        transform: translateY(50px);
    }

    @keyframes cardSlideIn {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .auth-required-card::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        border: 2px solid transparent;
        border-radius: 15px;
        background: linear-gradient(45deg, #00ff00, transparent, #00ff00);
        background-size: 400% 400%;
        opacity: 0.3;
        z-index: -1;
        animation: borderGlow 3s ease infinite;
    }

    @keyframes borderGlow {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    .auth-icon {
        font-size: 4rem;
        color: #00ff00;
        margin-bottom: 25px;
        text-shadow: 0 0 20px rgba(0, 255, 0, 0.5);
        animation: iconPulse 2s ease-in-out infinite alternate;
    }

    @keyframes iconPulse {
        from { text-shadow: 0 0 10px rgba(0, 255, 0, 0.5); }
        to { text-shadow: 0 0 30px #00ff00, 0 0 40px #00ff00; }
    }

    .auth-required-card h3 {
        font-family: 'Press Start 2P', cursive;
        font-size: 1.8rem;
        color: #00ff00;
        margin-bottom: 20px;
        text-shadow: 0 0 15px rgba(0, 255, 0, 0.5);
    }

    .auth-required-card p {
        color: #f0f0f0;
        font-size: 1.1rem;
        margin-bottom: 30px;
        line-height: 1.6;
        font-family: 'Roboto', sans-serif;
    }

    .auth-buttons {
        display: flex;
        gap: 20px;
        justify-content: center;
        margin-bottom: 40px;
        flex-wrap: wrap;
    }

    .btn-outline {
        display: inline-block;
        padding: 12px 24px;
        background-color: transparent;
        border: 2px solid #00ff00;
        color: #00ff00;
        text-decoration: none;
        font-family: 'Press Start 2P', cursive;
        font-size: 0.9em;
        text-transform: uppercase;
        position: relative;
        overflow: hidden;
        transition: all 0.3s;
        box-shadow: 0 4px 0 rgba(0, 255, 0, 0.3), 0 0 20px rgba(0, 255, 0, 0.2);
        border-radius: 3px;
        cursor: pointer;
    }

    .btn-outline:hover {
        background-color: rgba(0, 255, 0, 0.1);
        transform: translateY(-5px);
        box-shadow: 0 9px 0 rgba(0, 255, 0, 0.3), 0 0 30px rgba(0, 255, 0, 0.4);
        color: #00ff00;
        text-decoration: none;
    }

    .auth-info {
        background: rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(0, 255, 0, 0.3);
        border-radius: 10px;
        padding: 25px;
        text-align: left;
    }

    .auth-info h4 {
        font-family: 'Press Start 2P', cursive;
        font-size: 1rem;
        color: #00ff00;
        margin-bottom: 15px;
        text-align: center;
    }

    .auth-info ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .auth-info li {
        color: #f0f0f0;
        margin-bottom: 10px;
        padding-left: 25px;
        position: relative;
        font-family: 'Roboto', sans-serif;
    }

    .auth-info li::before {
        content: '✓';
        position: absolute;
        left: 0;
        color: #00ff00;
        font-weight: bold;
        font-size: 1.1rem;
    }

    @media (max-width: 768px) {
        .hero h1 {
            font-size: 2.2em;
        }

        .content-section h2 {
            font-size: 1.8em;
        }

        .application-container, .auth-required-card {
            padding: 30px 20px;
        }

        .btn-minecraft, .btn-outline {
            padding: 15px 25px;
            font-size: 0.8em;
        }

        .auth-buttons {
            flex-direction: column;
            align-items: center;
        }

        .auth-required-card h3 {
            font-size: 1.4rem;
        }

        .auth-icon {
            font-size: 3rem;
        }
    }

    @media (max-width: 480px) {
        .auth-required-card {
            padding: 25px 15px;
        }

        .auth-required-card h3 {
            font-size: 1.2rem;
        }

        .auth-required-card p {
            font-size: 1rem;
        }

        .btn-minecraft, .btn-outline {
            padding: 12px 20px;
            font-size: 0.7em;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="hero">
    <div class="particles-container" id="particles"></div>
    <div class="hero-content">
        <h1 class="animate-fade-in-up">ПОДАТЬ ЗАЯВКУ</h1>
    </div>
</section>

<section class="content-section">
    <h2 class="animate-fade-in">Присоединяйтесь к нашему сообществу</h2>

    {% if not current_user.is_authenticated %}
    <!-- Сообщение для неавторизованных пользователей -->
    <div class="auth-required-container animate-fade-in-up">
        <div class="auth-required-card">
            <div class="auth-icon">
                <i class="fas fa-user-lock"></i>
            </div>
            <h3>Требуется авторизация</h3>
            <p>Для подачи заявки на сервер PlayLand необходимо зарегистрироваться или войти в систему.</p>
            <div class="auth-buttons">
                <a href="{{ url_for('auth.enhanced_register') }}" class="btn-minecraft">Зарегистрироваться</a>
                <a href="{{ url_for('auth.enhanced_login') }}" class="btn-outline">Войти</a>
            </div>
            <div class="auth-info">
                <h4>Почему нужна регистрация?</h4>
                <ul>
                    <li>Отслеживание статуса заявки</li>
                    <li>Связь с администрацией</li>
                    <li>Доступ к системе поддержки</li>
                    <li>Уведомления о решении</li>
                </ul>
            </div>
        </div>
    </div>
    {% else %}
    <!-- Форма заявки для авторизованных пользователей -->
    <div class="application-container animate-fade-in-up">
        <form class="application-form" method="POST" action="{{ url_for('application') }}">
            {{ csrf_token() }}
            
            <div class="form-group">
                <label for="minecraft_nickname">Ваш никнейм в Minecraft*</label>
                <input type="text" class="form-control" id="minecraft_nickname" name="minecraft_nickname" value="{{ form_data.minecraft_nickname if form_data else '' }}" required>
                {% if errors and errors.minecraft_nickname %}
                    <div class="error-message">{{ errors.minecraft_nickname }}</div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <label for="discord_tag">Ваш Discord тег*</label>
                <input type="text" class="form-control" id="discord_tag" name="discord_tag" placeholder="username#0000" value="{{ form_data.discord_tag if form_data else '' }}" required>
                {% if errors and errors.discord_tag %}
                    <div class="error-message">{{ errors.discord_tag }}</div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <label for="email">Ваш Email*</label>
                <input type="email" class="form-control" id="email" name="email" value="{{ form_data.email if form_data else '' }}" required>
                {% if errors and errors.email %}
                    <div class="error-message">{{ errors.email }}</div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <label for="age">Ваш возраст*</label>
                <input type="number" class="form-control" id="age" name="age" min="8" max="100" value="{{ form_data.age if form_data else '' }}" required>
                {% if errors and errors.age %}
                    <div class="error-message">{{ errors.age }}</div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <label for="about_yourself">Расскажите о себе*</label>
                <textarea class="form-control" id="about_yourself" name="about_yourself" rows="4" required>{{ form_data.about_yourself if form_data else '' }}</textarea>
                {% if errors and errors.about_yourself %}
                    <div class="error-message">{{ errors.about_yourself }}</div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <label for="find_server">Как вы нашли наш сервер?*</label>
                <select class="form-control" id="find_server" name="find_server" required>
                    <option value="" disabled selected>Выберите вариант</option>
                    <option value="search" {% if form_data and form_data.find_server == 'search' %}selected{% endif %}>Поисковые системы</option>
                    <option value="friends" {% if form_data and form_data.find_server == 'friends' %}selected{% endif %}>От друзей</option>
                    <option value="social" {% if form_data and form_data.find_server == 'social' %}selected{% endif %}>Социальные сети</option>
                    <option value="server_list" {% if form_data and form_data.find_server == 'server_list' %}selected{% endif %}>Списки серверов</option>
                    <option value="other" {% if form_data and form_data.find_server == 'other' %}selected{% endif %}>Другое</option>
                </select>
                {% if errors and errors.find_server %}
                    <div class="error-message">{{ errors.find_server }}</div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <label for="friend_referral">Если вас пригласил друг, укажите его никнейм</label>
                <input type="text" class="form-control" id="friend_referral" name="friend_referral" value="{{ form_data.friend_referral if form_data else '' }}">
            </div>
            
            <div class="form-group">
                <label for="minecraft_experience">Ваш опыт игры в Minecraft*</label>
                <select class="form-control" id="minecraft_experience" name="minecraft_experience" required>
                    <option value="" disabled selected>Выберите вариант</option>
                    <option value="beginner" {% if form_data and form_data.minecraft_experience == 'beginner' %}selected{% endif %}>Новичок (меньше года)</option>
                    <option value="intermediate" {% if form_data and form_data.minecraft_experience == 'intermediate' %}selected{% endif %}>Средний (1-3 года)</option>
                    <option value="advanced" {% if form_data and form_data.minecraft_experience == 'advanced' %}selected{% endif %}>Опытный (3-5 лет)</option>
                    <option value="expert" {% if form_data and form_data.minecraft_experience == 'expert' %}selected{% endif %}>Эксперт (более 5 лет)</option>
                </select>
                {% if errors and errors.minecraft_experience %}
                    <div class="error-message">{{ errors.minecraft_experience }}</div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <label for="minecraft_preferences">Что вам нравится делать в Minecraft больше всего?</label>
                <textarea class="form-control" id="minecraft_preferences" name="minecraft_preferences" rows="3">{{ form_data.minecraft_preferences if form_data else '' }}</textarea>
                </div>
            
            <div class="form-group">
                <label for="additional_info">Дополнительная информация</label>
                <textarea class="form-control" id="additional_info" name="additional_info" rows="3">{{ form_data.additional_info if form_data else '' }}</textarea>
            </div>
            
            <div class="form-group">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="rules_acceptance" name="rules_acceptance" required {% if form_data and form_data.rules_acceptance %}checked{% endif %}>
                    <label class="form-check-label" for="rules_acceptance">Я ознакомился и согласен с <a href="{{ url_for('rules') }}" target="_blank">правилами сервера</a>*</label>
                </div>
                {% if errors and errors.rules_acceptance %}
                    <div class="error-message">{{ errors.rules_acceptance }}</div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="notification_acceptance" name="notification_acceptance" {% if form_data and form_data.notification_acceptance %}checked{% endif %}>
                    <label class="form-check-label" for="notification_acceptance">Я согласен получать уведомления о новостях сервера</label>
                </div>
            </div>
            
            <div class="form-submit">
                <button type="submit" class="btn-minecraft">Отправить заявку</button>
            </div>
        </form>
    </div>
    {% endif %}
</section>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Создаем частицы
        const particlesContainer = document.getElementById('particles');
        if (!particlesContainer) return;
        
        const colors = [
            'rgba(0, 255, 0, 0.4)',
            'rgba(50, 205, 50, 0.4)',
            'rgba(173, 255, 47, 0.4)',
            'rgba(152, 251, 152, 0.4)'
        ];
        
        for (let i = 0; i < 50; i++) {
            const size = Math.random() * 6 + 3;
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.width = `${size}px`;
            particle.style.height = `${size}px`;
            particle.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
            particle.style.left = `${Math.random() * 100}%`;
            particle.style.setProperty('--rand-x', Math.random());
            particle.style.boxShadow = `0 0 ${size}px ${particle.style.backgroundColor}`;
            particle.style.animationDuration = `${Math.random() * 20 + 10}s`;
            particle.style.animationDelay = `${Math.random() * 5}s`;
            
            particlesContainer.appendChild(particle);
            }
        });
    </script>
{% endblock %}