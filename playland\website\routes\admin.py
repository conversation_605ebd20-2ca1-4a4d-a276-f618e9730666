from flask import Blueprint, render_template, redirect, url_for, request, flash, abort
from flask_login import login_required, current_user
from playland.website.models import db, User, Ticket, TicketMessage, TeamApplication, AuditLog
from datetime import datetime
from functools import wraps

admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

# Декоратор для проверки прав администратора
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin:
            abort(403)  # Запретить доступ
        return f(*args, **kwargs)
    return decorated_function

# Главная страница админ-панели
@admin_bp.route('/')
@login_required
@admin_required
def admin_panel():
    # Получаем основную статистику
    tickets_count = Ticket.query.count()
    open_tickets_count = Ticket.query.filter_by(status='open').count()
    users_count = User.query.count()
    team_applications_count = TeamApplication.query.count()
    pending_team_applications = TeamApplication.query.filter_by(status='pending').count()
    
    # Последние тикеты
    latest_tickets = Ticket.query.order_by(Ticket.updated_at.desc()).limit(5).all()
    
    # Последние заявки в команду
    latest_team_applications = TeamApplication.query.order_by(TeamApplication.created_at.desc()).limit(5).all()
    
    return render_template('admin/dashboard.html', 
                           tickets_count=tickets_count,
                           open_tickets_count=open_tickets_count,
                           users_count=users_count,
                           team_applications_count=team_applications_count,
                           pending_team_applications=pending_team_applications,
                           latest_tickets=latest_tickets,
                           latest_team_applications=latest_team_applications)

# Список всех тикетов
@admin_bp.route('/tickets')
@login_required
@admin_required
def tickets_list():
    status_filter = request.args.get('status')
    type_filter = request.args.get('type')
    
    # Базовый запрос
    query = Ticket.query
    
    # Применяем фильтры
    if status_filter:
        query = query.filter_by(status=status_filter)
    if type_filter:
        query = query.filter_by(application_type=type_filter)
    
    # Сортировка и получение результатов
    tickets = query.order_by(Ticket.updated_at.desc()).all()
    
    return render_template('admin/tickets.html', tickets=tickets, 
                          status_filter=status_filter, type_filter=type_filter)

# Просмотр тикета и ответ на него
@admin_bp.route('/tickets/<int:ticket_id>', methods=['GET', 'POST'])
@login_required
@admin_required
def view_ticket(ticket_id):
    ticket = Ticket.query.get_or_404(ticket_id)
    messages = TicketMessage.query.filter_by(ticket_id=ticket_id).order_by(TicketMessage.created_at.asc()).all()
    
    # Получаем связанную заявку в команду, если есть
    team_application = None
    if ticket.application_type == 'team':
        team_application = TeamApplication.query.filter_by(ticket_id=ticket_id).first()
    
    # Если это POST-запрос, обрабатываем ответ на тикет
    if request.method == 'POST':
        content = request.form.get('content', '').strip()
        action = request.form.get('action', '')
        
        if content:
            # Добавляем ответ
            message = TicketMessage(
                ticket_id=ticket_id,
                user_id=current_user.id,
                content=content,
                is_admin_reply=True
            )
            db.session.add(message)
            
            # Обновляем статус тикета
            ticket.status = 'answered'
            ticket.updated_at = datetime.utcnow()
            
            # Логирование
            log = AuditLog(
                action="ticket_reply",
                user_id=current_user.id,
                details=f"Ответ администратора на тикет #{ticket_id}"
            )
            db.session.add(log)
            
            try:
                db.session.commit()
                
                # Отправка уведомления пользователю через Telegram
                try:
                    from telegram_integration import send_ticket_reply_notification
                    send_ticket_reply_notification(ticket_id, content)
                except Exception as e:
                    flash(f'Ошибка при отправке уведомления: {str(e)}', 'warning')
                
                flash('Ответ успешно отправлен', 'success')
            except Exception as e:
                db.session.rollback()
                flash(f'Ошибка при отправке ответа: {str(e)}', 'danger')
        
        # Обрабатываем действие (изменение статуса)
        if action:
            if action == 'close' and ticket.status != 'closed':
                ticket.status = 'closed'
                flash('Тикет закрыт', 'success')
            elif action == 'reopen' and ticket.status == 'closed':
                ticket.status = 'open'
                flash('Тикет открыт', 'success')
            
            ticket.updated_at = datetime.utcnow()
            
            try:
                db.session.commit()
            except Exception as e:
                db.session.rollback()
                flash(f'Ошибка при изменении статуса тикета: {str(e)}', 'danger')
        
        return redirect(url_for('admin.view_ticket', ticket_id=ticket_id))
    
    return render_template('admin/ticket_details.html', ticket=ticket, 
                          messages=messages, team_application=team_application)

# Список заявок в команду
@admin_bp.route('/team-applications')
@login_required
@admin_required
def team_applications_list():
    status_filter = request.args.get('status')
    
    # Базовый запрос
    query = TeamApplication.query
    
    # Применяем фильтр по статусу
    if status_filter:
        query = query.filter_by(status=status_filter)
    
    # Получаем результаты
    applications = query.order_by(TeamApplication.created_at.desc()).all()
    
    return render_template('admin/team_applications.html', 
                          applications=applications, 
                          status_filter=status_filter)

# Просмотр и обработка заявки в команду
@admin_bp.route('/team-applications/<int:application_id>', methods=['GET', 'POST'])
@login_required
@admin_required
def view_team_application(application_id):
    application = TeamApplication.query.get_or_404(application_id)
    
    # Если это заявка с тикетом, получаем сообщения
    messages = []
    if application.ticket_id:
        ticket = Ticket.query.get(application.ticket_id)
        if ticket:
            messages = TicketMessage.query.filter_by(ticket_id=ticket.id).order_by(TicketMessage.created_at.asc()).all()
    
    # Если это POST-запрос, обрабатываем действие
    if request.method == 'POST':
        action = request.form.get('action', '')
        comment = request.form.get('comment', '').strip()
        
        if action in ['approve', 'reject', 'interview']:
            # Обновляем статус заявки
            old_status = application.status
            
            if action == 'approve':
                application.status = 'approved'
                status_text = 'одобрена'
            elif action == 'reject':
                application.status = 'rejected'
                status_text = 'отклонена'
            elif action == 'interview':
                application.status = 'interview'
                status_text = 'назначено интервью'
            
            application.reviewed_at = datetime.utcnow()
            application.reviewed_by = current_user.id
            
            # Логирование
            log = AuditLog(
                action=f"team_application_{action}",
                user_id=current_user.id,
                details=f"Заявка в команду #{application_id} {status_text} администратором {current_user.username}"
            )
            db.session.add(log)
            
            # Если есть комментарий и тикет, добавляем сообщение
            if comment and application.ticket_id:
                message = TicketMessage(
                    ticket_id=application.ticket_id,
                    user_id=current_user.id,
                    content=f"Статус заявки изменен на: {status_text}\n\n{comment}",
                    is_admin_reply=True
                )
                db.session.add(message)
                
                # Обновляем статус тикета
                ticket = Ticket.query.get(application.ticket_id)
                if ticket:
                    ticket.status = 'answered'
                    ticket.updated_at = datetime.utcnow()
            
            try:
                db.session.commit()
                
                # Отправка уведомления пользователю
                try:
                    from telegram_integration import send_team_application_status_notification
                    send_team_application_status_notification(application_id, status_text, comment)
                except Exception as e:
                    flash(f'Ошибка при отправке уведомления: {str(e)}', 'warning')
                
                flash(f'Статус заявки изменен на: {status_text}', 'success')
            except Exception as e:
                db.session.rollback()
                flash(f'Ошибка при обновлении статуса заявки: {str(e)}', 'danger')
        
        return redirect(url_for('admin.view_team_application', application_id=application_id))
    
    return render_template('admin/team_application_details.html', 
                          application=application, 
                          messages=messages)

# Список пользователей
@admin_bp.route('/users')
@login_required
@admin_required
def users_list():
    search_query = request.args.get('search', '')
    
    # Базовый запрос
    query = User.query
    
    # Применяем поиск
    if search_query:
        query = query.filter(
            db.or_(
                User.username.ilike(f'%{search_query}%'),
                User.nickname.ilike(f'%{search_query}%'),
                User.email.ilike(f'%{search_query}%')
            )
        )
    
    # Получаем результаты
    users = query.order_by(User.username).all()
    
    return render_template('admin/users.html', users=users, search_query=search_query)

# Изменение роли пользователя
@admin_bp.route('/users/<int:user_id>/toggle-admin', methods=['POST'])
@login_required
@admin_required
def toggle_admin_role(user_id):
    user = User.query.get_or_404(user_id)
    
    # Предотвращаем самоотключение прав администратора
    if user.id == current_user.id:
        flash('Вы не можете изменить свои собственные права администратора', 'danger')
        return redirect(url_for('admin.users_list'))
    
    # Изменяем статус администратора
    user.is_admin = not user.is_admin
    
    # Логирование
    log = AuditLog(
        action="user_admin_toggle",
        user_id=current_user.id,
        details=f"Статус администратора пользователя {user.username} изменен на: {user.is_admin}"
    )
    db.session.add(log)
    
    try:
        db.session.commit()
        status = "присвоены" if user.is_admin else "сняты"
        flash(f'Права администратора {status} для пользователя {user.username}', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Ошибка при изменении прав: {str(e)}', 'danger')
    
    return redirect(url_for('admin.users_list')) 