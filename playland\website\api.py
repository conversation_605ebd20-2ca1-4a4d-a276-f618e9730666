#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
API для веб-сайта PlayLand.
"""

from flask import Blueprint, request, jsonify, current_app
from flask_login import current_user, login_required
import hmac
import hashlib
import json
import time
from datetime import datetime
from functools import wraps

from playland.website.models import db, User, Application, ServerEvent
from playland.website.telegram_sync import telegram_sync
from playland.utils.error_handler import handle_exceptions, InvalidRequestError, UnauthorizedError

# Импортируем модуль для работы с API
import requests

# Создаем Blueprint для API
api = Blueprint('api', __name__)

# Функция для проверки подписи API
def verify_api_signature(data, signature):
    """
    Проверяет подпись API.

    Args:
        data (dict): Данные запроса.
        signature (str): Подпись запроса.

    Returns:
        bool: True, если подпись верна, иначе False.
    """
    if not signature:
        return False

    # Получаем секретный ключ API
    secret_key = current_app.config.get('API_SECRET_KEY')

    # Создаем подпись
    expected_signature = hmac.new(
        secret_key.encode('utf-8'),
        json.dumps(data, sort_keys=True).encode('utf-8'),
        hashlib.sha256
    ).hexdigest()

    # Сравниваем подписи
    return hmac.compare_digest(expected_signature, signature)

def verify_telegram_signature(data):
    """
    Проверяет подпись данных от Telegram.

    Args:
        data (dict): Данные от Telegram

    Returns:
        bool: True, если подпись верна, иначе False
    """
    if not all(key in data for key in ['hash', 'auth_date', 'id']):
        return False

    # Получаем данные
    auth_date = data.get('auth_date')
    telegram_id = data.get('id')
    first_name = data.get('first_name', '')
    last_name = data.get('last_name', '')
    username = data.get('username', '')
    photo_url = data.get('photo_url', '')
    hash_value = data.get('hash')

    # Проверяем, что запрос не устарел (не старше 24 часов)
    current_time = int(time.time())
    if current_time - int(auth_date) > 86400:  # 24 часа
        return False

    # Создаем строку для проверки подписи
    check_string = []
    for key, value in sorted(data.items()):
        if key != 'hash':
            check_string.append(f"{key}={value}")

    check_string = '\n'.join(check_string)

    # Создаем секретный ключ
    secret_key = hashlib.sha256(current_app.config.get('TELEGRAM_BOT_TOKEN', '').encode()).digest()

    # Создаем подпись
    signature = hmac.new(
        secret_key,
        check_string.encode(),
        hashlib.sha256
    ).hexdigest()

    # Проверяем подпись
    return signature == hash_value

# Декоратор для проверки подписи API
def require_api_signature(f):
    """
    Декоратор для проверки подписи API.
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Получаем данные запроса
        data = request.get_json()

        # Получаем подпись из заголовка
        signature = request.headers.get('X-API-Signature')

        # Проверяем подпись
        if not verify_api_signature(data, signature):
            raise UnauthorizedError("Неверная подпись API")

        return f(*args, **kwargs)

    return decorated_function

# Маршруты API

@api.route('/status', methods=['GET'])
@handle_exceptions
def api_status():
    """
    Возвращает статус API.
    """
    return jsonify({
        'status': 'ok',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    })

@api.route('/server/status', methods=['GET'])
@handle_exceptions
def server_status():
    """
    Возвращает статус сервера.
    """
    # Здесь должна быть логика получения статуса сервера
    # Например, запрос к Minecraft серверу через RCON

    # Пример ответа
    return jsonify({
        'status': 'online',
        'players': {
            'online': 10,
            'max': 100
        },
        'version': '1.19.2',
        'motd': 'Welcome to PlayLand!',
        'timestamp': datetime.now().isoformat()
    })

@api.route('/server/events', methods=['GET'])
@handle_exceptions
def server_events():
    """
    Возвращает последние события сервера.
    """
    # Получаем количество событий из параметров запроса
    limit = request.args.get('limit', 10, type=int)

    # Получаем последние события из базы данных
    events = ServerEvent.query.order_by(ServerEvent.timestamp.desc()).limit(limit).all()

    # Преобразуем события в словари
    events_dict = [event.to_dict() for event in events]

    return jsonify({
        'events': events_dict,
        'count': len(events_dict),
        'timestamp': datetime.now().isoformat()
    })

@api.route('/applications', methods=['GET'])
@login_required
@handle_exceptions
def applications():
    """
    Возвращает список заявок.
    """
    # Проверяем, является ли пользователь администратором
    if not current_user.is_admin:
        raise UnauthorizedError("Доступ запрещен")

    # Получаем статус из параметров запроса
    status = request.args.get('status')

    # Создаем запрос
    query = Application.query

    # Фильтруем по статусу, если указан
    if status:
        query = query.filter_by(status=status)

    # Получаем заявки из базы данных
    applications = query.order_by(Application.created_at.desc()).all()

    # Преобразуем заявки в словари
    applications_dict = [app.to_dict() for app in applications]

    return jsonify({
        'applications': applications_dict,
        'count': len(applications_dict),
        'timestamp': datetime.now().isoformat()
    })

@api.route('/applications/<int:application_id>', methods=['GET'])
@login_required
@handle_exceptions
def application(application_id):
    """
    Возвращает информацию о заявке.
    """
    # Проверяем, является ли пользователь администратором
    if not current_user.is_admin:
        raise UnauthorizedError("Доступ запрещен")

    # Получаем заявку из базы данных
    application = Application.query.get_or_404(application_id)

    return jsonify({
        'application': application.to_dict(),
        'timestamp': datetime.now().isoformat()
    })

@api.route('/applications/<int:application_id>/approve', methods=['POST'])
@login_required
@handle_exceptions
def approve_application(application_id):
    """
    Одобряет заявку.
    """
    # Проверяем, является ли пользователь администратором
    if not current_user.is_admin:
        raise UnauthorizedError("Доступ запрещен")

    # Получаем заявку из базы данных
    application = Application.query.get_or_404(application_id)

    # Проверяем, не обработана ли уже заявка
    if application.status != 'pending':
        raise InvalidRequestError("Заявка уже обработана")

    # Одобряем заявку
    application.approve(current_user.id)

    # Сохраняем изменения
    db.session.commit()

    return jsonify({
        'success': True,
        'message': 'Заявка успешно одобрена',
        'application': application.to_dict(),
        'timestamp': datetime.now().isoformat()
    })

@api.route('/applications/<int:application_id>/reject', methods=['POST'])
@login_required
@handle_exceptions
def reject_application(application_id):
    """
    Отклоняет заявку.
    """
    # Проверяем, является ли пользователь администратором
    if not current_user.is_admin:
        raise UnauthorizedError("Доступ запрещен")

    # Получаем заявку из базы данных
    application = Application.query.get_or_404(application_id)

    # Проверяем, не обработана ли уже заявка
    if application.status != 'pending':
        raise InvalidRequestError("Заявка уже обработана")

    # Получаем причину отклонения из запроса
    data = request.get_json()
    reason = data.get('reason', '')

    # Отклоняем заявку
    application.reject(current_user.id)
    application.rejection_reason = reason

    # Сохраняем изменения
    db.session.commit()

    return jsonify({
        'success': True,
        'message': 'Заявка успешно отклонена',
        'application': application.to_dict(),
        'timestamp': datetime.now().isoformat()
    })

# API для синхронизации с Telegram ботом

@api.route('/telegram/sync/user', methods=['POST'])
@require_api_signature
@handle_exceptions
def sync_telegram_user():
    """
    Синхронизирует пользователя из Telegram бота.
    """
    data = request.get_json()

    if not data or 'telegram_id' not in data:
        raise InvalidRequestError("Отсутствует telegram_id")

    user = telegram_sync.sync_user_from_telegram(data)

    if user:
        return jsonify({
            'success': True,
            'message': 'Пользователь успешно синхронизирован',
            'user_id': user.id,
            'telegram_id': user.telegram_id,
            'timestamp': datetime.now().isoformat()
        })
    else:
        return jsonify({
            'success': False,
            'message': 'Ошибка при синхронизации пользователя',
            'timestamp': datetime.now().isoformat()
        }), 400

@api.route('/telegram/sync/application', methods=['POST'])
@require_api_signature
@handle_exceptions
def sync_telegram_application():
    """
    Синхронизирует заявку из Telegram бота.
    """
    data = request.get_json()

    required_fields = ['telegram_user_id', 'minecraft_nickname']
    if not data or not all(field in data for field in required_fields):
        raise InvalidRequestError(f"Отсутствуют обязательные поля: {required_fields}")

    application = telegram_sync.sync_application_from_telegram(data)

    if application:
        return jsonify({
            'success': True,
            'message': 'Заявка успешно синхронизирована',
            'application_id': application.id,
            'timestamp': datetime.now().isoformat()
        })
    else:
        return jsonify({
            'success': False,
            'message': 'Ошибка при синхронизации заявки',
            'timestamp': datetime.now().isoformat()
        }), 400

@api.route('/telegram/link', methods=['POST'])
@require_api_signature
@handle_exceptions
def link_telegram_account():
    """
    Связывает Telegram аккаунт с веб-пользователем.
    """
    data = request.get_json()

    required_fields = ['telegram_id', 'web_user_id']
    if not data or not all(field in data for field in required_fields):
        raise InvalidRequestError(f"Отсутствуют обязательные поля: {required_fields}")

    success = telegram_sync.link_telegram_to_web_user(
        data['telegram_id'],
        data['web_user_id']
    )

    if success:
        return jsonify({
            'success': True,
            'message': 'Аккаунты успешно связаны',
            'timestamp': datetime.now().isoformat()
        })
    else:
        return jsonify({
            'success': False,
            'message': 'Ошибка при связывании аккаунтов',
            'timestamp': datetime.now().isoformat()
        }), 400

@api.route('/telegram/user/<int:telegram_id>/applications', methods=['GET'])
@require_api_signature
@handle_exceptions
def get_telegram_user_applications(telegram_id):
    """
    Получает заявки пользователя по Telegram ID.
    """
    applications = telegram_sync.get_user_applications(telegram_id)

    return jsonify({
        'success': True,
        'applications': applications,
        'count': len(applications),
        'timestamp': datetime.now().isoformat()
    })

@api.route('/telegram/applications/<int:application_id>/status', methods=['PUT'])
@require_api_signature
@handle_exceptions
def update_telegram_application_status(application_id):
    """
    Обновляет статус заявки.
    """
    data = request.get_json()

    if not data or 'status' not in data:
        raise InvalidRequestError("Отсутствует статус")

    success = telegram_sync.update_application_status(
        application_id=application_id,
        status=data['status'],
        processed_by=data.get('processed_by'),
        rejection_reason=data.get('rejection_reason')
    )

    if success:
        return jsonify({
            'success': True,
            'message': 'Статус заявки обновлен',
            'timestamp': datetime.now().isoformat()
        })
    else:
        return jsonify({
            'success': False,
            'message': 'Ошибка при обновлении статуса',
            'timestamp': datetime.now().isoformat()
        }), 400

@api.route('/telegram/applications/pending', methods=['GET'])
@require_api_signature
@handle_exceptions
def get_pending_telegram_applications():
    """
    Получает ожидающие рассмотрения заявки.
    """
    limit = request.args.get('limit', 50, type=int)
    applications = telegram_sync.get_pending_applications(limit)

    return jsonify({
        'success': True,
        'applications': applications,
        'count': len(applications),
        'timestamp': datetime.now().isoformat()
    })

@api.route('/telegram/statistics', methods=['GET'])
@require_api_signature
@handle_exceptions
def get_telegram_statistics():
    """
    Получает статистику заявок.
    """
    stats = telegram_sync.get_application_statistics()

    return jsonify({
        'success': True,
        'statistics': stats,
        'timestamp': datetime.now().isoformat()
    })

@api.route('/telegram/cleanup', methods=['POST'])
@require_api_signature
@handle_exceptions
def cleanup_telegram_data():
    """
    Очищает потерянные данные.
    """
    cleanup_stats = telegram_sync.cleanup_orphaned_data()

    return jsonify({
        'success': True,
        'message': 'Очистка данных завершена',
        'cleanup_stats': cleanup_stats,
        'timestamp': datetime.now().isoformat()
    })
