{% extends "base.html" %}

{% block title %}Подтверждение Email - PlayLand{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">Подтверждение Email адреса</h4>
                </div>
                <div class="card-body">
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    {% if verification_status == 'success' %}
                        <!-- Успешное подтверждение -->
                        <div class="text-center mb-4">
                            <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                            <h5 class="text-success">Email успешно подтвержден!</h5>
                            <p class="text-muted">Ваш email адрес был успешно подтвержден. Теперь вы можете пользоваться всеми функциями сайта.</p>
                        </div>

                        <div class="d-grid gap-2">
                            {% if current_user.is_authenticated %}
                                <a href="{{ url_for('profile.profile') }}" class="btn btn-primary">
                                    <i class="fas fa-user"></i> Перейти в профиль
                                </a>
                            {% else %}
                                <a href="{{ url_for('auth.enhanced_login') }}" class="btn btn-primary">
                                    <i class="fas fa-sign-in-alt"></i> Войти в аккаунт
                                </a>
                            {% endif %}
                            <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-home"></i> На главную
                            </a>
                        </div>

                    {% elif verification_status == 'expired' %}
                        <!-- Токен истек -->
                        <div class="text-center mb-4">
                            <i class="fas fa-clock fa-4x text-warning mb-3"></i>
                            <h5 class="text-warning">Ссылка устарела</h5>
                            <p class="text-muted">Срок действия ссылки для подтверждения истек. Запросите новую ссылку для подтверждения email.</p>
                        </div>

                        {% if current_user.is_authenticated and not current_user.email_verified %}
                            <form method="POST" action="{{ url_for('auth.resend_verification') }}">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-envelope"></i> Отправить новую ссылку
                                    </button>
                                    <a href="{{ url_for('profile.profile') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-user"></i> В профиль
                                    </a>
                                </div>
                            </form>
                        {% else %}
                            <div class="d-grid gap-2">
                                <a href="{{ url_for('auth.enhanced_login') }}" class="btn btn-primary">
                                    <i class="fas fa-sign-in-alt"></i> Войти в аккаунт
                                </a>
                                <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-home"></i> На главную
                                </a>
                            </div>
                        {% endif %}

                    {% elif verification_status == 'invalid' %}
                        <!-- Неверный токен -->
                        <div class="text-center mb-4">
                            <i class="fas fa-times-circle fa-4x text-danger mb-3"></i>
                            <h5 class="text-danger">Неверная ссылка</h5>
                            <p class="text-muted">Ссылка для подтверждения недействительна или уже была использована.</p>
                        </div>

                        <div class="d-grid gap-2">
                            <a href="{{ url_for('auth.enhanced_login') }}" class="btn btn-primary">
                                <i class="fas fa-sign-in-alt"></i> Войти в аккаунт
                            </a>
                            <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-home"></i> На главную
                            </a>
                        </div>

                    {% elif verification_status == 'already_verified' %}
                        <!-- Email уже подтвержден -->
                        <div class="text-center mb-4">
                            <i class="fas fa-info-circle fa-4x text-info mb-3"></i>
                            <h5 class="text-info">Email уже подтвержден</h5>
                            <p class="text-muted">Ваш email адрес уже был подтвержден ранее. Вы можете пользоваться всеми функциями сайта.</p>
                        </div>

                        <div class="d-grid gap-2">
                            {% if current_user.is_authenticated %}
                                <a href="{{ url_for('profile.profile') }}" class="btn btn-primary">
                                    <i class="fas fa-user"></i> Перейти в профиль
                                </a>
                            {% else %}
                                <a href="{{ url_for('auth.enhanced_login') }}" class="btn btn-primary">
                                    <i class="fas fa-sign-in-alt"></i> Войти в аккаунт
                                </a>
                            {% endif %}
                            <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-home"></i> На главную
                            </a>
                        </div>

                    {% else %}
                        <!-- Форма для отправки ссылки подтверждения -->
                        <div class="text-center mb-4">
                            <i class="fas fa-envelope fa-3x text-primary mb-3"></i>
                            <h5>Подтвердите ваш email</h5>
                            <p class="text-muted">Для завершения регистрации необходимо подтвердить ваш email адрес.</p>
                        </div>

                        {% if current_user.is_authenticated and not current_user.email_verified %}
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle"></i> Требуется подтверждение</h6>
                                <p class="mb-2">Ваш email <strong>{{ current_user.email }}</strong> еще не подтвержден.</p>
                                <p class="mb-0">Проверьте почту или запросите новую ссылку для подтверждения.</p>
                            </div>

                            <form method="POST" action="{{ url_for('auth.resend_verification') }}">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-envelope"></i> Отправить ссылку подтверждения
                                    </button>
                                    <a href="{{ url_for('profile.profile') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-user"></i> В профиль
                                    </a>
                                </div>
                            </form>
                        {% else %}
                            <div class="d-grid gap-2">
                                <a href="{{ url_for('auth.enhanced_register') }}" class="btn btn-primary">
                                    <i class="fas fa-user-plus"></i> Зарегистрироваться
                                </a>
                                <a href="{{ url_for('auth.enhanced_login') }}" class="btn btn-outline-primary">
                                    <i class="fas fa-sign-in-alt"></i> Войти
                                </a>
                            </div>
                        {% endif %}
                    {% endif %}

                    <hr>

                    <div class="mt-4">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> Информация:</h6>
                            <ul class="mb-0">
                                <li>Ссылка для подтверждения действительна в течение 24 часов</li>
                                <li>Если письмо не пришло, проверьте папку "Спам"</li>
                                <li>Можно запросить новую ссылку в любое время</li>
                                <li>После подтверждения email станут доступны все функции сайта</li>
                            </ul>
                        </div>
                    </div>

                    {% if current_user.is_authenticated and not current_user.email_verified %}
                        <div class="mt-3">
                            <div class="alert alert-secondary">
                                <h6><i class="fas fa-cog"></i> Альтернативные способы активации:</h6>
                                <p class="mb-2">Если у вас проблемы с email, вы можете активировать аккаунт через:</p>
                                <div class="d-flex gap-2">
                                    <a href="{{ url_for('auth.auth_telegram') }}" class="btn btn-sm btn-info">
                                        <i class="fab fa-telegram"></i> Telegram
                                    </a>
                                    <a href="{{ url_for('auth.auth_discord') }}" class="btn btn-sm btn-primary">
                                        <i class="fab fa-discord"></i> Discord
                                    </a>
                                </div>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Автоматическое скрытие уведомлений через 5 секунд
    const alerts = document.querySelectorAll('.alert-dismissible');
    alerts.forEach(alert => {
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });

    // Анимация иконок
    const icons = document.querySelectorAll('.fa-4x, .fa-3x');
    icons.forEach((icon, index) => {
        setTimeout(() => {
            icon.style.animation = 'bounceIn 0.6s ease-out';
        }, index * 200);
    });

    // Обработка отправки формы повторной верификации
    const resendForms = document.querySelectorAll('form[action*="resend_verification"]');
    resendForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Отправка...';
                
                // Восстанавливаем кнопку через 10 секунд
                setTimeout(() => {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="fas fa-envelope"></i> Отправить ссылку подтверждения';
                }, 10000);
            }
        });
    });
});
</script>

<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.alert {
    border-radius: 0.375rem;
}

.btn {
    border-radius: 0.375rem;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.alert-secondary {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    color: #6c757d;
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.fa-check-circle {
    color: #28a745 !important;
}

.fa-times-circle {
    color: #dc3545 !important;
}

.fa-clock {
    color: #ffc107 !important;
}

.fa-info-circle {
    color: #17a2b8 !important;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    color: #0056b3;
    text-decoration: underline;
}
</style>
{% endblock %}
