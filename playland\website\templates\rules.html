{% extends "base.html" %}

{% block title %}PLAYLAND - Правила сервера{% endblock %}

{% block namespace %}rules{% endblock %}

{% block extra_head %}
<style>
    .hero {
        background-image: url("{{ url_for('static', filename='images/grass_background.jpg') }}");
        background-size: cover;
        background-position: center center;
        background-attachment: fixed;
        height: 50vh;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        padding: 20px;
        padding-top: 100px;
        box-sizing: border-box;
        position: relative;
        overflow: hidden;
    }
    
    .hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.6);
        backdrop-filter: blur(2px);
        z-index: 1;
    }
    
    .hero-content {
        position: relative;
        z-index: 2;
        max-width: 900px;
        width: 100%;
    }
    
    .hero h1 {
        font-family: 'Press Start 2P', cursive;
        font-size: 3.5em;
        color: #00ff00;
        text-shadow: 2px 2px 0 #000000, 0 0 15px #00ff00;
        margin-bottom: 30px;
        line-height: 1.3;
        animation: textPopIn 0.8s cubic-bezier(0.68, -0.55, 0.27, 1.55) 0.5s forwards, heroGlow 2.5s infinite alternate 1s;
        opacity: 0;
        transform: scale(0.5);
    }
    
    @keyframes textPopIn {
        to {
            opacity: 1;
            transform: scale(1);
        }
    }
    
    @keyframes heroGlow {
        0% {
            text-shadow: 2px 2px 0 #000000, 0 0 5px rgba(0, 255, 0, 0.5); 
        }
        100% {
            text-shadow: 2px 2px 0 #000000, 0 0 15px #00ff00, 0 0 30px #00ff00, 0 0 40px #00ff00;
        }
    }

    .content-section {
        padding: 80px 20px;
        background-color: rgba(0, 0, 0, 0.8);
    }
    
    .content-section h2 {
        font-family: 'Press Start 2P', cursive;
        font-size: 2.2em;
        color: #00ff00;
        text-align: center;
        margin-bottom: 40px;
        text-shadow: 0 0 10px #00ff00;
    }
    
    .rules-container {
        max-width: 900px;
        margin: 0 auto;
        background-color: rgba(0, 0, 0, 0.5);
        border: 2px solid #00ff00;
        border-radius: 10px;
        padding: 40px;
        box-shadow: 0 0 20px rgba(0, 255, 0, 0.2);
    }
    
    .rule-category {
        margin-bottom: 50px;
    }
    
    .rule-category:last-child {
        margin-bottom: 0;
    }
    
    .category-title {
        font-family: 'Press Start 2P', cursive;
        font-size: 1.5em;
        color: #00ff00;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid rgba(0, 255, 0, 0.3);
    }
    
    .rules-list {
        list-style: none;
        padding: 0;
    }
    
    .rules-list li {
        margin-bottom: 15px;
        padding-left: 30px;
        position: relative;
        color: #f0f0f0;
        line-height: 1.6;
    }
    
    .rules-list li:before {
        content: '';
        position: absolute;
        left: 0;
        top: 8px;
        width: 10px;
        height: 10px;
        background-color: #00ff00;
        border-radius: 2px;
        box-shadow: 0 0 5px #00ff00;
    }
    
    .important-note {
        margin-top: 40px;
        padding: 20px;
        background-color: rgba(255, 255, 0, 0.1);
        border-left: 4px solid #ffff00;
        color: #ffff00;
    }
    
    .important-note h3 {
        font-family: 'Press Start 2P', cursive;
        font-size: 1.2em;
        margin-bottom: 15px;
    }
    
    .acceptance-section {
        margin-top: 60px;
        text-align: center;
    }
    
    .btn-minecraft {
        display: inline-block;
        padding: 18px 35px;
        background-color: #00ff00;
        color: #000;
        text-decoration: none;
        font-family: 'Press Start 2P', cursive;
        font-size: 1em;
        text-transform: uppercase;
        border: none;
        position: relative;
        overflow: hidden;
        transition: all 0.3s;
        box-shadow: 0 4px 0 #006400, 0 0 20px rgba(0, 255, 0, 0.4);
        border-radius: 3px;
    }

    .btn-minecraft:hover {
        transform: translateY(-5px);
        box-shadow: 0 9px 0 #006400, 0 0 30px rgba(0, 255, 0, 0.6);
    }

    @media (max-width: 768px) {
        .hero h1 {
            font-size: 2.2em;
        }
        
        .content-section h2 {
            font-size: 1.8em;
        }
        
        .rules-container {
            padding: 20px;
        }
        
        .category-title {
            font-size: 1.2em;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="hero">
    <div class="particles-container" id="particles"></div>
    <div class="hero-content">
        <h1 class="animate-fade-in-up">ПРАВИЛА СЕРВЕРА</h1>
    </div>
</section>

<section class="content-section">
    <h2 class="animate-fade-in">Для комфортной игры на нашем сервере</h2>
    
    <div class="rules-container animate-fade-in-up">
        <div class="rule-category">
            <h3 class="category-title">1. Основные правила</h3>
            <ul class="rules-list">
                <li>Уважайте других игроков и администрацию сервера.</li>
                <li>Запрещено использование читов, хаков и любых модификаций, дающих преимущество в игре.</li>
                <li>Запрещен гриферинг чужих построек и кража ресурсов.</li>
                <li>Мат и оскорбления в чате запрещены.</li>
                <li>Запрещено распространение ложной информации о сервере или его игроках.</li>
                <li>Спам и флуд в чате запрещены.</li>
            </ul>
        </div>
        
        <div class="rule-category">
            <h3 class="category-title">2. Правила строительства</h3>
            <ul class="rules-list">
                <li>Минимальное расстояние между игроками - 100 блоков.</li>
                <li>Запрещено строить слишком близко к спавну без разрешения администрации.</li>
                <li>Запрещено создавать лаговые машины и механизмы, нагружающие сервер.</li>
                <li>Запрещено порчить ландшафт - заполняйте ямы и срубайте плавающие деревья.</li>
                <li>Не рекомендуется строить огромные однотипные фермы без согласования с администрацией.</li>
            </ul>
        </div>
        
        <div class="rule-category">
            <h3 class="category-title">3. Чат и коммуникация</h3>
            <ul class="rules-list">
                <li>Запрещено оскорблять других игроков и администрацию.</li>
                <li>Запрещена политическая агитация и религиозная пропаганда.</li>
                <li>Запрещено обсуждение нелегальных тем.</li>
                <li>Запрещена реклама других серверов и ресурсов.</li>
                <li>Используйте знаки препинания и избегайте написания текста CAPS LOCK.</li>
            </ul>
        </div>
        
        <div class="rule-category">
            <h3 class="category-title">4. Экономика и торговля</h3>
            <ul class="rules-list">
                <li>Запрещено мошенничество при торговле с игроками.</li>
                <li>Запрещено использование багов и эксплойтов экономики.</li>
                <li>Старайтесь устанавливать адекватные цены на товары.</li>
                <li>При возникновении споров - обращайтесь к администрации.</li>
            </ul>
        </div>
        
        <div class="important-note">
            <h3>Важно!</h3>
            <p>Незнание правил не освобождает от ответственности. Администрация оставляет за собой право выдавать наказания на своё усмотрение в зависимости от тяжести нарушения и истории игрока.</p>
            <p>Правила могут дополняться и изменяться - следите за объявлениями в Discord сервере.</p>
        </div>
        
        <div class="acceptance-section">
            <p>Подавая заявку на сервер, вы автоматически соглашаетесь с данными правилами.</p>
            <a href="{{ url_for('application') }}" class="btn-minecraft">Подать заявку</a>
        </div>
    </div>
</section>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const particlesContainer = document.getElementById('particles');
        if (!particlesContainer) return;
        
        const colors = [
            'rgba(0, 255, 0, 0.4)',
            'rgba(50, 205, 50, 0.4)',
            'rgba(173, 255, 47, 0.4)',
            'rgba(152, 251, 152, 0.4)'
        ];
        
        for (let i = 0; i < 50; i++) {
            const size = Math.random() * 6 + 3;
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.width = `${size}px`;
            particle.style.height = `${size}px`;
            particle.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
            particle.style.left = `${Math.random() * 100}%`;
            particle.style.setProperty('--rand-x', Math.random());
            particle.style.boxShadow = `0 0 ${size}px ${particle.style.backgroundColor}`;
            particle.style.animationDuration = `${Math.random() * 20 + 10}s`;
            particle.style.animationDelay = `${Math.random() * 5}s`;
            
            particlesContainer.appendChild(particle);
        }
    });
</script>
{% endblock %} 