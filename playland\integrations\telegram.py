#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Telegram интеграция для PlayLand.
Обеспечивает синхронизацию между веб-сайтом и Telegram ботом.
"""

import logging
import requests
import hmac
import hashlib
from typing import Dict, Any, Optional
from datetime import datetime, timezone
from flask import current_app

logger = logging.getLogger(__name__)

class TelegramIntegration:
    """Класс для интеграции с Telegram ботом"""
    
    def __init__(self, app=None):
        self.app = app
        self.bot_token = None
        self.webhook_url = None
        self.admin_chat_ids = []
        self.enabled = False
        
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """Инициализация с Flask приложением"""
        self.app = app
        self.bot_token = app.config.get('TELEGRAM_BOT_TOKEN')
        self.webhook_url = app.config.get('TELEGRAM_WEBHOOK_URL')
        self.admin_chat_ids = app.config.get('TELEGRAM_ADMIN_CHAT_IDS', [])
        
        if self.bot_token:
            self.enabled = True
            logger.info("Telegram integration enabled")
        else:
            logger.warning("Telegram bot token not configured")
    
    def send_message(self, chat_id: int, text: str, parse_mode: str = 'HTML') -> bool:
        """Отправка сообщения в Telegram"""
        if not self.enabled:
            logger.warning("Telegram integration disabled")
            return False
        
        try:
            url = f"https://api.telegram.org/bot{self.bot_token}/sendMessage"
            data = {
                'chat_id': chat_id,
                'text': text,
                'parse_mode': parse_mode
            }
            
            response = requests.post(url, json=data, timeout=10)
            response.raise_for_status()
            
            logger.info(f"Message sent to chat {chat_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send Telegram message: {e}")
            return False
    
    def send_admin_notification(self, text: str) -> bool:
        """Отправка уведомления всем администраторам"""
        if not self.admin_chat_ids:
            logger.warning("No admin chat IDs configured")
            return False
        
        success_count = 0
        for chat_id in self.admin_chat_ids:
            if self.send_message(chat_id, text):
                success_count += 1
        
        return success_count > 0
    
    def send_application_notification(self, application_data: Dict[str, Any]) -> bool:
        """Отправка уведомления о новой заявке"""
        try:
            text = f"""
🎮 <b>Новая заявка на сервер!</b>

👤 <b>Никнейм:</b> <code>{application_data.get('minecraft_nickname', 'Не указан')}</code>
🔖 <b>Discord:</b> <code>{application_data.get('discord_tag', 'Не указан')}</code>
📧 <b>Email:</b> <code>{application_data.get('email', 'Не указан')}</code>
👶 <b>Возраст:</b> {application_data.get('age', 'Не указан')}

📝 <b>О себе:</b>
{application_data.get('about_yourself', 'Не указано')[:200]}...

🔍 <b>Как нашел сервер:</b>
{application_data.get('find_server', 'Не указано')}

⏰ <b>Время подачи:</b> {datetime.now(timezone.utc).strftime('%d.%m.%Y %H:%M UTC')}
            """
            
            return self.send_admin_notification(text)
            
        except Exception as e:
            logger.error(f"Failed to send application notification: {e}")
            return False
    
    def send_team_application_notification(self, application_data: Dict[str, Any]) -> bool:
        """Отправка уведомления о заявке в команду"""
        try:
            text = f"""
📧 <b>Новая заявка в команду!</b>

👤 <b>Никнейм:</b> <code>{application_data.get('minecraft_nickname', 'Не указан')}</code>
🔖 <b>Discord:</b> <code>{application_data.get('discord_tag', 'Не указан')}</code>
📧 <b>Email:</b> <code>{application_data.get('email', 'Не указан')}</code>
👶 <b>Возраст:</b> {application_data.get('age', 'Не указан')}
🎯 <b>Должность:</b> {application_data.get('position', 'Не указана')}

💼 <b>Опыт:</b>
{application_data.get('experience', 'Не указан')[:200]}...

💡 <b>Мотивация:</b>
{application_data.get('motivation', 'Не указана')[:200]}...

⏰ <b>Время подачи:</b> {datetime.now(timezone.utc).strftime('%d.%m.%Y %H:%M UTC')}
            """
            
            return self.send_admin_notification(text)
            
        except Exception as e:
            logger.error(f"Failed to send team application notification: {e}")
            return False
    
    def sync_application_status(self, application_id: int, status: str, 
                              rejection_reason: Optional[str] = None) -> bool:
        """Синхронизация статуса заявки с ботом"""
        try:
            # Здесь будет API вызов к боту для обновления статуса
            # Пока что просто логируем
            logger.info(f"Syncing application {application_id} status: {status}")
            
            if rejection_reason:
                logger.info(f"Rejection reason: {rejection_reason}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to sync application status: {e}")
            return False
    
    def verify_webhook_signature(self, data: str, signature: str) -> bool:
        """Проверка подписи webhook от Telegram"""
        if not self.bot_token:
            return False
        
        try:
            expected_signature = hmac.new(
                self.bot_token.encode(),
                data.encode(),
                hashlib.sha256
            ).hexdigest()
            
            return hmac.compare_digest(signature, expected_signature)
            
        except Exception as e:
            logger.error(f"Failed to verify webhook signature: {e}")
            return False
    
    def get_bot_info(self) -> Optional[Dict[str, Any]]:
        """Получение информации о боте"""
        if not self.enabled:
            return None
        
        try:
            url = f"https://api.telegram.org/bot{self.bot_token}/getMe"
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            
            return response.json().get('result')
            
        except Exception as e:
            logger.error(f"Failed to get bot info: {e}")
            return None

# Глобальный экземпляр интеграции
telegram_integration = TelegramIntegration()

def init_telegram_integration(app):
    """Инициализация Telegram интеграции"""
    telegram_integration.init_app(app)
    
    # Добавляем маршруты для webhook
    @app.route('/api/telegram/webhook', methods=['POST'])
    def telegram_webhook():
        """Webhook для получения обновлений от Telegram бота"""
        from flask import request, jsonify
        
        try:
            # Проверяем подпись
            signature = request.headers.get('X-Telegram-Bot-Api-Secret-Token')
            if not telegram_integration.verify_webhook_signature(
                request.get_data(as_text=True), signature or ''
            ):
                return jsonify({'error': 'Invalid signature'}), 401
            
            # Обрабатываем обновление
            update = request.get_json()
            logger.info(f"Received Telegram update: {update}")
            
            return jsonify({'status': 'ok'})
            
        except Exception as e:
            logger.error(f"Telegram webhook error: {e}")
            return jsonify({'error': 'Internal error'}), 500
    
    logger.info("Telegram integration initialized successfully")

# Функции для использования в других модулях
def send_application_notification(application_data: Dict[str, Any]) -> bool:
    """Отправка уведомления о новой заявке"""
    return telegram_integration.send_application_notification(application_data)

def send_team_application_notification(application_data: Dict[str, Any]) -> bool:
    """Отправка уведомления о заявке в команду"""
    return telegram_integration.send_team_application_notification(application_data)
