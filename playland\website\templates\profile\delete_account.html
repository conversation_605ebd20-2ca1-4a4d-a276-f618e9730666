{% extends "base.html" %}

{% block title %}Удаление аккаунта - PlayLand{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Удаление аккаунта</h5>
                </div>
                <div class="card-body">
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    <div class="alert alert-danger">
                        <h5><i class="fas fa-skull-crossbones"></i> ВНИМАНИЕ! Это действие необратимо!</h5>
                        <p class="mb-0">
                            Удаление аккаунта приведет к полной потере всех ваших данных, включая:
                        </p>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6><i class="fas fa-times-circle text-danger"></i> Что будет удалено:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-user text-danger"></i> Профиль пользователя</li>
                                <li><i class="fas fa-envelope text-danger"></i> Email и контактная информация</li>
                                <li><i class="fas fa-file-alt text-danger"></i> Все заявки и тикеты</li>
                                <li><i class="fas fa-comments text-danger"></i> История сообщений</li>
                                <li><i class="fas fa-cog text-danger"></i> Настройки и предпочтения</li>
                                <li><i class="fas fa-shield-alt text-danger"></i> Настройки безопасности и 2FA</li>
                                <li><i class="fas fa-link text-danger"></i> Связанные аккаунты (Telegram, Discord)</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-info-circle text-info"></i> Альтернативы:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-pause text-warning"></i> Временная деактивация</li>
                                <li><i class="fas fa-edit text-info"></i> Изменение настроек приватности</li>
                                <li><i class="fas fa-envelope text-info"></i> Отключение уведомлений</li>
                                <li><i class="fas fa-user-slash text-warning"></i> Смена имени пользователя</li>
                            </ul>
                            <p class="text-muted">
                                Если у вас есть проблемы с аккаунтом, рассмотрите эти варианты или 
                                <a href="{{ url_for('profile.create_ticket') }}">создайте тикет</a> для получения помощи.
                            </p>
                        </div>
                    </div>

                    <div class="alert alert-warning">
                        <h6><i class="fas fa-clock"></i> Период ожидания</h6>
                        <p class="mb-0">
                            После подтверждения удаления у вас будет 7 дней для отмены этого действия. 
                            По истечении этого срока аккаунт будет удален безвозвратно.
                        </p>
                    </div>

                    <hr>

                    <form method="POST" action="{{ url_for('profile.delete_account') }}" id="deleteAccountForm">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-3">
                            {{ form.password.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.password(class="form-control", id="password", placeholder="Введите ваш текущий пароль") }}
                                <button type="button" class="btn btn-outline-secondary" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            {% if form.password.errors %}
                                <div class="text-danger">
                                    {% for error in form.password.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            {{ form.confirmation.label(class="form-label") }}
                            {{ form.confirmation(class="form-control", id="confirmation", placeholder="УДАЛИТЬ") }}
                            {% if form.confirmation.errors %}
                                <div class="text-danger">
                                    {% for error in form.confirmation.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">
                                Введите слово "УДАЛИТЬ" (заглавными буквами) для подтверждения
                            </small>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="finalConfirmation" required>
                            <label class="form-check-label" for="finalConfirmation">
                                Я понимаю, что это действие необратимо и приведет к полной потере всех моих данных
                            </label>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('profile.security_settings') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Отмена
                            </a>
                            
                            <button type="button" class="btn btn-danger" id="deleteBtn" disabled data-bs-toggle="modal" data-bs-target="#confirmDeleteModal">
                                <i class="fas fa-trash"></i> Удалить аккаунт
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Модальное окно финального подтверждения -->
<div class="modal fade" id="confirmDeleteModal" tabindex="-1" aria-labelledby="confirmDeleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-danger">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="confirmDeleteModalLabel">
                    <i class="fas fa-exclamation-triangle"></i> Финальное подтверждение
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <i class="fas fa-skull-crossbones fa-3x text-danger"></i>
                </div>
                <h6 class="text-center">Вы действительно хотите удалить свой аккаунт?</h6>
                <p class="text-center text-muted">
                    Пользователь: <strong>{{ current_user.username }}</strong><br>
                    Email: <strong>{{ current_user.email }}</strong>
                </p>
                <div class="alert alert-danger">
                    <strong>Последнее предупреждение:</strong> Все ваши данные будут безвозвратно удалены через 7 дней.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i> Отмена
                </button>
                <button type="button" class="btn btn-danger" id="finalDeleteBtn">
                    <i class="fas fa-trash"></i> Да, удалить аккаунт
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const passwordInput = document.getElementById('password');
    const confirmationInput = document.getElementById('confirmation');
    const finalConfirmationCheckbox = document.getElementById('finalConfirmation');
    const deleteBtn = document.getElementById('deleteBtn');
    const finalDeleteBtn = document.getElementById('finalDeleteBtn');
    const togglePasswordBtn = document.getElementById('togglePassword');
    const deleteForm = document.getElementById('deleteAccountForm');

    // Показать/скрыть пароль
    togglePasswordBtn.addEventListener('click', function() {
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            this.innerHTML = '<i class="fas fa-eye-slash"></i>';
        } else {
            passwordInput.type = 'password';
            this.innerHTML = '<i class="fas fa-eye"></i>';
        }
    });

    // Проверка валидности формы
    function checkFormValidity() {
        const passwordValid = passwordInput.value.length > 0;
        const confirmationValid = confirmationInput.value.toUpperCase() === 'УДАЛИТЬ';
        const finalConfirmed = finalConfirmationCheckbox.checked;
        
        const formValid = passwordValid && confirmationValid && finalConfirmed;
        deleteBtn.disabled = !formValid;
        
        // Обновляем стиль кнопки
        if (formValid) {
            deleteBtn.classList.remove('btn-secondary');
            deleteBtn.classList.add('btn-danger');
        } else {
            deleteBtn.classList.remove('btn-danger');
            deleteBtn.classList.add('btn-secondary');
        }
    }

    // Слушатели событий для проверки валидности
    passwordInput.addEventListener('input', checkFormValidity);
    confirmationInput.addEventListener('input', checkFormValidity);
    finalConfirmationCheckbox.addEventListener('change', checkFormValidity);

    // Финальное подтверждение удаления
    finalDeleteBtn.addEventListener('click', function() {
        // Показываем индикатор загрузки
        this.disabled = true;
        this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Удаление...';
        
        // Отправляем форму
        deleteForm.submit();
    });

    // Предупреждение при попытке покинуть страницу
    let formChanged = false;
    
    [passwordInput, confirmationInput, finalConfirmationCheckbox].forEach(element => {
        element.addEventListener('change', () => {
            formChanged = true;
        });
    });

    window.addEventListener('beforeunload', function(e) {
        if (formChanged && (passwordInput.value || confirmationInput.value || finalConfirmationCheckbox.checked)) {
            e.preventDefault();
            e.returnValue = '';
        }
    });

    // Анимация предупреждающих элементов
    const dangerElements = document.querySelectorAll('.alert-danger, .text-danger, .border-danger');
    dangerElements.forEach((element, index) => {
        setTimeout(() => {
            element.style.animation = 'pulse 2s infinite';
        }, index * 200);
    });
});
</script>

<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.alert {
    border-radius: 0.375rem;
}

.btn {
    border-radius: 0.375rem;
}

.modal-content {
    border-radius: 0.5rem;
}

.form-control:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
    }
}

.list-unstyled li {
    padding: 0.25rem 0;
}

.input-group .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.input-group .form-control {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.bg-danger {
    background-color: #dc3545 !important;
}

.border-danger {
    border-color: #dc3545 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.btn-close-white {
    filter: invert(1) grayscale(100%) brightness(200%);
}
</style>
{% endblock %}
