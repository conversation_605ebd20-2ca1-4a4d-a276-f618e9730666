#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Административная панель для мониторинга безопасности PlayLand.
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from datetime import datetime, timedelta
from sqlalchemy import func, desc, and_, or_
import json

# Импорты моделей и утилит
from playland.website.models import (
    db, User, AuditLog, LoginHistory, TrustedDevice
)
from playland.website.security import log_security_event, SecurityValidator
from playland.website.email_service import email_service

# Создаем Blueprint
admin_security_bp = Blueprint('admin_security', __name__, url_prefix='/admin/security')

def admin_required(f):
    """Декоратор для проверки прав администратора"""
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin:
            flash('Доступ запрещен. Требуются права администратора.', 'error')
            return redirect(url_for('index'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

@admin_security_bp.route('/')
@login_required
@admin_required
def dashboard():
    """Главная страница панели безопасности"""
    # Статистика за последние 24 часа
    last_24h = datetime.now(datetime.timezone.utc) - timedelta(hours=24)
    
    # Общая статистика
    stats = {
        'total_users': User.query.count(),
        'active_users_24h': User.query.filter(User.last_seen >= last_24h).count(),
        'failed_logins_24h': LoginHistory.query.filter(
            LoginHistory.success == False,
            LoginHistory.timestamp >= last_24h
        ).count(),
        'successful_logins_24h': LoginHistory.query.filter(
            LoginHistory.success == True,
            LoginHistory.timestamp >= last_24h
        ).count(),
        'locked_accounts': User.query.filter(
            User.account_locked_until > datetime.now(datetime.timezone.utc)
        ).count(),
        'unverified_emails': User.query.filter(User.email_verified == False).count(),
        'two_factor_enabled': User.query.filter(User.two_factor_enabled == True).count(),
        'trusted_devices': TrustedDevice.query.filter(TrustedDevice.is_active == True).count()
    }
    
    # Последние события безопасности
    recent_events = AuditLog.query.filter(
        AuditLog.action.in_(['login', 'failed_login', 'password_changed', '2fa_enabled', '2fa_disabled', 'account_locked'])
    ).order_by(desc(AuditLog.timestamp)).limit(20).all()
    
    # Подозрительная активность
    suspicious_ips = db.session.query(
        LoginHistory.ip_address,
        func.count(LoginHistory.id).label('failed_attempts')
    ).filter(
        LoginHistory.success == False,
        LoginHistory.timestamp >= last_24h
    ).group_by(LoginHistory.ip_address).having(
        func.count(LoginHistory.id) >= 5
    ).order_by(desc('failed_attempts')).limit(10).all()
    
    # Новые устройства
    new_devices = TrustedDevice.query.filter(
        TrustedDevice.created_at >= last_24h
    ).order_by(desc(TrustedDevice.created_at)).limit(10).all()
    
    return render_template('admin/security_dashboard.html',
                         stats=stats,
                         recent_events=recent_events,
                         suspicious_ips=suspicious_ips,
                         new_devices=new_devices)

@admin_security_bp.route('/users')
@login_required
@admin_required
def users():
    """Управление пользователями"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    filter_type = request.args.get('filter', 'all')
    
    query = User.query
    
    # Поиск
    if search:
        query = query.filter(
            or_(
                User.username.contains(search),
                User.email.contains(search),
                User.nickname.contains(search)
            )
        )
    
    # Фильтры
    if filter_type == 'locked':
        query = query.filter(User.account_locked_until > datetime.now(datetime.timezone.utc))
    elif filter_type == 'unverified':
        query = query.filter(User.email_verified == False)
    elif filter_type == '2fa_enabled':
        query = query.filter(User.two_factor_enabled == True)
    elif filter_type == 'suspicious':
        # Пользователи с большим количеством неудачных попыток входа
        suspicious_user_ids = db.session.query(LoginHistory.user_id).filter(
            LoginHistory.success == False,
            LoginHistory.timestamp >= datetime.now(datetime.timezone.utc) - timedelta(days=7)
        ).group_by(LoginHistory.user_id).having(
            func.count(LoginHistory.id) >= 10
        ).subquery()
        query = query.filter(User.id.in_(suspicious_user_ids))
    
    users = query.order_by(desc(User.created_at)).paginate(
        page=page, per_page=50, error_out=False
    )
    
    return render_template('admin/security_users.html',
                         users=users,
                         search=search,
                         filter_type=filter_type)

@admin_security_bp.route('/user/<int:user_id>')
@login_required
@admin_required
def user_detail(user_id):
    """Детальная информация о пользователе"""
    user = User.query.get_or_404(user_id)
    
    # История входов
    login_history = user.login_history.order_by(desc(LoginHistory.timestamp)).limit(50).all()
    
    # Доверенные устройства
    trusted_devices = user.get_active_trusted_devices()
    
    # Последние события безопасности
    security_events = AuditLog.query.filter_by(user_id=user_id).order_by(
        desc(AuditLog.timestamp)
    ).limit(20).all()
    
    # Статистика
    stats = {
        'total_logins': user.login_history.filter_by(success=True).count(),
        'failed_logins': user.login_history.filter_by(success=False).count(),
        'trusted_devices_count': len(trusted_devices),
        'last_login': user.login_history.filter_by(success=True).order_by(
            desc(LoginHistory.timestamp)
        ).first(),
        'account_age': (datetime.now(datetime.timezone.utc) - user.created_at).days
    }
    
    return render_template('admin/security_user_detail.html',
                         user=user,
                         login_history=login_history,
                         trusted_devices=trusted_devices,
                         security_events=security_events,
                         stats=stats)

@admin_security_bp.route('/user/<int:user_id>/lock', methods=['POST'])
@login_required
@admin_required
def lock_user(user_id):
    """Блокировка пользователя"""
    user = User.query.get_or_404(user_id)
    duration = request.json.get('duration', 60)  # минуты
    reason = request.json.get('reason', 'Заблокирован администратором')
    
    user.lock_account(duration)
    db.session.commit()
    
    # Логируем действие
    log_security_event(
        event_type='admin_lock_user',
        description=f'Администратор {current_user.username} заблокировал пользователя {user.username}',
        user_id=current_user.id,
        severity='WARNING',
        details={'target_user_id': user_id, 'duration': duration, 'reason': reason}
    )
    
    # Отправляем уведомление пользователю
    if user.email_verified:
        email_service.send_security_alert_email(user, 'account_locked', {
            'reason': reason,
            'duration': f'{duration} минут',
            'admin': current_user.username
        })
    
    return jsonify({'success': True, 'message': f'Пользователь {user.username} заблокирован на {duration} минут'})

@admin_security_bp.route('/user/<int:user_id>/unlock', methods=['POST'])
@login_required
@admin_required
def unlock_user(user_id):
    """Разблокировка пользователя"""
    user = User.query.get_or_404(user_id)
    
    user.unlock_account()
    db.session.commit()
    
    # Логируем действие
    log_security_event(
        event_type='admin_unlock_user',
        description=f'Администратор {current_user.username} разблокировал пользователя {user.username}',
        user_id=current_user.id,
        severity='INFO',
        details={'target_user_id': user_id}
    )
    
    return jsonify({'success': True, 'message': f'Пользователь {user.username} разблокирован'})

@admin_security_bp.route('/user/<int:user_id>/disable_2fa', methods=['POST'])
@login_required
@admin_required
def disable_user_2fa(user_id):
    """Отключение 2FA для пользователя"""
    user = User.query.get_or_404(user_id)
    
    if user.two_factor_enabled:
        user.disable_two_factor()
        db.session.commit()
        
        # Логируем действие
        log_security_event(
            event_type='admin_disable_2fa',
            description=f'Администратор {current_user.username} отключил 2FA для пользователя {user.username}',
            user_id=current_user.id,
            severity='WARNING',
            details={'target_user_id': user_id}
        )
        
        # Отправляем уведомление пользователю
        if user.email_verified:
            email_service.send_security_alert_email(user, 'two_factor_disabled', {
                'admin': current_user.username,
                'reason': 'Отключено администратором'
            })
        
        return jsonify({'success': True, 'message': f'2FA отключена для пользователя {user.username}'})
    else:
        return jsonify({'success': False, 'message': 'У пользователя не включена 2FA'})

@admin_security_bp.route('/user/<int:user_id>/revoke_devices', methods=['POST'])
@login_required
@admin_required
def revoke_user_devices(user_id):
    """Отзыв всех доверенных устройств пользователя"""
    user = User.query.get_or_404(user_id)
    
    devices = user.get_active_trusted_devices()
    count = 0
    
    for device in devices:
        device.revoke()
        count += 1
    
    db.session.commit()
    
    # Логируем действие
    log_security_event(
        event_type='admin_revoke_devices',
        description=f'Администратор {current_user.username} отозвал {count} доверенных устройств пользователя {user.username}',
        user_id=current_user.id,
        severity='WARNING',
        details={'target_user_id': user_id, 'devices_count': count}
    )
    
    return jsonify({'success': True, 'message': f'Отозвано {count} доверенных устройств'})

@admin_security_bp.route('/logs')
@login_required
@admin_required
def security_logs():
    """Просмотр логов безопасности"""
    page = request.args.get('page', 1, type=int)
    event_type = request.args.get('event_type', '')
    user_search = request.args.get('user_search', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    
    query = AuditLog.query
    
    # Фильтры
    if event_type:
        query = query.filter(AuditLog.action == event_type)
    
    if user_search:
        user_ids = db.session.query(User.id).filter(
            or_(
                User.username.contains(user_search),
                User.email.contains(user_search)
            )
        ).subquery()
        query = query.filter(AuditLog.user_id.in_(user_ids))
    
    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(AuditLog.timestamp >= date_from_obj)
        except ValueError:
            pass
    
    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d') + timedelta(days=1)
            query = query.filter(AuditLog.timestamp < date_to_obj)
        except ValueError:
            pass
    
    logs = query.order_by(desc(AuditLog.timestamp)).paginate(
        page=page, per_page=100, error_out=False
    )
    
    # Типы событий для фильтра
    event_types = db.session.query(AuditLog.action).distinct().all()
    event_types = [et[0] for et in event_types]
    
    return render_template('admin/security_logs.html',
                         logs=logs,
                         event_types=event_types,
                         filters={
                             'event_type': event_type,
                             'user_search': user_search,
                             'date_from': date_from,
                             'date_to': date_to
                         })

@admin_security_bp.route('/api/stats')
@login_required
@admin_required
def api_stats():
    """API для получения статистики безопасности"""
    days = request.args.get('days', 7, type=int)
    since = datetime.now(datetime.timezone.utc) - timedelta(days=days)
    
    # Статистика по дням
    daily_stats = db.session.query(
        func.date(LoginHistory.timestamp).label('date'),
        func.count(LoginHistory.id).label('total_logins'),
        func.sum(func.case([(LoginHistory.success == True, 1)], else_=0)).label('successful_logins'),
        func.sum(func.case([(LoginHistory.success == False, 1)], else_=0)).label('failed_logins')
    ).filter(
        LoginHistory.timestamp >= since
    ).group_by(
        func.date(LoginHistory.timestamp)
    ).order_by('date').all()
    
    # Топ IP адресов с неудачными попытками
    top_failed_ips = db.session.query(
        LoginHistory.ip_address,
        func.count(LoginHistory.id).label('failed_count')
    ).filter(
        LoginHistory.success == False,
        LoginHistory.timestamp >= since
    ).group_by(
        LoginHistory.ip_address
    ).order_by(desc('failed_count')).limit(10).all()
    
    return jsonify({
        'daily_stats': [
            {
                'date': stat.date.isoformat(),
                'total_logins': stat.total_logins,
                'successful_logins': stat.successful_logins or 0,
                'failed_logins': stat.failed_logins or 0
            }
            for stat in daily_stats
        ],
        'top_failed_ips': [
            {
                'ip': ip,
                'failed_count': count
            }
            for ip, count in top_failed_ips
        ]
    })
