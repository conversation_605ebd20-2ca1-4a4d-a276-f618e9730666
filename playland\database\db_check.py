#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Скрипт для проверки базы данных SQLite.
"""

import os
import sqlite3
import sys

def check_db():
    """
    Проверяет базу данных SQLite.
    """
    # Проверка наличия файла базы данных
    db_path = 'playland.db'
    if not os.path.exists(db_path):
        print(f"Ошибка: файл базы данных {db_path} не найден.")
        return False
    
    try:
        # Подключение к базе данных
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Получение списка таблиц
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"База данных {db_path} содержит {len(tables)} таблиц:")
        for i, table in enumerate(tables, 1):
            print(f"{i}. {table[0]}")
        
        # Проверка наличия основных таблиц
        required_tables = [
            'users', 'roles', 'user_roles', 'applications', 'tickets', 
            'ticket_messages', 'server_events', 'transactions'
        ]
        
        missing_tables = [table for table in required_tables if (table,) not in tables]
        
        if missing_tables:
            print("\nВнимание: отсутствуют следующие таблицы:")
            for table in missing_tables:
                print(f"- {table}")
        else:
            print("\nВсе необходимые таблицы присутствуют в базе данных.")
        
        # Проверка наличия данных в таблицах
        print("\nПроверка наличия данных в таблицах:")
        for table in [t[0] for t in tables]:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"- {table}: {count} записей")
        
        # Закрытие соединения
        conn.close()
        
        return True
    
    except sqlite3.Error as e:
        print(f"Ошибка SQLite: {e}")
        return False
    except Exception as e:
        print(f"Ошибка: {e}")
        return False

if __name__ == '__main__':
    print("Проверка базы данных PlayLand...")
    if check_db():
        print("\nПроверка базы данных завершена успешно.")
    else:
        print("\nПроверка базы данных завершена с ошибками.")
        sys.exit(1)
