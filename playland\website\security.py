#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Модуль безопасности для веб-приложения PlayLand.
Содержит функции для валидации, санитизации, защиты от атак и управления сессиями.
"""

import re
import html
import secrets
import hashlib
import time
from datetime import datetime, timedelta
from functools import wraps
from flask import request, session, current_app, abort, jsonify, g
from flask_login import current_user
try:
    from werkzeug.security import safe_str_cmp
except ImportError:
    # В новых версиях werkzeug safe_str_cmp удален, используем hmac.compare_digest
    import hmac
    def safe_str_cmp(a, b):
        return hmac.compare_digest(str(a), str(b))
import bleach
from collections import defaultdict, deque

# Глобальные переменные для rate limiting
rate_limit_storage = defaultdict(lambda: defaultdict(deque))

class SecurityValidator:
    """Класс для валидации и санитизации входных данных"""
    
    # Разрешенные HTML теги для пользовательского контента
    ALLOWED_TAGS = ['b', 'i', 'u', 'em', 'strong', 'p', 'br', 'ul', 'ol', 'li']
    ALLOWED_ATTRIBUTES = {}
    
    @staticmethod
    def sanitize_html(text):
        """Санитизация HTML контента"""
        if not text:
            return ""
        return bleach.clean(text, tags=SecurityValidator.ALLOWED_TAGS, 
                          attributes=SecurityValidator.ALLOWED_ATTRIBUTES, strip=True)
    
    @staticmethod
    def validate_username(username):
        """Валидация имени пользователя"""
        if not username:
            return False, "Имя пользователя не может быть пустым"
        
        if len(username) < 3 or len(username) > 32:
            return False, "Имя пользователя должно содержать от 3 до 32 символов"
        
        # Проверка на допустимые символы
        if not re.match(r'^[a-zA-Z0-9_-]+$', username):
            return False, "Имя пользователя может содержать только буквы, цифры, дефис и подчеркивание"
        
        # Проверка на зарезервированные имена
        reserved_names = ['admin', 'administrator', 'root', 'system', 'api', 'www', 'mail', 'ftp']
        if username.lower() in reserved_names:
            return False, "Это имя пользователя зарезервировано"
        
        return True, ""
    
    @staticmethod
    def validate_email(email):
        """Валидация email адреса"""
        if not email:
            return False, "Email не может быть пустым"
        
        # Базовая проверка формата email
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            return False, "Некорректный формат email"
        
        if len(email) > 254:
            return False, "Email слишком длинный"
        
        return True, ""
    
    @staticmethod
    def validate_password(password):
        """Валидация пароля"""
        if not password:
            return False, "Пароль не может быть пустым"
        
        if len(password) < 8:
            return False, "Пароль должен содержать не менее 8 символов"
        
        if len(password) > 128:
            return False, "Пароль слишком длинный"
        
        # Проверка на наличие различных типов символов
        has_upper = any(c.isupper() for c in password)
        has_lower = any(c.islower() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)
        
        strength_score = sum([has_upper, has_lower, has_digit, has_special])
        
        if strength_score < 3:
            return False, "Пароль должен содержать как минимум 3 из 4 типов символов: заглавные буквы, строчные буквы, цифры, специальные символы"
        
        return True, ""
    
    @staticmethod
    def validate_minecraft_nickname(nickname):
        """Валидация Minecraft никнейма"""
        if not nickname:
            return False, "Никнейм не может быть пустым"
        
        if len(nickname) < 3 or len(nickname) > 16:
            return False, "Никнейм должен содержать от 3 до 16 символов"
        
        if not re.match(r'^[a-zA-Z0-9_]+$', nickname):
            return False, "Никнейм может содержать только буквы, цифры и подчеркивание"
        
        return True, ""

class RateLimiter:
    """Класс для ограничения частоты запросов"""
    
    @staticmethod
    def is_rate_limited(identifier, limit=10, window=60, action="default"):
        """
        Проверяет, превышен ли лимит запросов
        
        Args:
            identifier: Идентификатор (IP, user_id и т.д.)
            limit: Максимальное количество запросов
            window: Временное окно в секундах
            action: Тип действия для разных лимитов
        """
        current_time = time.time()
        key = f"{identifier}:{action}"
        
        # Очищаем старые записи
        while rate_limit_storage[key] and rate_limit_storage[key][0] < current_time - window:
            rate_limit_storage[key].popleft()
        
        # Проверяем лимит
        if len(rate_limit_storage[key]) >= limit:
            return True
        
        # Добавляем текущий запрос
        rate_limit_storage[key].append(current_time)
        return False

def rate_limit(limit=10, window=60, action="default", per="ip"):
    """
    Декоратор для ограничения частоты запросов
    
    Args:
        limit: Максимальное количество запросов
        window: Временное окно в секундах
        action: Тип действия
        per: По чему ограничивать ("ip", "user")
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if per == "ip":
                identifier = request.remote_addr
            elif per == "user" and current_user.is_authenticated:
                identifier = current_user.id
            else:
                identifier = request.remote_addr
            
            if RateLimiter.is_rate_limited(identifier, limit, window, action):
                # Логируем попытку превышения лимита
                current_app.logger.warning(f"Rate limit exceeded for {identifier} on action {action}")
                
                # Возвращаем ошибку
                if request.is_json:
                    return jsonify({"error": "Слишком много запросов. Попробуйте позже."}), 429
                else:
                    abort(429)
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_verified_email(f):
    """Декоратор, требующий подтвержденный email"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            abort(401)
        
        if not current_user.email_verified:
            if request.is_json:
                return jsonify({"error": "Требуется подтверждение email"}), 403
            else:
                return redirect(url_for('auth.verify_email'))
        
        return f(*args, **kwargs)
    return decorated_function

def csrf_protect():
    """Дополнительная CSRF защита для AJAX запросов"""
    if request.method in ['POST', 'PUT', 'DELETE', 'PATCH']:
        token = session.get('csrf_token')
        if not token or not safe_str_cmp(token, request.headers.get('X-CSRFToken', '')):
            abort(403)

def generate_csrf_token():
    """Генерирует CSRF токен"""
    if 'csrf_token' not in session:
        session['csrf_token'] = secrets.token_hex(16)
    return session['csrf_token']

def validate_session():
    """Валидация сессии пользователя"""
    if current_user.is_authenticated:
        # Проверяем, не истекла ли сессия
        if hasattr(current_user, 'session_expires') and current_user.session_expires:
            if datetime.now(datetime.timezone.utc) > current_user.session_expires:
                return False
        
        # Проверяем токен сессии
        if hasattr(current_user, 'session_token') and current_user.session_token:
            session_token = session.get('session_token')
            if not session_token or not safe_str_cmp(session_token, current_user.session_token):
                return False
    
    return True

class SecurityHeaders:
    """Класс для установки заголовков безопасности"""
    
    @staticmethod
    def set_security_headers(response):
        """Устанавливает заголовки безопасности"""
        # Content Security Policy
        csp = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' https://telegram.org https://discord.com; "
            "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; "
            "font-src 'self' https://fonts.gstatic.com; "
            "img-src 'self' data: https:; "
            "connect-src 'self' https://api.telegram.org https://discord.com; "
            "frame-ancestors 'none';"
        )
        response.headers['Content-Security-Policy'] = csp
        
        # Другие заголовки безопасности
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        response.headers['Permissions-Policy'] = 'geolocation=(), microphone=(), camera=()'
        
        return response

def log_security_event(event_type, description, user_id=None, ip_address=None, severity="INFO"):
    """Логирует события безопасности"""
    try:
        from playland.monitoring.security_monitor import log_security_event as monitor_log
        monitor_log(
            event_type=event_type,
            severity=severity,
            description=description,
            user_id=user_id,
            ip_address=ip_address or request.remote_addr
        )
    except Exception as e:
        current_app.logger.error(f"Failed to log security event: {e}")

def check_suspicious_activity():
    """Проверяет подозрительную активность"""
    if not current_user.is_authenticated:
        return
    
    # Проверяем количество неудачных попыток входа
    if current_user.failed_login_attempts >= 3:
        log_security_event(
            "suspicious_login_attempts",
            f"User {current_user.id} has {current_user.failed_login_attempts} failed login attempts",
            user_id=current_user.id,
            severity="WARNING"
        )
    
    # Проверяем время последней активности
    if current_user.last_seen:
        time_diff = datetime.now(datetime.timezone.utc) - current_user.last_seen
        if time_diff > timedelta(hours=24):
            log_security_event(
                "long_inactivity",
                f"User {current_user.id} inactive for {time_diff.days} days",
                user_id=current_user.id,
                severity="INFO"
            )
