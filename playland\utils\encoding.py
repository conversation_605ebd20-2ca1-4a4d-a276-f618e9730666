#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Утилиты для работы с кодировкой файлов.
"""

import os
import logging
import re
from pathlib import Path

logger = logging.getLogger('playland.utils.encoding')

# Пути, которые нужно исключить
EXCLUDE_PATHS = [
    'venv',
    '__pycache__',
    '.git',
    'node_modules'
]

def detect_encoding(file_path):
    """
    Определяет кодировку файла.
    
    Простая эвристика:
    - Если файл содержит BOM, то это UTF-8, UTF-16 или UTF-32
    - Если файл содержит символы с кодами > 127, то это не ASCII
    - Если файл содержит последовательности байтов, характерные для кириллицы в CP1251, то это CP1251
    - Иначе предполагаем, что это UTF-8
    """
    with open(file_path, 'rb') as f:
        raw_data = f.read()
        
        # Проверка на BOM
        if raw_data.startswith(b'\xef\xbb\xbf'):
            return 'utf-8-sig', raw_data
        elif raw_data.startswith(b'\xff\xfe') or raw_data.startswith(b'\xfe\xff'):
            return 'utf-16', raw_data
        elif raw_data.startswith(b'\xff\xfe\x00\x00') or raw_data.startswith(b'\x00\x00\xfe\xff'):
            return 'utf-32', raw_data
        
        # Проверка на ASCII
        is_ascii = all(b <= 127 for b in raw_data)
        if is_ascii:
            return 'ascii', raw_data
        
        # Проверка на CP1251 (кириллица)
        # Характерные последовательности для кириллицы в CP1251
        cp1251_patterns = [
            b'\xd0\xe0', b'\xd1\xe2', b'\xd2\xe5', b'\xd3\xea', b'\xd4\xee',
            b'\xc0\xe0', b'\xc1\xe1', b'\xc2\xe2', b'\xc3\xe3', b'\xc4\xe4'
        ]
        
        # Если найдены характерные последовательности для CP1251
        if any(pattern in raw_data for pattern in cp1251_patterns):
            # Проверяем, можно ли декодировать как UTF-8
            try:
                raw_data.decode('utf-8')
                return 'utf-8', raw_data
            except UnicodeDecodeError:
                return 'cp1251', raw_data
        
        # По умолчанию предполагаем UTF-8
        try:
            raw_data.decode('utf-8')
            return 'utf-8', raw_data
        except UnicodeDecodeError:
            # Если не удалось декодировать как UTF-8, пробуем CP1251
            try:
                raw_data.decode('cp1251')
                return 'cp1251', raw_data
            except UnicodeDecodeError:
                # Если не удалось декодировать и как CP1251, возвращаем None
                return None, raw_data

def fix_file_encoding(file_path):
    """
    Исправляет кодировку файла.
    """
    try:
        # Определяем текущую кодировку файла
        encoding, raw_data = detect_encoding(file_path)
        
        if encoding is None:
            logger.warning(f"Не удалось определить кодировку файла: {file_path}")
            return False
        
        # Если кодировка не UTF-8, конвертируем файл
        if encoding.lower() != 'utf-8':
            logger.info(f"Конвертация файла {file_path} из {encoding} в UTF-8")
            
            # Пытаемся декодировать содержимое файла
            try:
                content = raw_data.decode(encoding)
            except UnicodeDecodeError:
                logger.error(f"Ошибка декодирования файла {file_path} с кодировкой {encoding}")
                return False
            
            # Добавляем или обновляем заголовок кодировки для Python файлов
            if file_path.endswith('.py'):
                lines = content.split('\n')
                
                # Проверяем наличие shebang
                has_shebang = lines[0].startswith('#!')
                
                # Проверяем наличие заголовка кодировки
                has_encoding = any(line.startswith('# -*- coding:') or line.startswith('# coding=') for line in lines[:2])
                
                if has_shebang and not has_encoding:
                    # Добавляем заголовок кодировки после shebang
                    lines.insert(1, '# -*- coding: utf-8 -*-')
                    content = '\n'.join(lines)
                elif not has_shebang and not has_encoding:
                    # Добавляем заголовок кодировки в начало файла
                    content = '# -*- coding: utf-8 -*-\n\n' + content
            
            # Записываем содержимое в файл в кодировке UTF-8
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info(f"Файл {file_path} успешно конвертирован в UTF-8")
            return True
        else:
            logger.debug(f"Файл {file_path} уже в кодировке UTF-8")
            return True
    
    except Exception as e:
        logger.error(f"Ошибка при исправлении кодировки файла {file_path}: {str(e)}")
        return False

def fix_project_encoding(project_path):
    """
    Исправляет кодировку всех файлов в проекте.
    """
    # Получаем список всех файлов в проекте
    files_to_fix = []
    
    for root, dirs, files in os.walk(project_path):
        # Исключаем указанные пути
        dirs[:] = [d for d in dirs if d not in EXCLUDE_PATHS]
        
        for file in files:
            # Обрабатываем только текстовые файлы
            if file.endswith(('.py', '.txt', '.md', '.html', '.css', '.js', '.json', '.yml', '.yaml')):
                file_path = os.path.join(root, file)
                files_to_fix.append(file_path)
    
    # Исправляем кодировку файлов
    fixed_count = 0
    error_count = 0
    
    for file_path in files_to_fix:
        logger.debug(f"Обработка файла: {file_path}")
        
        if fix_file_encoding(file_path):
            fixed_count += 1
        else:
            error_count += 1
    
    logger.info(f"Обработка завершена. Исправлено файлов: {fixed_count}, ошибок: {error_count}")
    
    return fixed_count, error_count
