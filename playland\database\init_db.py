#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Скрипт для инициализации базы данных.
Создает необходимые таблицы и заполняет их начальными данными.
"""

import os
import sys
import logging
import argparse
from datetime import datetime

# Заменяем функцию load_dotenv своей реализацией
def load_dotenv():
    """
    Простая реализация загрузки переменных окружения из файла .env
    """
    if not os.path.exists('.env'):
        return

    with open('.env', 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line or line.startswith('#'):
                continue

            try:
                key, value = line.split('=', 1)
                os.environ[key] = value
            except ValueError:
                # Пропускаем строки, которые не содержат '='
                continue

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('init_db')

# Загрузка переменных окружения
load_dotenv()

# Добавление пути к проекту
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
root_dir = os.path.dirname(parent_dir)
if root_dir not in sys.path:
    sys.path.insert(0, root_dir)

def init_db(drop_all=False, create_admin=False, admin_email=None, admin_password=None):
    """
    Инициализирует базу данных.

    Args:
        drop_all (bool): Удалить все таблицы перед созданием
        create_admin (bool): Создать администратора
        admin_email (str): Email администратора
        admin_password (str): Пароль администратора
    """
    try:
        # Импортируем модули
        import sys
        import os

        # Добавляем корневую директорию проекта в sys.path
        project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../.."))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)

        # Импорт моделей
        from playland.website.models import db, User, Role, Application, Ticket, ServerEvent, TeamApplication
        from playland.website.app import create_app

        # Создание приложения
        app = create_app()

        with app.app_context():
            # Удаление всех таблиц, если указано
            if drop_all:
                logger.info("Удаление всех таблиц...")
                db.drop_all()

            # Создание таблиц
            logger.info("Создание таблиц...")
            db.create_all()

            # Создание ролей
            logger.info("Создание ролей...")
            roles = [
                Role(name='admin', description='Администратор'),
                Role(name='moderator', description='Модератор'),
                Role(name='user', description='Пользователь'),
                Role(name='vip', description='VIP-пользователь'),
                Role(name='banned', description='Заблокированный пользователь')
            ]

            # Проверка существования ролей
            for role in roles:
                existing_role = Role.query.filter_by(name=role.name).first()
                if not existing_role:
                    db.session.add(role)

            db.session.commit()

            # Создание администратора, если указано
            if create_admin:
                admin_email = admin_email or os.getenv('ADMIN_EMAIL', '<EMAIL>')
                admin_password = admin_password or os.getenv('ADMIN_PASSWORD', 'admin')

                # Проверка существования администратора
                existing_admin = User.query.filter_by(email=admin_email).first()
                if not existing_admin:
                    logger.info(f"Создание администратора с email: {admin_email}")

                    # Получение роли администратора
                    admin_role = Role.query.filter_by(name='admin').first()

                    # Создание администратора
                    admin = User(
                        email=admin_email,
                        username='admin',
                        minecraft_nickname='admin',
                        is_active=True,
                        is_admin=True,
                        created_at=datetime.now()
                    )
                    admin.set_password(admin_password)
                    admin.roles.append(admin_role)

                    db.session.add(admin)
                    db.session.commit()
                else:
                    logger.info(f"Администратор с email {admin_email} уже существует")

            logger.info("База данных успешно инициализирована")

    except Exception as e:
        logger.error(f"Ошибка при инициализации базы данных: {str(e)}")
        sys.exit(1)

def create_test_data():
    """
    Создает тестовые данные в базе данных.
    """
    try:
        # Импортируем модули
        import sys
        import os

        # Добавляем корневую директорию проекта в sys.path
        project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../.."))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)

        # Импорт моделей
        from playland.website.models import db, User, Role, Application, Ticket, ServerEvent, TeamApplication
        from playland.website.app import create_app

        # Создание приложения
        app = create_app()

        with app.app_context():
            # Создание тестовых пользователей
            logger.info("Создание тестовых пользователей...")

            # Получение ролей
            user_role = Role.query.filter_by(name='user').first()
            vip_role = Role.query.filter_by(name='vip').first()

            # Создание тестовых пользователей
            test_users = [
                {
                    'email': '<EMAIL>',
                    'username': 'user1',
                    'minecraft_nickname': 'player1',
                    'password': 'password1',
                    'role': user_role
                },
                {
                    'email': '<EMAIL>',
                    'username': 'user2',
                    'minecraft_nickname': 'player2',
                    'password': 'password2',
                    'role': user_role
                },
                {
                    'email': '<EMAIL>',
                    'username': 'vip_user',
                    'minecraft_nickname': 'vip_player',
                    'password': 'vip_password',
                    'role': vip_role
                }
            ]

            for user_data in test_users:
                existing_user = User.query.filter_by(email=user_data['email']).first()
                if not existing_user:
                    user = User(
                        email=user_data['email'],
                        username=user_data['username'],
                        minecraft_nickname=user_data['minecraft_nickname'],
                        is_active=True,
                        created_at=datetime.now()
                    )
                    user.set_password(user_data['password'])
                    user.roles.append(user_data['role'])

                    db.session.add(user)

            db.session.commit()

            # Создание тестовых заявок
            logger.info("Создание тестовых заявок...")

            test_applications = [
                {
                    'minecraft_nickname': 'new_player1',
                    'discord_tag': 'new_player1#1234',
                    'email': '<EMAIL>',
                    'age': 18,
                    'about_yourself': 'Я новый игрок, хочу присоединиться к серверу.',
                    'find_server': 'Нашел через друга',
                    'minecraft_experience': '2 года',
                    'status': 'pending'
                },
                {
                    'minecraft_nickname': 'new_player2',
                    'discord_tag': 'new_player2#5678',
                    'email': '<EMAIL>',
                    'age': 20,
                    'about_yourself': 'Опытный игрок, ищу новый сервер.',
                    'find_server': 'Нашел в интернете',
                    'minecraft_experience': '5 лет',
                    'status': 'approved'
                },
                {
                    'minecraft_nickname': 'new_player3',
                    'discord_tag': 'new_player3#9012',
                    'email': '<EMAIL>',
                    'age': 16,
                    'about_yourself': 'Новичок в Minecraft.',
                    'find_server': 'Нашел через YouTube',
                    'minecraft_experience': '6 месяцев',
                    'status': 'rejected'
                }
            ]

            for app_data in test_applications:
                existing_app = Application.query.filter_by(minecraft_nickname=app_data['minecraft_nickname']).first()
                if not existing_app:
                    application = Application(
                        minecraft_nickname=app_data['minecraft_nickname'],
                        discord_tag=app_data['discord_tag'],
                        email=app_data['email'],
                        age=app_data['age'],
                        about_yourself=app_data['about_yourself'],
                        find_server=app_data['find_server'],
                        minecraft_experience=app_data['minecraft_experience'],
                        status=app_data['status'],
                        created_at=datetime.now()
                    )

                    db.session.add(application)

            db.session.commit()

            # Создание тестовых тикетов
            logger.info("Создание тестовых тикетов...")

            # Получение пользователей
            user1 = User.query.filter_by(username='user1').first()
            user2 = User.query.filter_by(username='user2').first()

            test_tickets = [
                {
                    'user': user1,
                    'subject': 'Проблема с входом на сервер',
                    'description': 'Не могу войти на сервер, выдает ошибку.',
                    'status': 'open'
                },
                {
                    'user': user1,
                    'subject': 'Вопрос по донату',
                    'description': 'Как приобрести VIP-статус?',
                    'status': 'closed'
                },
                {
                    'user': user2,
                    'subject': 'Жалоба на игрока',
                    'description': 'Игрок нарушает правила сервера.',
                    'status': 'in_progress'
                }
            ]

            for ticket_data in test_tickets:
                ticket = Ticket(
                    user_id=ticket_data['user'].id,
                    subject=ticket_data['subject'],
                    description=ticket_data['description'],
                    status=ticket_data['status'],
                    created_at=datetime.now()
                )

                db.session.add(ticket)

            db.session.commit()

            # Создание тестовых событий сервера
            logger.info("Создание тестовых событий сервера...")

            test_events = [
                {
                    'event_type': 'server_start',
                    'content': 'Сервер запущен',
                    'timestamp': datetime.now()
                },
                {
                    'event_type': 'player_join',
                    'content': 'Игрок player1 присоединился к серверу',
                    'timestamp': datetime.now()
                },
                {
                    'event_type': 'player_leave',
                    'content': 'Игрок player1 покинул сервер',
                    'timestamp': datetime.now()
                },
                {
                    'event_type': 'whitelist_add',
                    'content': 'Игрок new_player2 добавлен в вайтлист',
                    'timestamp': datetime.now()
                }
            ]

            for event_data in test_events:
                event = ServerEvent(
                    event_type=event_data['event_type'],
                    content=event_data['content'],
                    timestamp=event_data['timestamp']
                )

                db.session.add(event)

            db.session.commit()

            logger.info("Тестовые данные успешно созданы")

    except Exception as e:
        logger.error(f"Ошибка при создании тестовых данных: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Инициализация базы данных')
    parser.add_argument('--drop-all', action='store_true', help='Удалить все таблицы перед созданием')
    parser.add_argument('--create-admin', action='store_true', help='Создать администратора')
    parser.add_argument('--admin-email', help='Email администратора')
    parser.add_argument('--admin-password', help='Пароль администратора')
    parser.add_argument('--test-data', action='store_true', help='Создать тестовые данные')

    args = parser.parse_args()

    # Инициализация базы данных
    init_db(
        drop_all=args.drop_all,
        create_admin=args.create_admin,
        admin_email=args.admin_email,
        admin_password=args.admin_password
    )

    # Создание тестовых данных, если указано
    if args.test_data:
        create_test_data()
