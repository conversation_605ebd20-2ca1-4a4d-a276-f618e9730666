# PlayLand - Unified Telegram Bot + Website
FROM python:3.11-slim

# Метаданные
LABEL maintainer="PlayLand Team"
LABEL description="PlayLand Telegram Bot and Website"
LABEL version="1.0"

# Рабочая директория
WORKDIR /app

# Системные зависимости
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    libpq-dev \
    gcc \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Копируем файлы зависимостей
COPY requirements.txt .

# Устанавливаем Python зависимости
RUN pip install --no-cache-dir -r requirements.txt

# Копируем код приложения
COPY . .

# Создаем необходимые папки
RUN mkdir -p logs playland/database playland/website/static/uploads playland/website/instance

# Переменные окружения
ENV PYTHONPATH=/app \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1

# Порт для веб-сайта
EXPOSE 5000

# Проверка здоровья
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:5000/health')" || exit 1

# Запуск приложения (бот + сайт)
CMD ["python", "main.py"]
