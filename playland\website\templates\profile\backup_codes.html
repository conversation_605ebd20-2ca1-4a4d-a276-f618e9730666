{% extends "base.html" %}

{% block title %}Резервные коды 2FA - PlayLand{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0"><i class="fas fa-check-circle"></i> Двухфакторная аутентификация настроена!</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <h5><i class="fas fa-exclamation-triangle"></i> Важно! Сохраните эти резервные коды</h5>
                        <p class="mb-0">
                            Эти коды позволят вам войти в аккаунт, если вы потеряете доступ к приложению аутентификации. 
                            Каждый код можно использовать только один раз.
                        </p>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <h5>Ваши резервные коды:</h5>
                            <div class="backup-codes-container">
                                {% for code in codes %}
                                    <div class="backup-code">
                                        <code>{{ code }}</code>
                                    </div>
                                {% endfor %}
                            </div>

                            <div class="mt-3">
                                <button type="button" class="btn btn-primary" onclick="copyAllCodes()">
                                    <i class="fas fa-copy"></i> Копировать все коды
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="printCodes()">
                                    <i class="fas fa-print"></i> Распечатать
                                </button>
                                <button type="button" class="btn btn-info" onclick="downloadCodes()">
                                    <i class="fas fa-download"></i> Скачать как файл
                                </button>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> Рекомендации:</h6>
                                <ul class="mb-0">
                                    <li>Сохраните коды в безопасном месте</li>
                                    <li>Не храните их в том же месте, что и пароли</li>
                                    <li>Можете распечатать и хранить физически</li>
                                    <li>Сгенерируйте новые коды, если эти скомпрометированы</li>
                                </ul>
                            </div>

                            <div class="alert alert-success">
                                <h6><i class="fas fa-shield-alt"></i> Ваш аккаунт защищен!</h6>
                                <p class="mb-0">
                                    Теперь при входе вам потребуется ввести код из приложения аутентификации.
                                </p>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <div class="text-center">
                        <div class="form-check d-inline-block me-3">
                            <input class="form-check-input" type="checkbox" id="confirmSaved">
                            <label class="form-check-label" for="confirmSaved">
                                Я сохранил резервные коды в безопасном месте
                            </label>
                        </div>
                        <button type="button" class="btn btn-success" id="continueBtn" disabled onclick="goToSecurity()">
                            <i class="fas fa-arrow-right"></i> Продолжить
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Скрытый элемент для печати -->
<div id="printContent" style="display: none;">
    <h2>PlayLand - Резервные коды двухфакторной аутентификации</h2>
    <p><strong>Пользователь:</strong> {{ current_user.username }}</p>
    <p><strong>Email:</strong> {{ current_user.email }}</p>
    <p><strong>Дата создания:</strong> {{ moment().format('DD.MM.YYYY HH:mm') }}</p>
    <hr>
    <h3>Резервные коды:</h3>
    <div style="font-family: monospace; font-size: 14px; line-height: 2;">
        {% for code in codes %}
            <div>{{ loop.index }}. {{ code }}</div>
        {% endfor %}
    </div>
    <hr>
    <p><strong>Важно:</strong></p>
    <ul>
        <li>Каждый код можно использовать только один раз</li>
        <li>Храните эти коды в безопасном месте</li>
        <li>Не передавайте коды третьим лицам</li>
        <li>При компрометации сгенерируйте новые коды</li>
    </ul>
</div>

<script>
// Включение кнопки "Продолжить" при подтверждении
document.getElementById('confirmSaved').addEventListener('change', function() {
    document.getElementById('continueBtn').disabled = !this.checked;
});

function copyAllCodes() {
    const codes = [
        {% for code in codes %}
            "{{ code }}"{% if not loop.last %},{% endif %}
        {% endfor %}
    ];
    
    const codesText = codes.join('\n');
    
    navigator.clipboard.writeText(codesText).then(function() {
        showNotification('Все коды скопированы в буфер обмена!', 'success');
    }).catch(function(err) {
        console.error('Ошибка копирования: ', err);
        // Fallback для старых браузеров
        const textArea = document.createElement('textarea');
        textArea.value = codesText;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showNotification('Все коды скопированы в буфер обмена!', 'success');
    });
}

function printCodes() {
    const printContent = document.getElementById('printContent').innerHTML;
    const originalContent = document.body.innerHTML;
    
    document.body.innerHTML = printContent;
    window.print();
    document.body.innerHTML = originalContent;
    
    // Восстанавливаем обработчики событий
    location.reload();
}

function downloadCodes() {
    const codes = [
        {% for code in codes %}
            "{{ code }}"{% if not loop.last %},{% endif %}
        {% endfor %}
    ];
    
    const content = `PlayLand - Резервные коды двухфакторной аутентификации

Пользователь: {{ current_user.username }}
Email: {{ current_user.email }}
Дата создания: ${new Date().toLocaleString('ru-RU')}

Резервные коды:
${codes.map((code, index) => `${index + 1}. ${code}`).join('\n')}

Важно:
- Каждый код можно использовать только один раз
- Храните эти коды в безопасном месте
- Не передавайте коды третьим лицам
- При компрометации сгенерируйте новые коды`;

    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'playland_backup_codes.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
    
    showNotification('Файл с кодами скачан!', 'success');
}

function goToSecurity() {
    window.location.href = "{{ url_for('profile.security_settings') }}";
}

function showNotification(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // Автоматическое удаление через 3 секунды
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 3000);
}
</script>

<style>
.backup-codes-container {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.backup-code {
    display: inline-block;
    margin: 0.25rem;
    padding: 0.5rem;
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    width: calc(50% - 0.5rem);
    text-align: center;
}

.backup-code code {
    font-size: 1.1em;
    font-weight: bold;
    color: #495057;
    background: none;
    padding: 0;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.alert {
    border-radius: 0.375rem;
}

.btn {
    border-radius: 0.375rem;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

@media print {
    body * {
        visibility: hidden;
    }
    
    #printContent, #printContent * {
        visibility: visible;
    }
    
    #printContent {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
    }
}
</style>
{% endblock %}
