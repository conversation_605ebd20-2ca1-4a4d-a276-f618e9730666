#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Модуль для централизованной системы уведомлений.
Предоставляет функции для отправки уведомлений через различные каналы связи.
"""

import os
import logging
import json
from datetime import datetime
from dotenv import load_dotenv
from flask import current_app

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('notifications')

# Загрузка переменных окружения
load_dotenv()

# Импорт модулей интеграции
try:
    from website.telegram_integration import send_message as telegram_send
    from website.telegram_integration import send_admin_notification as telegram_admin_notify
    TELEGRAM_AVAILABLE = True
except ImportError:
    TELEGRAM_AVAILABLE = False
    logger.warning("Модуль Telegram не установлен, уведомления через Telegram не будут работать")
    
    # Заглушка для функций Telegram
    def telegram_send(chat_id, message, **kwargs):
        logger.warning("Попытка отправить сообщение в Telegram, но модуль не установлен")
        return False
    
    def telegram_admin_notify(message, **kwargs):
        logger.warning("Попытка отправить уведомление администраторам через Telegram, но модуль не установлен")
        return False

try:
    from website.discord_integration import send_notification as discord_send
    from website.discord_integration import send_admin_notification as discord_admin_notify
    DISCORD_AVAILABLE = True
except ImportError:
    DISCORD_AVAILABLE = False
    logger.warning("Модуль Discord не установлен, уведомления через Discord не будут работать")
    
    # Заглушка для функций Discord
    def discord_send(channel_id, message, **kwargs):
        logger.warning("Попытка отправить сообщение в Discord, но модуль не установлен")
        return False
    
    def discord_admin_notify(message, **kwargs):
        logger.warning("Попытка отправить уведомление администраторам через Discord, но модуль не установлен")
        return False

try:
    from cross_platform_integration import broadcast_server_event
    CROSS_PLATFORM_AVAILABLE = True
except ImportError:
    CROSS_PLATFORM_AVAILABLE = False
    logger.warning("Модуль кросс-платформенной интеграции не установлен")
    
    # Заглушка для функции кросс-платформенного уведомления
    def broadcast_server_event(event_type, content, **kwargs):
        logger.warning("Попытка отправить кросс-платформенное уведомление, но модуль не установлен")
        return False

class NotificationService:
    """
    Сервис для отправки уведомлений через различные каналы связи.
    """
    
    def __init__(self, app=None):
        """
        Инициализация сервиса уведомлений.
        
        Args:
            app: Экземпляр Flask-приложения
        """
        self.telegram_enabled = TELEGRAM_AVAILABLE
        self.discord_enabled = DISCORD_AVAILABLE
        self.cross_platform_enabled = CROSS_PLATFORM_AVAILABLE
        self.email_enabled = False
        
        # Настройки каналов уведомлений
        self.telegram_chat_id = os.getenv('TELEGRAM_NOTIFICATION_CHAT_ID')
        self.discord_channel_id = os.getenv('DISCORD_NOTIFICATION_CHANNEL_ID')
        
        # Настройки для администраторов
        self.admin_telegram_chat_id = os.getenv('ADMIN_TELEGRAM_CHAT_ID')
        self.admin_discord_channel_id = os.getenv('ADMIN_DISCORD_CHANNEL_ID')
        
        # Настройки для уведомлений по электронной почте
        self.smtp_server = os.getenv('SMTP_SERVER')
        self.smtp_port = os.getenv('SMTP_PORT')
        self.smtp_username = os.getenv('SMTP_USERNAME')
        self.smtp_password = os.getenv('SMTP_PASSWORD')
        self.email_from = os.getenv('EMAIL_FROM')
        self.admin_email = os.getenv('ADMIN_EMAIL')
        
        if self.smtp_server and self.smtp_username and self.smtp_password:
            self.email_enabled = True
        
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """
        Инициализирует сервис уведомлений для Flask-приложения.
        
        Args:
            app: Экземпляр Flask-приложения
        """
        # Настройки из конфигурации приложения
        self.telegram_enabled = app.config.get('TELEGRAM_NOTIFICATIONS_ENABLED', self.telegram_enabled)
        self.discord_enabled = app.config.get('DISCORD_NOTIFICATIONS_ENABLED', self.discord_enabled)
        self.cross_platform_enabled = app.config.get('CROSS_PLATFORM_NOTIFICATIONS_ENABLED', self.cross_platform_enabled)
        self.email_enabled = app.config.get('EMAIL_NOTIFICATIONS_ENABLED', self.email_enabled)
        
        # Логирование статуса каналов уведомлений
        channels = []
        if self.telegram_enabled:
            channels.append('Telegram')
        if self.discord_enabled:
            channels.append('Discord')
        if self.cross_platform_enabled:
            channels.append('Cross-Platform')
        if self.email_enabled:
            channels.append('Email')
        
        app.logger.info(f"Инициализирован сервис уведомлений. Доступные каналы: {', '.join(channels)}")
    
    def send_notification(self, message, user_id=None, email=None, parse_mode=None):
        """
        Отправляет уведомление пользователю через доступные каналы связи.
        
        Args:
            message (str): Текст уведомления
            user_id (int, optional): ID пользователя в базе данных
            email (str, optional): Email пользователя
            parse_mode (str, optional): Режим форматирования текста
            
        Returns:
            dict: Результаты отправки по каждому каналу
        """
        results = {
            'telegram': False,
            'discord': False,
            'email': False
        }
        
        # Получаем информацию о пользователе, если указан ID
        user_info = None
        if user_id:
            user_info = self._get_user_info(user_id)
        
        # Отправка через Telegram
        if self.telegram_enabled and (user_info and user_info.get('telegram_id')):
            try:
                results['telegram'] = telegram_send(
                    user_info['telegram_id'],
                    message,
                    parse_mode=parse_mode
                )
            except Exception as e:
                logger.error(f"Ошибка при отправке уведомления в Telegram: {str(e)}")
        
        # Отправка через Discord
        if self.discord_enabled and (user_info and user_info.get('discord_id')):
            try:
                results['discord'] = discord_send(
                    user_info['discord_id'],
                    message,
                    is_dm=True
                )
            except Exception as e:
                logger.error(f"Ошибка при отправке уведомления в Discord: {str(e)}")
        
        # Отправка по электронной почте
        if self.email_enabled and (email or (user_info and user_info.get('email'))):
            email_to = email or user_info['email']
            try:
                results['email'] = self._send_email(
                    email_to,
                    "Уведомление от PlayLand",
                    message
                )
            except Exception as e:
                logger.error(f"Ошибка при отправке уведомления по email: {str(e)}")
        
        # Логирование результатов
        success_channels = [channel for channel, result in results.items() if result]
        if success_channels:
            logger.info(f"Уведомление отправлено через каналы: {', '.join(success_channels)}")
        else:
            logger.warning(f"Не удалось отправить уведомление ни через один канал")
        
        return results
    
    def send_admin_notification(self, message, parse_mode=None):
        """
        Отправляет уведомление администраторам через доступные каналы связи.
        
        Args:
            message (str): Текст уведомления
            parse_mode (str, optional): Режим форматирования текста
            
        Returns:
            dict: Результаты отправки по каждому каналу
        """
        results = {
            'telegram': False,
            'discord': False,
            'email': False
        }
        
        # Отправка через Telegram
        if self.telegram_enabled and self.admin_telegram_chat_id:
            try:
                results['telegram'] = telegram_admin_notify(message, parse_mode=parse_mode)
            except Exception as e:
                logger.error(f"Ошибка при отправке уведомления администраторам через Telegram: {str(e)}")
        
        # Отправка через Discord
        if self.discord_enabled and self.admin_discord_channel_id:
            try:
                results['discord'] = discord_admin_notify(message)
            except Exception as e:
                logger.error(f"Ошибка при отправке уведомления администраторам через Discord: {str(e)}")
        
        # Отправка по электронной почте
        if self.email_enabled and self.admin_email:
            try:
                results['email'] = self._send_email(
                    self.admin_email,
                    "Уведомление администратору PlayLand",
                    message
                )
            except Exception as e:
                logger.error(f"Ошибка при отправке уведомления администраторам по email: {str(e)}")
        
        # Логирование результатов
        success_channels = [channel for channel, result in results.items() if result]
        if success_channels:
            logger.info(f"Уведомление администраторам отправлено через каналы: {', '.join(success_channels)}")
        else:
            logger.warning(f"Не удалось отправить уведомление администраторам ни через один канал")
        
        return results
    
    def broadcast_event(self, event_type, content, **kwargs):
        """
        Отправляет уведомление о событии на все платформы.
        
        Args:
            event_type (str): Тип события
            content (str): Содержание уведомления
            **kwargs: Дополнительные параметры
            
        Returns:
            bool: True, если уведомление отправлено хотя бы на одну платформу
        """
        if not self.cross_platform_enabled:
            logger.warning("Кросс-платформенные уведомления отключены")
            return False
        
        try:
            return broadcast_server_event(event_type, content, **kwargs)
        except Exception as e:
            logger.error(f"Ошибка при отправке кросс-платформенного уведомления: {str(e)}")
            return False
    
    def _get_user_info(self, user_id):
        """
        Получает информацию о пользователе из базы данных.
        
        Args:
            user_id (int): ID пользователя
            
        Returns:
            dict: Информация о пользователе или None в случае ошибки
        """
        try:
            # Импортируем модели только при необходимости
            from website.models import User, db
            
            user = User.query.get(user_id)
            if user:
                return {
                    'telegram_id': user.telegram_id,
                    'discord_id': user.discord_id,
                    'email': user.email
                }
            
            return None
        except Exception as e:
            logger.error(f"Ошибка при получении информации о пользователе: {str(e)}")
            return None
    
    def _send_email(self, to, subject, body):
        """
        Отправляет электронное письмо.
        
        Args:
            to (str): Адрес получателя
            subject (str): Тема письма
            body (str): Текст письма
            
        Returns:
            bool: True, если письмо отправлено успешно, иначе False
        """
        if not self.email_enabled:
            logger.warning("Отправка email отключена")
            return False
        
        try:
            import smtplib
            from email.mime.text import MIMEText
            from email.mime.multipart import MIMEMultipart
            
            # Создание сообщения
            msg = MIMEMultipart()
            msg['From'] = self.email_from
            msg['To'] = to
            msg['Subject'] = subject
            
            # Добавление текста
            msg.attach(MIMEText(body, 'plain'))
            
            # Отправка письма
            server = smtplib.SMTP(self.smtp_server, int(self.smtp_port))
            server.starttls()
            server.login(self.smtp_username, self.smtp_password)
            server.send_message(msg)
            server.quit()
            
            logger.info(f"Email отправлен на адрес {to}")
            return True
        
        except Exception as e:
            logger.error(f"Ошибка при отправке email: {str(e)}")
            return False

# Создаем глобальный экземпляр сервиса уведомлений
notification_service = NotificationService()

def init_notification_service(app):
    """
    Инициализирует сервис уведомлений для Flask-приложения.
    
    Args:
        app: Экземпляр Flask-приложения
        
    Returns:
        NotificationService: Экземпляр сервиса уведомлений
    """
    global notification_service
    notification_service = NotificationService(app)
    return notification_service

def send_notification(message, user_id=None, email=None, parse_mode=None):
    """
    Отправляет уведомление пользователю через доступные каналы связи.
    
    Args:
        message (str): Текст уведомления
        user_id (int, optional): ID пользователя в базе данных
        email (str, optional): Email пользователя
        parse_mode (str, optional): Режим форматирования текста
        
    Returns:
        dict: Результаты отправки по каждому каналу
    """
    return notification_service.send_notification(message, user_id, email, parse_mode)

def send_admin_notification(message, parse_mode=None):
    """
    Отправляет уведомление администраторам через доступные каналы связи.
    
    Args:
        message (str): Текст уведомления
        parse_mode (str, optional): Режим форматирования текста
        
    Returns:
        dict: Результаты отправки по каждому каналу
    """
    return notification_service.send_admin_notification(message, parse_mode)

def broadcast_event(event_type, content, **kwargs):
    """
    Отправляет уведомление о событии на все платформы.
    
    Args:
        event_type (str): Тип события
        content (str): Содержание уведомления
        **kwargs: Дополнительные параметры
        
    Returns:
        bool: True, если уведомление отправлено хотя бы на одну платформу
    """
    return notification_service.broadcast_event(event_type, content, **kwargs)
