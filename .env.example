# Основные настройки приложения
FLASK_APP=playland/website/app.py
FLASK_ENV=development
SECRET_KEY=your_secret_key_here
DEBUG=True

# Настройки базы данных
DATABASE_URL=sqlite:///playland.db
# Для PostgreSQL:
# DATABASE_URL=postgresql://username:password@localhost:5432/playland
# Для MySQL:
# DATABASE_URL=mysql+pymysql://username:password@localhost:3306/playland

# Настройки Telegram бота
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_WEBHOOK_URL=https://your-domain.com/webhook/telegram
TELEGRAM_NOTIFICATION_CHAT_ID=your_notification_chat_id
ADMIN_TELEGRAM_CHAT_ID=your_admin_chat_id

# Настройки Discord бота
DISCORD_BOT_TOKEN=your_discord_bot_token
DISCORD_GUILD_ID=your_guild_id_here
DISCORD_ROLE_ID=your_role_id_here
DISCORD_NOTIFICATION_CHANNEL_ID=your_notification_channel_id
ADMIN_DISCORD_CHANNEL_ID=your_admin_channel_id
DISCORD_CLIENT_ID=your_client_id_here
DISCORD_CLIENT_SECRET=your_client_secret_here
DISCORD_REDIRECT_URI=http://localhost:5000/auth/discord/callback

# Настройки для кросс-платформенной интеграции
DISCORD_SYNC_CHANNEL_ID=your_discord_sync_channel_id
TELEGRAM_SYNC_CHAT_ID=your_telegram_sync_chat_id
API_BASE_URL=http://localhost:5000/api
API_SECRET_KEY=your_api_secret_key

# Настройки для Minecraft сервера
MINECRAFT_SERVER_HOST=localhost
MINECRAFT_SERVER_RCON_PORT=25575
MINECRAFT_SERVER_RCON_PASSWORD=your_rcon_password
MINECRAFT_SERVER_QUERY_PORT=25565
MINECRAFT_SERVER_API_URL=http://localhost:8080/api
MINECRAFT_SERVER_API_KEY=your_minecraft_api_key

# Настройки для отправки email
SMTP_SERVER=smtp.example.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_email_password
EMAIL_FROM=<EMAIL>
ADMIN_EMAIL=<EMAIL>

# Настройки для платежной системы
PAYMENT_SYSTEM_API_KEY=your_payment_system_api_key
PAYMENT_SYSTEM_SECRET=your_payment_system_secret
PAYMENT_CALLBACK_URL=https://your-domain.com/api/payment/callback

# Настройки для логирования и мониторинга
LOG_LEVEL=INFO
SENTRY_DSN=your_sentry_dsn

# Настройки для ограничения запросов
RATE_LIMIT_WINDOW=60
RATE_LIMIT_DEFAULT_IP=100
RATE_LIMIT_DEFAULT_PATH=200
