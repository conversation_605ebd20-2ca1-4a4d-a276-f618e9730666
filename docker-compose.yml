version: '3.8'

services:
  playland:
    build: .
    container_name: playland-server
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - WEBSITE_HOST=0.0.0.0
      - WEBSITE_PORT=5000
      - DEBUG=false
      - SECRET_KEY=${SECRET_KEY:-dev_secret_key_change_in_production}
    volumes:
      - ./playland/database:/app/playland/database
      - ./logs:/app/logs
      - ./playland/website/static/uploads:/app/playland/website/static/uploads
    networks:
      - playland-network
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:5000/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx для проксирования (опционально)
  nginx:
    image: nginx:alpine
    container_name: playland-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - playland
    networks:
      - playland-network
    profiles:
      - with-nginx

networks:
  playland-network:
    driver: bridge

volumes:
  playland-data:
    driver: local
