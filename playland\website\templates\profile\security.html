{% extends "base.html" %}

{% block title %}Настройки безопасности - PlayLand{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-3">
            <!-- Боковое меню профиля -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Профиль</h6>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('profile.profile') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-user"></i> Основная информация
                    </a>
                    <a href="{{ url_for('profile.security_settings') }}" class="list-group-item list-group-item-action active">
                        <i class="fas fa-shield-alt"></i> Безопасность
                    </a>
                    <a href="{{ url_for('profile.view_tickets') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-ticket-alt"></i> Тикеты
                    </a>
                </div>
            </div>
        </div>

        <div class="col-md-9">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <!-- Изменение пароля -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-key"></i> Изменение пароля</h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">Регулярно меняйте пароль для обеспечения безопасности аккаунта.</p>
                    <a href="{{ url_for('profile.change_password') }}" class="btn btn-primary">
                        <i class="fas fa-edit"></i> Изменить пароль
                    </a>
                </div>
            </div>

            <!-- Двухфакторная аутентификация -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-mobile-alt"></i> Двухфакторная аутентификация</h5>
                </div>
                <div class="card-body">
                    {% if current_user.two_factor_enabled %}
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> Двухфакторная аутентификация включена
                        </div>
                        <p class="text-muted">Ваш аккаунт защищен дополнительным уровнем безопасности.</p>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#disable2FAModal">
                                    <i class="fas fa-times"></i> Отключить 2FA
                                </button>
                            </div>
                            <div class="col-md-6">
                                <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#regenerateCodesModal">
                                    <i class="fas fa-sync"></i> Новые резервные коды
                                </button>
                            </div>
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i> Двухфакторная аутентификация отключена
                        </div>
                        <p class="text-muted">Включите 2FA для дополнительной защиты вашего аккаунта.</p>
                        <a href="{{ url_for('profile.two_factor_setup') }}" class="btn btn-success">
                            <i class="fas fa-plus"></i> Включить 2FA
                        </a>
                    {% endif %}
                </div>
            </div>

            <!-- Статус email -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-envelope"></i> Подтверждение Email</h5>
                </div>
                <div class="card-body">
                    {% if current_user.email_verified %}
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> Email подтвержден
                        </div>
                        <p class="text-muted">Ваш email адрес {{ current_user.email }} подтвержден.</p>
                    {% else %}
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle"></i> Email не подтвержден
                        </div>
                        <p class="text-muted">Подтвердите ваш email для полного доступа к функциям сайта.</p>
                        <a href="{{ url_for('auth.verify_email') }}" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i> Отправить письмо подтверждения
                        </a>
                    {% endif %}
                </div>
            </div>

            <!-- Активные сессии -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-desktop"></i> Активные сессии</h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">Управляйте активными сессиями вашего аккаунта.</p>
                    <div class="alert alert-info">
                        <strong>Текущая сессия:</strong><br>
                        <i class="fas fa-globe"></i> IP: {{ request.remote_addr }}<br>
                        <i class="fas fa-clock"></i> Время входа: {{ current_user.last_seen.strftime('%d.%m.%Y %H:%M') if current_user.last_seen }}
                    </div>
                    <button type="button" class="btn btn-danger" onclick="logoutAllSessions()">
                        <i class="fas fa-sign-out-alt"></i> Завершить все сессии
                    </button>
                </div>
            </div>

            <!-- Удаление аккаунта -->
            <div class="card mb-4 border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0"><i class="fas fa-trash"></i> Удаление аккаунта</h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">Удаление аккаунта необратимо. Все ваши данные будут потеряны.</p>
                    <a href="{{ url_for('profile.delete_account') }}" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Удалить аккаунт
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Модальное окно отключения 2FA -->
<div class="modal fade" id="disable2FAModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Отключить двухфакторную аутентификацию</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('profile.two_factor_disable') }}">
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i> 
                        Отключение 2FA снизит безопасность вашего аккаунта.
                    </div>
                    <div class="mb-3">
                        <label for="disable2FAPassword" class="form-label">Введите пароль для подтверждения:</label>
                        <input type="password" class="form-control" id="disable2FAPassword" name="password" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отмена</button>
                    <button type="submit" class="btn btn-warning">Отключить 2FA</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Модальное окно генерации новых резервных кодов -->
<div class="modal fade" id="regenerateCodesModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Генерация новых резервных кодов</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('profile.regenerate_backup_codes') }}">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> 
                        Старые резервные коды станут недействительными.
                    </div>
                    <div class="mb-3">
                        <label for="regeneratePassword" class="form-label">Введите пароль для подтверждения:</label>
                        <input type="password" class="form-control" id="regeneratePassword" name="password" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отмена</button>
                    <button type="submit" class="btn btn-primary">Сгенерировать</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function logoutAllSessions() {
    if (confirm('Вы уверены, что хотите завершить все активные сессии? Вам потребуется войти заново.')) {
        // Здесь можно добавить AJAX запрос для завершения всех сессий
        window.location.href = "{{ url_for('auth.enhanced_logout') }}";
    }
}
</script>

<style>
.list-group-item.active {
    background-color: #007bff;
    border-color: #007bff;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.alert {
    border-radius: 0.375rem;
}

.btn {
    border-radius: 0.375rem;
}

.modal-content {
    border-radius: 0.5rem;
}
</style>
{% endblock %}
