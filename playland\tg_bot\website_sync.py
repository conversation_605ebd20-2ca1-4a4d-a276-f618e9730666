#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Модуль синхронизации Telegram бота с веб-сайтом PlayLand.
Обеспечивает связь между ботом и сайтом через API.
"""

import os
import json
import hmac
import hashlib
import logging
import asyncio
import aiohttp
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any

# Настройка логирования
logger = logging.getLogger(__name__)

class WebsiteSync:
    """Класс для синхронизации с веб-сайтом"""
    
    def __init__(self):
        self.api_url = os.getenv('WEBSITE_API_URL', 'http://localhost:5555/api/sync')
        self.secret_key = os.getenv('SYNC_SECRET_KEY', 'sync-secret-key-change-in-production')
        self.session = None
        
    async def __aenter__(self):
        """Асинхронный контекстный менеджер - вход"""
        # Отключаем SSL для локального тестирования
        connector = aiohttp.TCPConnector(ssl=False)
        self.session = aiohttp.ClientSession(connector=connector)
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Асинхронный контекстный менеджер - выход"""
        if self.session:
            await self.session.close()
    
    def _generate_signature(self, data: str) -> str:
        """Генерирует HMAC подпись для аутентификации"""
        return hmac.new(
            self.secret_key.encode(),
            data.encode(),
            hashlib.sha256
        ).hexdigest()
    
    async def _make_request(self, endpoint: str, method: str = 'GET', data: Dict = None) -> Optional[Dict]:
        """Выполняет HTTP запрос к API сайта"""
        url = f"{self.api_url}/{endpoint.lstrip('/')}"
        
        try:
            # Подготавливаем данные
            json_data = json.dumps(data) if data else ""
            signature = self._generate_signature(json_data)
            
            headers = {
                'Content-Type': 'application/json',
                'X-Sync-Signature': signature
            }
            
            # Выполняем запрос
            async with self.session.request(
                method=method,
                url=url,
                data=json_data if data else None,
                headers=headers
            ) as response:
                
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    logger.error(f"API request failed: {response.status} - {error_text}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error making API request to {url}: {e}")
            return None
    
    async def link_telegram_user(self, telegram_id: int, username: str = None, 
                                first_name: str = None, last_name: str = None) -> bool:
        """Связывает пользователя Telegram с сайтом"""
        data = {
            'telegram_id': telegram_id,
            'username': username,
            'first_name': first_name,
            'last_name': last_name
        }
        
        result = await self._make_request('user/link', 'POST', data)
        if result and result.get('success'):
            logger.info(f"User {telegram_id} linked successfully")
            return True
        else:
            logger.error(f"Failed to link user {telegram_id}")
            return False
    
    async def create_application(self, telegram_id: int, minecraft_username: str,
                               discord_tag: str = None, email: str = None,
                               age: int = None, reason: str = None) -> Optional[int]:
        """Создает заявку через API сайта"""
        data = {
            'telegram_id': telegram_id,
            'minecraft_username': minecraft_username,
            'discord_tag': discord_tag,
            'email': email,
            'age': age,
            'reason': reason
        }
        
        result = await self._make_request('application/create', 'POST', data)
        if result and result.get('success'):
            application_id = result.get('application_id')
            logger.info(f"Application created successfully: {application_id}")
            return application_id
        else:
            logger.error(f"Failed to create application for user {telegram_id}")
            return None
    
    async def create_ticket(self, telegram_id: int, title: str, content: str,
                          application_type: str = 'support') -> Optional[int]:
        """Создает тикет через API сайта"""
        data = {
            'telegram_id': telegram_id,
            'title': title,
            'content': content,
            'application_type': application_type
        }
        
        result = await self._make_request('ticket/create', 'POST', data)
        if result and result.get('success'):
            ticket_id = result.get('ticket_id')
            logger.info(f"Ticket created successfully: {ticket_id}")
            return ticket_id
        else:
            logger.error(f"Failed to create ticket for user {telegram_id}")
            return None
    
    async def add_ticket_message(self, ticket_id: int, telegram_id: int, content: str,
                               is_admin_reply: bool = False, telegram_message_id: int = None) -> bool:
        """Добавляет сообщение в тикет"""
        data = {
            'telegram_id': telegram_id,
            'content': content,
            'is_admin_reply': is_admin_reply,
            'telegram_message_id': telegram_message_id
        }
        
        result = await self._make_request(f'ticket/{ticket_id}/message', 'POST', data)
        if result and result.get('success'):
            logger.info(f"Message added to ticket {ticket_id}")
            return True
        else:
            logger.error(f"Failed to add message to ticket {ticket_id}")
            return False
    
    async def get_ticket_messages(self, ticket_id: int) -> Optional[Dict]:
        """Получает сообщения тикета"""
        result = await self._make_request(f'ticket/{ticket_id}/messages', 'GET')
        if result and result.get('success'):
            return result
        else:
            logger.error(f"Failed to get messages for ticket {ticket_id}")
            return None
    
    async def get_latest_news(self, limit: int = 5) -> List[Dict]:
        """Получает последние новости с сайта"""
        result = await self._make_request(f'news/latest?limit={limit}', 'GET')
        if result and result.get('success'):
            return result.get('news', [])
        else:
            logger.error("Failed to get latest news")
            return []

# Глобальный экземпляр для использования в боте
website_sync = WebsiteSync()

async def sync_user_to_website(telegram_id: int, username: str = None,
                             first_name: str = None, last_name: str = None) -> bool:
    """Синхронизирует пользователя с сайтом"""
    async with WebsiteSync() as sync:
        return await sync.link_telegram_user(telegram_id, username, first_name, last_name)

async def create_application_on_website(telegram_id: int, application_data: Dict) -> Optional[int]:
    """Создает заявку на сайте"""
    async with WebsiteSync() as sync:
        return await sync.create_application(
            telegram_id=telegram_id,
            minecraft_username=application_data.get('minecraft_username'),
            discord_tag=application_data.get('discord_tag'),
            email=application_data.get('email'),
            age=application_data.get('age'),
            reason=application_data.get('reason')
        )

async def create_support_ticket(telegram_id: int, title: str, content: str) -> Optional[int]:
    """Создает тикет поддержки на сайте"""
    async with WebsiteSync() as sync:
        return await sync.create_ticket(telegram_id, title, content, 'support')

async def send_ticket_message(ticket_id: int, telegram_id: int, content: str,
                            is_admin: bool = False, message_id: int = None) -> bool:
    """Отправляет сообщение в тикет"""
    async with WebsiteSync() as sync:
        return await sync.add_ticket_message(
            ticket_id, telegram_id, content, is_admin, message_id
        )

async def get_news_for_telegram(limit: int = 3) -> List[Dict]:
    """Получает новости для отправки в Telegram"""
    async with WebsiteSync() as sync:
        return await sync.get_latest_news(limit)

# Функции для интеграции с существующим ботом
def setup_website_integration(bot_application):
    """Настраивает интеграцию с сайтом для существующего бота"""
    logger.info("Setting up website integration...")
    
    # Здесь можно добавить дополнительную настройку
    # например, периодическую синхронизацию или webhook'и
    
    return True

async def sync_application_status(application_id: int, status: str, 
                                rejection_reason: str = None) -> bool:
    """Синхронизирует статус заявки с сайтом"""
    # Эта функция будет вызываться при изменении статуса заявки в боте
    # чтобы обновить статус на сайте
    
    # TODO: Реализовать API endpoint для обновления статуса заявки
    logger.info(f"Application {application_id} status changed to {status}")
    return True

async def notify_website_about_bot_action(action: str, data: Dict) -> bool:
    """Уведомляет сайт о действиях в боте"""
    # Общая функция для уведомления сайта о различных действиях
    logger.info(f"Bot action: {action} with data: {data}")
    return True

# Декоратор для автоматической синхронизации пользователей
def sync_user(func):
    """Декоратор для автоматической синхронизации пользователя с сайтом"""
    async def wrapper(update, context, *args, **kwargs):
        user = update.effective_user
        if user:
            # Синхронизируем пользователя в фоне
            asyncio.create_task(sync_user_to_website(
                user.id, user.username, user.first_name, user.last_name
            ))
        
        return await func(update, context, *args, **kwargs)
    
    return wrapper

# Утилиты для работы с данными
def format_application_for_website(bot_application: Dict) -> Dict:
    """Преобразует данные заявки из формата бота в формат сайта"""
    return {
        'minecraft_username': bot_application.get('minecraft_nickname'),
        'discord_tag': bot_application.get('discord_tag'),
        'email': bot_application.get('email'),
        'age': bot_application.get('age'),
        'reason': bot_application.get('reason')
    }

def format_news_for_telegram(news_item: Dict) -> str:
    """Форматирует новость для отправки в Telegram"""
    title = news_item.get('title', 'Без названия')
    excerpt = news_item.get('excerpt', '')
    author = news_item.get('author', 'Администрация')
    date = news_item.get('created_at', '')
    
    # Форматируем дату
    try:
        date_obj = datetime.fromisoformat(date.replace('Z', '+00:00'))
        formatted_date = date_obj.strftime('%d.%m.%Y')
    except:
        formatted_date = date
    
    return f"📰 <b>{title}</b>\n\n{excerpt}\n\n👤 {author} • 📅 {formatted_date}"

# Проверка подключения к API
async def test_website_connection() -> bool:
    """Проверяет подключение к API сайта"""
    try:
        async with WebsiteSync() as sync:
            result = await sync._make_request('health', 'GET')
            if result and result.get('status') == 'healthy':
                logger.info("Website API connection successful")
                return True
            else:
                logger.error("Website API connection failed")
                return False
    except Exception as e:
        logger.error(f"Error testing website connection: {e}")
        return False
