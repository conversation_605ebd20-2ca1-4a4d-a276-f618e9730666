# -*- coding: utf-8 -*-
import os
import uuid
import json
import secrets
import requests
from datetime import datetime, timedelta
from flask import Blueprint, current_app, redirect, url_for, flash, request, session, render_template, jsonify
from flask_login import login_user, logout_user, current_user, login_required
from werkzeug.security import generate_password_hash, check_password_hash
from flask_mail import Message

from playland.website.models import db, User, AuditLog
from playland.website.forms import (
    LoginForm, RegisterForm, PasswordResetRequestForm, PasswordResetForm,
    TwoFactorSetupForm, TwoFactorForm, ChangePasswordForm, DeleteAccountForm,
    EmailVerificationForm, SecuritySettingsForm
)
from playland.website.security import (
    SecurityValidator, rate_limit, log_security_event,
    check_suspicious_activity, require_verified_email
)
# Опциональные импорты интеграций
try:
    from playland.integrations.telegram import process_telegram_auth, generate_telegram_auth_link
except ImportError:
    # Заглушки для отсутствующих интеграций
    def process_telegram_auth(data):
        return False, "Telegram интеграция недоступна"

    def generate_telegram_auth_link(user_id):
        return "#", "temp_code"

try:
    from playland.website.api import verify_telegram_signature
except ImportError:
    def verify_telegram_signature(data):
        return False

auth_bp = Blueprint('auth', __name__)

# Discord OAuth2 настройки
DISCORD_CLIENT_ID = os.environ.get('DISCORD_CLIENT_ID', '')
DISCORD_CLIENT_SECRET = os.environ.get('DISCORD_CLIENT_SECRET', '')
DISCORD_REDIRECT_URI = os.environ.get('DISCORD_REDIRECT_URI', '')
DISCORD_API_ENDPOINT = 'https://discord.com/api/v10'

@auth_bp.route('/connect-discord')
@login_required
def connect_discord():
    """Страница подключения Discord к аккаунту"""
    error = request.args.get('error')
    return render_template('connect_discord.html', error=error)

@auth_bp.route('/connect-telegram')
@login_required
def connect_telegram():
    """Страница подключения Telegram к аккаунту"""
    error = request.args.get('error')

    # Генерируем ссылку для подключения Telegram
    auth_link, auth_code = generate_telegram_auth_link(current_user.id)

    # Сохраняем код авторизации в сессии или БД
    session['telegram_auth_code'] = auth_code

    return render_template('connect_telegram.html', auth_link=auth_link, error=error)

@auth_bp.route('/auth/discord/state')
def auth_discord_state():
    """Генерация state для CSRF защиты при авторизации через Discord"""
    state = secrets.token_hex(16)
    session['discord_oauth_state'] = state
    return jsonify({'state': state})

@auth_bp.route('/auth/discord')
def auth_discord():
    """Начало процесса авторизации через Discord"""
    # Логирование попытки авторизации через Discord
    current_app.logger.info(f"Попытка авторизации через Discord: IP={request.remote_addr}")

    if not DISCORD_CLIENT_ID or not DISCORD_CLIENT_SECRET:
        current_app.logger.error("Отсутствуют настройки Discord OAuth (CLIENT_ID или CLIENT_SECRET)")
        flash('Авторизация через Discord временно недоступна. Пожалуйста, попробуйте позже.', 'error')
        return redirect(url_for('connect_discord', error='config_missing'))

    # Генерируем состояние для предотвращения CSRF
    state = secrets.token_hex(16)
    session['discord_oauth_state'] = state

    # Сохраняем информацию о том, что это подключение существующего аккаунта
    is_connect = current_user.is_authenticated
    session['is_discord_connect'] = is_connect

    # Логирование типа операции
    if is_connect:
        current_app.logger.info(f"Попытка связать Discord с существующим аккаунтом: user_id={current_user.id}, IP={request.remote_addr}")
    else:
        current_app.logger.info(f"Попытка входа/регистрации через Discord: IP={request.remote_addr}")

    # Авторизация через Discord
    auth_url = f"{DISCORD_API_ENDPOINT}/oauth2/authorize"
    params = {
        'client_id': DISCORD_CLIENT_ID,
        'redirect_uri': DISCORD_REDIRECT_URI,
        'response_type': 'code',
        'scope': 'identify email',
        'state': state,
        'prompt': 'consent'
    }

    # Формируем URL с параметрами
    query_string = '&'.join([f'{key}={value}' for key, value in params.items()])
    redirect_url = f"{auth_url}?{query_string}"

    return redirect(redirect_url)

@auth_bp.route('/auth/discord/callback')
def discord_callback():
    """Обработка колбэка от Discord OAuth2"""
    # Логирование получения колбэка
    current_app.logger.info(f"Получен колбэк от Discord: IP={request.remote_addr}")

    error = request.args.get('error')
    if error:
        current_app.logger.warning(f"Ошибка авторизации Discord: {error}, IP={request.remote_addr}")
        flash(f'Ошибка авторизации Discord: {error}', 'error')
        if session.get('is_discord_connect'):
            return redirect(url_for('connect_discord', error=error))
        return redirect(url_for('login'))

    # Проверка состояния для предотвращения CSRF
    state = request.args.get('state')
    stored_state = session.get('discord_oauth_state')
    if not state or state != stored_state:
        current_app.logger.warning(f"Неверное состояние авторизации Discord: получено={state}, ожидалось={stored_state}, IP={request.remote_addr}")
        flash('Неверное состояние авторизации. Пожалуйста, попробуйте снова.', 'error')
        return redirect(url_for('login'))

    code = request.args.get('code')
    if not code:
        current_app.logger.warning(f"Код авторизации Discord не получен, IP={request.remote_addr}")
        flash('Код авторизации не получен', 'error')
        return redirect(url_for('login'))

    # Обмениваем код на токен доступа
    data = {
        'client_id': DISCORD_CLIENT_ID,
        'client_secret': DISCORD_CLIENT_SECRET,
        'grant_type': 'authorization_code',
        'code': code,
        'redirect_uri': DISCORD_REDIRECT_URI
    }

    try:
        current_app.logger.info(f"Обмен кода на токен Discord: IP={request.remote_addr}")
        token_response = requests.post(f"{DISCORD_API_ENDPOINT}/oauth2/token", data=data, headers={
            'Content-Type': 'application/x-www-form-urlencoded'
        })
        token_data = token_response.json()

        if 'error' in token_data or 'access_token' not in token_data:
            error_msg = token_data.get('error', 'unknown_error')
            current_app.logger.warning(f"Ошибка при получении токена Discord: {error_msg}, IP={request.remote_addr}")
            flash('Ошибка при получении токена доступа Discord', 'error')
            if session.get('is_discord_connect'):
                return redirect(url_for('connect_discord', error='token_error'))
            return redirect(url_for('login'))

        # Получаем информацию о пользователе
        current_app.logger.info(f"Получение информации о пользователе Discord: IP={request.remote_addr}")
        user_response = requests.get(f"{DISCORD_API_ENDPOINT}/users/@me", headers={
            'Authorization': f"Bearer {token_data['access_token']}"
        })

        user_data = user_response.json()
        if 'id' not in user_data:
            error_msg = user_data.get('message', 'unknown_error')
            current_app.logger.warning(f"Не удалось получить информацию о пользователе Discord: {error_msg}, IP={request.remote_addr}")
            flash('Не удалось получить информацию о пользователе Discord', 'error')
            if session.get('is_discord_connect'):
                return redirect(url_for('connect_discord', error='user_info_error'))
            return redirect(url_for('login'))

        # Сохраняем Discord ID и информацию о пользователе
        discord_id = user_data['id']
        discord_username = user_data.get('username', '')
        discord_email = user_data.get('email')
        avatar_hash = user_data.get('avatar')

        # Формируем URL аватара
        avatar_url = None
        if avatar_hash:
            avatar_format = 'gif' if avatar_hash.startswith('a_') else 'png'
            avatar_url = f"https://cdn.discordapp.com/avatars/{discord_id}/{avatar_hash}.{avatar_format}?size=256"

        # Проверяем, существует ли пользователь с таким Discord ID
        existing_user = User.query.filter_by(discord_id=discord_id).first()

        # Если это процесс подключения аккаунта к существующему пользователю
        if session.get('is_discord_connect') and current_user.is_authenticated:
            current_app.logger.info(f"Попытка связать Discord с существующим аккаунтом: user_id={current_user.id}, discord_id={discord_id}, IP={request.remote_addr}")

            # Если Discord уже подключен к другому аккаунту
            if existing_user and existing_user.id != current_user.id:
                current_app.logger.warning(f"Discord уже связан с другим аккаунтом: user_id={current_user.id}, discord_id={discord_id}, existing_user_id={existing_user.id}, IP={request.remote_addr}")
                flash('Этот Discord аккаунт уже подключен к другому аккаунту на сайте', 'error')
                return redirect(url_for('connect_discord', error='already_linked'))

            # Обновляем информацию текущего пользователя
            current_user.discord_id = discord_id
            current_user.discord = discord_username
            current_user.avatar_url = avatar_url or current_user.avatar_url

            # Активируем аккаунт, если он не был активирован
            if not current_user.is_activated:
                current_user.is_activated = True

            db.session.commit()

            # Запись в аудит лог
            try:
                from playland.website.models import AuditLog
                audit_log = AuditLog(
                    action="connect_discord",
                    user_id=current_user.id,
                    ip_address=request.remote_addr,
                    details=f"Подключение Discord аккаунта: {discord_username} ({discord_id})"
                )
                db.session.add(audit_log)
                db.session.commit()
            except Exception as e:
                current_app.logger.error(f"Ошибка при записи в аудит лог: {str(e)}")

            current_app.logger.info(f"Discord успешно связан с аккаунтом: user_id={current_user.id}, discord_id={discord_id}, IP={request.remote_addr}")
            flash('Discord аккаунт успешно подключен!', 'success')
            return redirect(url_for('connect_discord'))

        # Процесс входа или регистрации
        if existing_user:
            # Вход существующего пользователя
            current_app.logger.info(f"Вход через Discord: user_id={existing_user.id}, discord_id={discord_id}, IP={request.remote_addr}")
            login_user(existing_user)

            # Обновляем last_seen
            existing_user.last_seen = datetime.now(datetime.timezone.utc)
            db.session.commit()

            # Запись в аудит лог
            try:
                from playland.website.models import AuditLog
                audit_log = AuditLog(
                    action="login_discord",
                    user_id=existing_user.id,
                    ip_address=request.remote_addr,
                    details=f"Вход через Discord"
                )
                db.session.add(audit_log)
                db.session.commit()
            except Exception as e:
                current_app.logger.error(f"Ошибка при записи в аудит лог: {str(e)}")

            flash('Вы успешно вошли через Discord!', 'success')
            return redirect(url_for('profile.view_profile'))
        else:
            # Если есть пользователь с таким email, привязываем Discord к этому аккаунту
            email_user = User.query.filter_by(email=discord_email).first() if discord_email else None

            if email_user:
                current_app.logger.info(f"Связывание Discord с существующим аккаунтом по email: user_id={email_user.id}, discord_id={discord_id}, email={discord_email}, IP={request.remote_addr}")
                email_user.discord_id = discord_id
                email_user.discord = discord_username
                email_user.avatar_url = avatar_url or email_user.avatar_url

                # Активируем аккаунт, если он не был активирован
                if not email_user.is_activated:
                    email_user.is_activated = True

                # Обновляем last_seen
                email_user.last_seen = datetime.now(datetime.timezone.utc)
                db.session.commit()

                # Запись в аудит лог
                try:
                    from playland.website.models import AuditLog
                    audit_log = AuditLog(
                        action="link_discord_by_email",
                        user_id=email_user.id,
                        ip_address=request.remote_addr,
                        details=f"Связывание Discord аккаунта по email: {discord_username} ({discord_id})"
                    )
                    db.session.add(audit_log)
                    db.session.commit()
                except Exception as e:
                    current_app.logger.error(f"Ошибка при записи в аудит лог: {str(e)}")

                login_user(email_user)
                flash('Ваш Discord аккаунт был привязан к существующему аккаунту!', 'success')
                return redirect(url_for('profile.view_profile'))

            # Создаем нового пользователя
            username = discord_username or f"user_{uuid.uuid4().hex[:8]}"

            # Проверяем уникальность username
            base_username = username
            counter = 1
            while User.query.filter_by(username=username).first():
                username = f"{base_username}_{counter}"
                counter += 1

            current_app.logger.info(f"Создание нового пользователя через Discord: username={username}, discord_id={discord_id}, IP={request.remote_addr}")

            new_user = User(
                username=username,
                email=discord_email or f"{username}@discord.playland.ru",
                password_hash=generate_password_hash(secrets.token_hex(16)),
                nickname=username,
                created_at=datetime.now(datetime.timezone.utc),
                discord_id=discord_id,
                discord=discord_username,
                avatar_url=avatar_url,
                is_activated=True  # Аккаунт активирован, так как подтвержден через Discord
            )

            db.session.add(new_user)
            db.session.commit()

            # Запись в аудит лог
            try:
                from playland.website.models import AuditLog
                audit_log = AuditLog(
                    action="register_discord",
                    user_id=new_user.id,
                    ip_address=request.remote_addr,
                    details=f"Регистрация через Discord: {discord_username} ({discord_id})"
                )
                db.session.add(audit_log)
                db.session.commit()
            except Exception as e:
                current_app.logger.error(f"Ошибка при записи в аудит лог: {str(e)}")

            login_user(new_user)
            current_app.logger.info(f"Успешная регистрация через Discord: user_id={new_user.id}, discord_id={discord_id}, IP={request.remote_addr}")
            flash('Аккаунт успешно создан через Discord!', 'success')
            return redirect(url_for('profile.view_profile'))

    except Exception as e:
        current_app.logger.error(f"Discord auth error: {str(e)}")
        flash('Произошла ошибка при авторизации через Discord. Пожалуйста, попробуйте позже.', 'error')
        if session.get('is_discord_connect'):
            return redirect(url_for('connect_discord', error='general_error'))
        return redirect(url_for('login'))

@auth_bp.route('/disconnect-discord')
@login_required
def disconnect_discord():
    """Отключение Discord от аккаунта"""
    current_app.logger.info(f"Попытка отключения Discord: user_id={current_user.id}, IP={request.remote_addr}")

    if not current_user.discord_id:
        current_app.logger.warning(f"Попытка отключения Discord, но аккаунт не подключен: user_id={current_user.id}, IP={request.remote_addr}")
        flash('Discord аккаунт не подключен', 'error')
        return redirect(url_for('connect_discord'))

    discord_id = current_user.discord_id
    discord_username = current_user.discord

    current_user.discord_id = None
    current_user.discord = None
    db.session.commit()

    # Запись в аудит лог
    try:
        from playland.website.models import AuditLog
        audit_log = AuditLog(
            action="disconnect_discord",
            user_id=current_user.id,
            ip_address=request.remote_addr,
            details=f"Отключение Discord аккаунта: {discord_username} ({discord_id})"
        )
        db.session.add(audit_log)
        db.session.commit()
    except Exception as e:
        current_app.logger.error(f"Ошибка при записи в аудит лог: {str(e)}")

    current_app.logger.info(f"Discord успешно отключен: user_id={current_user.id}, discord_id={discord_id}, IP={request.remote_addr}")
    flash('Discord аккаунт успешно отключен', 'success')
    return redirect(url_for('connect_discord'))

@auth_bp.route('/activate/<token>')
def activate_account(token):
    """Активация аккаунта по токену"""
    current_app.logger.info(f"Попытка активации аккаунта: token={token}, IP={request.remote_addr}")

    if not token:
        current_app.logger.warning(f"Попытка активации без токена: IP={request.remote_addr}")
        flash('Недействительный токен активации', 'error')
        return redirect(url_for('login'))

    # Ищем пользователя с таким токеном активации
    user = User.query.filter_by(activation_token=token).first()

    if not user:
        current_app.logger.warning(f"Попытка активации с недействительным токеном: token={token}, IP={request.remote_addr}")
        flash('Недействительный токен активации или аккаунт уже активирован', 'error')
        return redirect(url_for('login'))

    # Активируем аккаунт
    user.activate()
    db.session.commit()

    # Запись в аудит лог
    try:
        audit_log = AuditLog(
            action="activate_account",
            user_id=user.id,
            ip_address=request.remote_addr,
            details=f"Активация аккаунта по токену"
        )
        db.session.add(audit_log)
        db.session.commit()
    except Exception as e:
        current_app.logger.error(f"Ошибка при записи в аудит лог: {str(e)}")

    current_app.logger.info(f"Аккаунт успешно активирован: user_id={user.id}, IP={request.remote_addr}")
    flash('Ваш аккаунт успешно активирован! Теперь вы можете войти.', 'success')
    return redirect(url_for('login'))


@auth_bp.route('/auth/telegram/callback', methods=['POST'])
def telegram_callback():
    """Колбэк для Telegram авторизации (используется в боте)"""
    # Получаем данные из запроса
    data = request.get_json()

    current_app.logger.info(f"Получен колбэк от Telegram бота: IP={request.remote_addr}")

    if not data:
        current_app.logger.warning(f"Отсутствуют данные в запросе от Telegram бота: IP={request.remote_addr}")
        return jsonify({'success': False, 'error': 'Отсутствуют данные запроса'}), 400

    # Обрабатываем запрос с помощью функции из bot_integration.py
    success, message = process_telegram_auth(data)

    if success:
        current_app.logger.info(f"Успешная обработка запроса от Telegram бота: {message}, IP={request.remote_addr}")
        return jsonify({'success': True, 'message': message}), 200
    else:
        current_app.logger.warning(f"Ошибка обработки запроса от Telegram бота: {message}, IP={request.remote_addr}")
        return jsonify({'success': False, 'error': message}), 400

@auth_bp.route('/disconnect-telegram')
@login_required
def disconnect_telegram():
    """Отключение Telegram от аккаунта"""
    current_app.logger.info(f"Попытка отключения Telegram: user_id={current_user.id}, IP={request.remote_addr}")

    if not current_user.telegram_id:
        current_app.logger.warning(f"Попытка отключения Telegram, но аккаунт не подключен: user_id={current_user.id}, IP={request.remote_addr}")
        flash('Telegram аккаунт не подключен', 'error')
        return redirect(url_for('connect_telegram'))

    telegram_id = current_user.telegram_id
    telegram_username = current_user.telegram

    current_user.telegram_id = None
    current_user.telegram = None
    db.session.commit()

    # Запись в аудит лог
    try:
        from playland.website.models import AuditLog
        audit_log = AuditLog(
            action="disconnect_telegram",
            user_id=current_user.id,
            ip_address=request.remote_addr,
            details=f"Отключение Telegram аккаунта: {telegram_username} ({telegram_id})"
        )
        db.session.add(audit_log)
        db.session.commit()
    except Exception as e:
        current_app.logger.error(f"Ошибка при записи в аудит лог: {str(e)}")

    current_app.logger.info(f"Telegram успешно отключен: user_id={current_user.id}, telegram_id={telegram_id}, IP={request.remote_addr}")
    flash('Telegram аккаунт успешно отключен', 'success')
    return redirect(url_for('connect_telegram'))

# Новые маршруты для улучшенной безопасности

@auth_bp.route('/login', methods=['GET', 'POST'])
@rate_limit(limit=5, window=300, action="login")  # 5 попыток за 5 минут
def enhanced_login():
    """Улучшенная форма входа с защитой от брутфорса"""
    if current_user.is_authenticated:
        return redirect(url_for('index'))

    form = LoginForm()

    if form.validate_on_submit():
        username = form.username.data.strip()
        password = form.password.data

        # Валидация входных данных
        is_valid, error_msg = SecurityValidator.validate_username(username)
        if not is_valid:
            log_security_event("invalid_login_attempt", f"Invalid username format: {username}",
                             ip_address=request.remote_addr, severity="WARNING")
            flash('Неверные учетные данные', 'error')
            return render_template('auth/login.html', form=form)

        # Поиск пользователя
        user = User.query.filter_by(username=username).first() or User.query.filter_by(email=username).first()

        if not user:
            log_security_event("login_attempt_nonexistent_user", f"Login attempt for non-existent user: {username}",
                             ip_address=request.remote_addr, severity="WARNING")
            flash('Неверные учетные данные', 'error')
            return render_template('auth/login.html', form=form)

        # Проверка блокировки аккаунта
        if user.is_account_locked():
            log_security_event("login_attempt_locked_account", f"Login attempt for locked account: {user.id}",
                             user_id=user.id, ip_address=request.remote_addr, severity="HIGH")
            flash('Аккаунт временно заблокирован из-за множественных неудачных попыток входа', 'error')
            return render_template('auth/login.html', form=form)

        # Проверка пароля
        if not user.verify_password(password):
            user.increment_failed_login()
            db.session.commit()

            log_security_event("failed_login_attempt", f"Failed login for user: {user.id}",
                             user_id=user.id, ip_address=request.remote_addr, severity="WARNING")
            flash('Неверные учетные данные', 'error')
            return render_template('auth/login.html', form=form)

        # Проверка двухфакторной аутентификации
        if user.two_factor_enabled:
            session['pre_2fa_user_id'] = user.id
            return redirect(url_for('auth.two_factor'))

        # Успешный вход
        user.reset_failed_login_attempts()
        user.last_seen = datetime.now(datetime.timezone.utc)

        # Генерируем токен сессии
        user.session_token = secrets.token_hex(32)
        user.session_expires = datetime.now(datetime.timezone.utc) + timedelta(hours=24)
        session['session_token'] = user.session_token

        db.session.commit()

        login_user(user, remember=form.remember_me.data)

        log_security_event("successful_login", f"User {user.id} logged in successfully",
                         user_id=user.id, ip_address=request.remote_addr, severity="INFO")

        # Проверяем подозрительную активность
        check_suspicious_activity()

        next_page = request.args.get('next')
        return redirect(next_page or url_for('index'))

    return render_template('auth/login.html', form=form)

@auth_bp.route('/register', methods=['GET', 'POST'])
@rate_limit(limit=3, window=3600, action="register")  # 3 регистрации в час
def enhanced_register():
    """Улучшенная форма регистрации с валидацией"""
    if current_user.is_authenticated:
        return redirect(url_for('index'))

    form = RegisterForm()

    if form.validate_on_submit():
        username = form.username.data.strip()
        email = form.email.data.strip().lower()
        password = form.password.data

        # Валидация данных
        username_valid, username_error = SecurityValidator.validate_username(username)
        if not username_valid:
            flash(username_error, 'error')
            return render_template('auth/register.html', form=form)

        email_valid, email_error = SecurityValidator.validate_email(email)
        if not email_valid:
            flash(email_error, 'error')
            return render_template('auth/register.html', form=form)

        password_valid, password_error = SecurityValidator.validate_password(password)
        if not password_valid:
            flash(password_error, 'error')
            return render_template('auth/register.html', form=form)

        # Проверка уникальности
        if User.query.filter_by(username=username).first():
            flash('Пользователь с таким именем уже существует', 'error')
            return render_template('auth/register.html', form=form)

        if User.query.filter_by(email=email).first():
            flash('Пользователь с таким email уже существует', 'error')
            return render_template('auth/register.html', form=form)

        # Создание пользователя
        user = User(
            username=username,
            email=email,
            password_hash=generate_password_hash(password),
            created_at=datetime.now(datetime.timezone.utc),
            is_activated=False
        )

        # Генерируем токен подтверждения email
        user.generate_email_verification_token()

        db.session.add(user)
        db.session.commit()

        log_security_event("user_registered", f"New user registered: {user.id}",
                         user_id=user.id, ip_address=request.remote_addr, severity="INFO")

        # TODO: Отправить email подтверждения
        flash('Регистрация успешна! Проверьте email для подтверждения аккаунта.', 'success')
        return redirect(url_for('auth.enhanced_login'))

    return render_template('auth/register.html', form=form)

@auth_bp.route('/two-factor', methods=['GET', 'POST'])
@rate_limit(limit=5, window=300, action="2fa")
def two_factor():
    """Страница ввода кода двухфакторной аутентификации"""
    if current_user.is_authenticated:
        return redirect(url_for('index'))

    user_id = session.get('pre_2fa_user_id')
    if not user_id:
        return redirect(url_for('auth.enhanced_login'))

    user = User.query.get(user_id)
    if not user or not user.two_factor_enabled:
        session.pop('pre_2fa_user_id', None)
        return redirect(url_for('auth.enhanced_login'))

    form = TwoFactorForm()

    if form.validate_on_submit():
        token = form.token.data.strip()

        # Проверяем TOTP токен
        if user.verify_totp(token):
            # Успешная аутентификация
            session.pop('pre_2fa_user_id', None)

            user.reset_failed_login_attempts()
            user.last_seen = datetime.now(datetime.timezone.utc)

            # Генерируем токен сессии
            user.session_token = secrets.token_hex(32)
            user.session_expires = datetime.now(datetime.timezone.utc) + timedelta(hours=24)
            session['session_token'] = user.session_token

            db.session.commit()

            login_user(user, remember=form.remember_device.data)

            log_security_event("successful_2fa_login", f"User {user.id} completed 2FA login",
                             user_id=user.id, ip_address=request.remote_addr, severity="INFO")

            return redirect(url_for('index'))

        # Проверяем резервный код
        elif user.verify_backup_code(token):
            session.pop('pre_2fa_user_id', None)

            user.reset_failed_login_attempts()
            user.last_seen = datetime.now(datetime.timezone.utc)

            user.session_token = secrets.token_hex(32)
            user.session_expires = datetime.now(datetime.timezone.utc) + timedelta(hours=24)
            session['session_token'] = user.session_token

            db.session.commit()

            login_user(user, remember=form.remember_device.data)

            log_security_event("successful_backup_code_login", f"User {user.id} used backup code",
                             user_id=user.id, ip_address=request.remote_addr, severity="INFO")

            flash('Вы использовали резервный код. Рекомендуем сгенерировать новые коды.', 'warning')
            return redirect(url_for('profile.security_settings'))

        else:
            user.increment_failed_login()
            db.session.commit()

            log_security_event("failed_2fa_attempt", f"Failed 2FA attempt for user: {user.id}",
                             user_id=user.id, ip_address=request.remote_addr, severity="WARNING")

            flash('Неверный код аутентификации', 'error')

    return render_template('auth/two_factor.html', form=form)

@auth_bp.route('/reset-password-request', methods=['GET', 'POST'])
@rate_limit(limit=3, window=3600, action="password_reset_request")
def reset_password_request():
    """Запрос сброса пароля"""
    if current_user.is_authenticated:
        return redirect(url_for('index'))

    form = PasswordResetRequestForm()

    if form.validate_on_submit():
        email = form.email.data.strip().lower()
        user = User.query.filter_by(email=email).first()

        if user:
            token = user.generate_password_reset_token()
            db.session.commit()

            # TODO: Отправить email с токеном сброса
            log_security_event("password_reset_requested", f"Password reset requested for user: {user.id}",
                             user_id=user.id, ip_address=request.remote_addr, severity="INFO")

        # Всегда показываем одинаковое сообщение для безопасности
        flash('Если указанный email существует, на него будет отправлена ссылка для сброса пароля.', 'info')
        return redirect(url_for('auth.enhanced_login'))

    return render_template('auth/reset_password_request.html', form=form)

@auth_bp.route('/reset-password/<token>', methods=['GET', 'POST'])
@rate_limit(limit=5, window=3600, action="password_reset")
def reset_password(token):
    """Сброс пароля по токену"""
    if current_user.is_authenticated:
        return redirect(url_for('index'))

    user = User.query.filter_by(password_reset_token=token).first()

    if not user or not user.verify_password_reset_token(token):
        flash('Недействительная или истекшая ссылка для сброса пароля.', 'error')
        return redirect(url_for('auth.reset_password_request'))

    form = PasswordResetForm()

    if form.validate_on_submit():
        password = form.password.data

        # Валидация пароля
        password_valid, password_error = SecurityValidator.validate_password(password)
        if not password_valid:
            flash(password_error, 'error')
            return render_template('auth/reset_password.html', form=form)

        # Устанавливаем новый пароль
        user.password_hash = generate_password_hash(password)
        user.clear_password_reset_token()
        user.reset_failed_login_attempts()

        db.session.commit()

        log_security_event("password_reset_completed", f"Password reset completed for user: {user.id}",
                         user_id=user.id, ip_address=request.remote_addr, severity="INFO")

        flash('Пароль успешно изменен. Теперь вы можете войти с новым паролем.', 'success')
        return redirect(url_for('auth.enhanced_login'))

    return render_template('auth/reset_password.html', form=form)

@auth_bp.route('/verify-email')
@login_required
def verify_email():
    """Страница подтверждения email"""
    if current_user.email_verified:
        flash('Ваш email уже подтвержден.', 'info')
        return redirect(url_for('index'))

    form = EmailVerificationForm()
    return render_template('auth/verify_email.html', form=form)

@auth_bp.route('/send-verification-email', methods=['POST'])
@login_required
@rate_limit(limit=3, window=3600, action="email_verification")
def send_verification_email():
    """Отправка письма подтверждения email"""
    if current_user.email_verified:
        flash('Ваш email уже подтвержден.', 'info')
        return redirect(url_for('index'))

    token = current_user.generate_email_verification_token()
    db.session.commit()

    # TODO: Отправить email с токеном подтверждения
    log_security_event("email_verification_sent", f"Email verification sent to user: {current_user.id}",
                     user_id=current_user.id, ip_address=request.remote_addr, severity="INFO")

    flash('Письмо подтверждения отправлено на ваш email.', 'success')
    return redirect(url_for('auth.verify_email'))

@auth_bp.route('/confirm-email/<token>')
def confirm_email(token):
    """Подтверждение email по токену"""
    user = User.query.filter_by(email_verification_token=token).first()

    if not user or not user.verify_email_token(token):
        flash('Недействительная ссылка подтверждения.', 'error')
        return redirect(url_for('index'))

    user.confirm_email()
    db.session.commit()

    log_security_event("email_verified", f"Email verified for user: {user.id}",
                     user_id=user.id, ip_address=request.remote_addr, severity="INFO")

    flash('Email успешно подтвержден!', 'success')
    return redirect(url_for('index'))

@auth_bp.route('/logout')
@login_required
def enhanced_logout():
    """Улучшенный выход с очисткой сессии"""
    user_id = current_user.id

    # Очищаем токен сессии
    current_user.session_token = None
    current_user.session_expires = None
    db.session.commit()

    # Очищаем сессию
    session.clear()

    logout_user()

    log_security_event("user_logout", f"User {user_id} logged out",
                     user_id=user_id, ip_address=request.remote_addr, severity="INFO")

    flash('Вы успешно вышли из системы.', 'success')
    return redirect(url_for('index'))