{% extends "base.html" %}

{% block title %}PLAYLAND - Требуется авторизация{% endblock %}

{% block namespace %}error401{% endblock %}

{% block extra_head %}
<style>
    :root {
        --pixel-font: 'Press Start 2P', cursive;
        --main-font: 'Roboto', sans-serif;
        --glow-blue: #0088ff;
        --bright-blue: #44aaff;
        --dark-bg: #121212;
    }

    .error-page {
        min-height: 100vh;
        background-image: url("{{ url_for('static', filename='images/grass_background.jpg') }}");
        background-size: cover;
        background-position: center center;
        background-attachment: fixed;
        position: relative;
        overflow: hidden;
    }

    .error-page::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        backdrop-filter: blur(2px);
        z-index: 1;
    }

    .error-container {
        position: relative;
        z-index: 2;
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
        padding-top: 100px;
    }

    .error-card {
        background: rgba(26, 26, 26, 0.95);
        border: 2px solid var(--glow-blue);
        border-radius: 15px;
        padding: 60px 50px;
        width: 100%;
        max-width: 600px;
        text-align: center;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5), 0 0 30px rgba(0, 136, 255, 0.2);
        position: relative;
        overflow: hidden;
        animation: cardSlideIn 0.8s cubic-bezier(0.68, -0.55, 0.27, 1.55) forwards;
        opacity: 0;
        transform: translateY(50px);
    }

    @keyframes cardSlideIn {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .error-card::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        border: 2px solid transparent;
        border-radius: 15px;
        background: linear-gradient(45deg, var(--glow-blue), transparent, var(--glow-blue));
        background-size: 400% 400%;
        opacity: 0.3;
        z-index: -1;
        animation: borderGlow 3s ease infinite;
    }

    @keyframes borderGlow {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    .error-icon {
        font-size: 6rem;
        color: var(--glow-blue);
        margin-bottom: 30px;
        text-shadow: 0 0 20px rgba(0, 136, 255, 0.5);
        animation: iconBounce 2s ease-in-out infinite;
    }

    @keyframes iconBounce {
        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-10px); }
        60% { transform: translateY(-5px); }
    }

    .error-code {
        font-family: var(--pixel-font);
        font-size: 4rem;
        color: var(--glow-blue);
        margin-bottom: 20px;
        text-shadow: 0 0 15px rgba(0, 136, 255, 0.5);
        animation: codeGlow 2s ease-in-out infinite alternate;
    }

    @keyframes codeGlow {
        from { text-shadow: 0 0 10px rgba(0, 136, 255, 0.5); }
        to { text-shadow: 0 0 30px var(--glow-blue), 0 0 40px var(--glow-blue); }
    }

    .error-title {
        font-family: var(--pixel-font);
        font-size: 1.5rem;
        color: var(--glow-blue);
        margin-bottom: 25px;
        text-shadow: 0 0 10px rgba(0, 136, 255, 0.3);
    }

    .error-message {
        color: #f0f0f0;
        font-size: 1.1rem;
        margin-bottom: 40px;
        line-height: 1.6;
        font-family: var(--main-font);
    }

    .error-actions {
        display: flex;
        gap: 20px;
        justify-content: center;
        flex-wrap: wrap;
        margin-bottom: 40px;
    }

    .btn-minecraft {
        display: inline-block;
        padding: 18px 35px;
        background-color: #00ff00;
        color: #000;
        text-decoration: none;
        font-family: var(--pixel-font);
        font-size: 0.9em;
        text-transform: uppercase;
        border: none;
        position: relative;
        overflow: hidden;
        transition: all 0.3s;
        box-shadow: 0 4px 0 #006400, 0 0 20px rgba(0, 255, 0, 0.4);
        border-radius: 5px;
        cursor: pointer;
        letter-spacing: 1px;
    }

    .btn-minecraft:hover {
        transform: translateY(-3px);
        box-shadow: 0 7px 0 #006400, 0 0 30px rgba(0, 255, 0, 0.6);
        text-decoration: none;
        color: #000;
    }

    .btn-outline {
        background-color: transparent;
        border: 2px solid var(--glow-blue);
        color: var(--glow-blue);
        box-shadow: 0 4px 0 rgba(0, 136, 255, 0.3), 0 0 20px rgba(0, 136, 255, 0.2);
        padding: 16px 33px;
        font-family: var(--pixel-font);
        font-size: 0.9em;
        text-transform: uppercase;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 5px;
        cursor: pointer;
        letter-spacing: 1px;
        transition: all 0.3s;
    }

    .btn-outline:hover {
        box-shadow: 0 7px 0 rgba(0, 136, 255, 0.3), 0 0 30px rgba(0, 136, 255, 0.4);
        background-color: rgba(0, 136, 255, 0.1);
        color: var(--glow-blue);
        text-decoration: none;
        transform: translateY(-3px);
    }

    .auth-benefits {
        background: rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(0, 136, 255, 0.3);
        border-radius: 10px;
        padding: 25px;
        text-align: left;
    }

    .auth-benefits h4 {
        font-family: var(--pixel-font);
        font-size: 1rem;
        color: var(--glow-blue);
        margin-bottom: 15px;
        text-align: center;
    }

    .auth-benefits ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .auth-benefits li {
        color: #f0f0f0;
        margin-bottom: 10px;
        padding-left: 25px;
        position: relative;
        font-family: var(--main-font);
    }

    .auth-benefits li::before {
        content: '🔐';
        position: absolute;
        left: 0;
        font-size: 1.1rem;
    }

    /* Частицы */
    .particles-container {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        z-index: 0;
    }

    .particle {
        position: absolute;
        opacity: 0;
        animation: floatParticle linear infinite;
    }

    @keyframes floatParticle {
        0% {
            transform: translateY(100vh) translateX(0);
            opacity: 0;
        }
        10% {
            opacity: 0.7;
        }
        90% {
            opacity: 0.7;
        }
        100% {
            transform: translateY(-20vh) translateX(calc(var(--rand-x, 0) * 40vw - 20vw));
            opacity: 0;
        }
    }

    /* Адаптивность */
    @media (max-width: 768px) {
        .error-container {
            padding: 15px;
            padding-top: 80px;
        }
        
        .error-card {
            padding: 40px 30px;
            max-width: 100%;
        }
        
        .error-code {
            font-size: 3rem;
        }
        
        .error-title {
            font-size: 1.2rem;
        }
        
        .btn-minecraft, .btn-outline {
            padding: 15px 25px;
            font-size: 0.8em;
        }
        
        .error-actions {
            flex-direction: column;
            align-items: center;
        }
        
        .error-icon {
            font-size: 4rem;
        }
    }

    @media (max-width: 480px) {
        .error-card {
            padding: 30px 20px;
        }
        
        .error-code {
            font-size: 2.5rem;
        }
        
        .error-title {
            font-size: 1rem;
        }
        
        .error-message {
            font-size: 1rem;
        }
        
        .btn-minecraft, .btn-outline {
            padding: 12px 20px;
            font-size: 0.7em;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="error-page">
    <div class="particles-container">
        <!-- Частицы будут добавлены через JavaScript -->
    </div>
    
    <div class="error-container">
        <div class="error-card">
            <div class="error-icon">
                <i class="fas fa-user-lock"></i>
            </div>
            
            <div class="error-code">401</div>
            
            <div class="error-title">Требуется авторизация</div>
            
            <div class="error-message">
                <p>Для доступа к этой странице необходимо войти в систему.</p>
                <p>Пожалуйста, авторизуйтесь или зарегистрируйтесь, если у вас еще нет аккаунта.</p>
            </div>
            
            <div class="error-actions">
                <a href="{{ url_for('login') }}" class="btn-minecraft">Войти</a>
                <a href="{{ url_for('register') }}" class="btn-outline">Регистрация</a>
            </div>
            
            <div class="auth-benefits">
                <h4>Преимущества авторизации</h4>
                <ul>
                    <li>Подача заявок на сервер</li>
                    <li>Отслеживание статуса заявки</li>
                    <li>Доступ к системе поддержки</li>
                    <li>Персональные настройки</li>
                    <li>Участие в сообществе</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Создание частиц (синие для 401)
    function createParticles() {
        const container = document.querySelector('.particles-container');
        if (!container) return;
        
        const particleCount = window.innerWidth < 768 ? 15 : 30;
        
        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.innerHTML = '⬛';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
            particle.style.animationDelay = Math.random() * 10 + 's';
            particle.style.fontSize = (Math.random() * 10 + 10) + 'px';
            particle.style.color = `rgba(0, 136, 255, ${Math.random() * 0.5 + 0.2})`;
            particle.style.setProperty('--rand-x', Math.random());
            
            container.appendChild(particle);
        }
    }
    
    createParticles();
});
</script>
{% endblock %}
