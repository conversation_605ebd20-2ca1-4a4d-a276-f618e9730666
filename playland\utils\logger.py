#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Настройка логирования в проекте PlayLand.
"""

import os
import sys
import logging
import logging.config
from pathlib import Path

def setup_logger(name, log_file=None, level=logging.INFO):
    """
    Настраивает и возвращает логгер с указанным именем.
    
    Args:
        name (str): Имя логгера.
        log_file (str, optional): Путь к файлу логов.
        level (int, optional): Уровень логирования.
    
    Returns:
        logging.Logger: Настроенный логгер.
    """
    # Создаем логгер
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # Создаем форматтер
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # Создаем обработчик для вывода в консоль
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    
    # Добавляем обработчик в логгер
    logger.addHandler(console_handler)
    
    # Если указан файл логов, создаем обработчик для вывода в файл
    if log_file:
        # Создаем директорию для файла логов, если она не существует
        log_dir = os.path.dirname(log_file)
        if log_dir:
            os.makedirs(log_dir, exist_ok=True)
        
        # Создаем обработчик для вывода в файл
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        
        # Добавляем обработчик в логгер
        logger.addHandler(file_handler)
    
    return logger

def setup_logging(config=None, logs_dir=None):
    """
    Настраивает логирование для всего проекта.
    
    Args:
        config (dict, optional): Конфигурация логирования.
        logs_dir (str, optional): Путь к директории с логами.
    """
    # Если директория с логами не указана, используем директорию logs в корне проекта
    if logs_dir is None:
        base_dir = Path(__file__).resolve().parent.parent.parent
        logs_dir = base_dir / 'logs'
    
    # Создаем директорию для логов, если она не существует
    os.makedirs(logs_dir, exist_ok=True)
    
    # Если конфигурация не указана, используем стандартную конфигурацию
    if config is None:
        config = {
            'version': 1,
            'disable_existing_loggers': False,
            'formatters': {
                'standard': {
                    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                },
            },
            'handlers': {
                'console': {
                    'level': 'INFO',
                    'class': 'logging.StreamHandler',
                    'formatter': 'standard',
                    'stream': 'ext://sys.stdout'
                },
                'file': {
                    'level': 'INFO',
                    'class': 'logging.FileHandler',
                    'formatter': 'standard',
                    'filename': os.path.join(logs_dir, 'playland.log')
                },
                'error_file': {
                    'level': 'ERROR',
                    'class': 'logging.FileHandler',
                    'formatter': 'standard',
                    'filename': os.path.join(logs_dir, 'error.log')
                }
            },
            'loggers': {
                '': {
                    'handlers': ['console', 'file', 'error_file'],
                    'level': 'INFO',
                    'propagate': True
                },
                'playland': {
                    'handlers': ['console', 'file', 'error_file'],
                    'level': 'INFO',
                    'propagate': False
                },
                'playland.website': {
                    'handlers': ['console', 'file', 'error_file'],
                    'level': 'INFO',
                    'propagate': False
                },
                'playland.tg_bot': {
                    'handlers': ['console', 'file', 'error_file'],
                    'level': 'INFO',
                    'propagate': False
                },
                'playland.discord_bot': {
                    'handlers': ['console', 'file', 'error_file'],
                    'level': 'INFO',
                    'propagate': False
                }
            }
        }
    
    # Настраиваем логирование
    logging.config.dictConfig(config)
    
    # Возвращаем корневой логгер
    return logging.getLogger('playland')

def get_logger(name):
    """
    Возвращает логгер с указанным именем.
    
    Args:
        name (str): Имя логгера.
    
    Returns:
        logging.Logger: Логгер с указанным именем.
    """
    return logging.getLogger(name)
