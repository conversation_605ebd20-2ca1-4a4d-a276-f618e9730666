#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sqlite3
import os

def check_database():
    """Проверка структуры базы данных"""
    db_path = os.path.join('instance', 'playland.db')
    
    if not os.path.exists(db_path):
        print(f"Файл базы данных не найден: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Получаем список таблиц
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print("Таблицы в базе данных:")
        for table in tables:
            print(f"- {table[0]}")
        
        # Проверяем наличие таблицы пользователей
        user_tables = [t[0] for t in tables if t[0].lower() in ('users', 'user', 'пользователи')]
        
        if user_tables:
            for user_table in user_tables:
                print(f"\nСтруктура таблицы {user_table}:")
                cursor.execute(f"PRAGMA table_info({user_table})")
                columns = cursor.fetchall()
                for col in columns:
                    print(f"- {col[1]} ({col[2]})")
        else:
            print("\nТаблица пользователей не найдена.")
        
        conn.close()
    except Exception as e:
        print(f"Ошибка при проверке базы данных: {e}")

if __name__ == "__main__":
    check_database()
 