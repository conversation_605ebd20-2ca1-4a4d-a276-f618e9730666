<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Админ-панель | PLAYLAND</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&display=swap" rel="stylesheet">
    <style>
        .admin-panel {
            padding: 60px 0;
        }
        
        .admin-card {
            background-color: var(--card-bg-color);
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
        
        .admin-card h3 {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .admin-menu {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .admin-menu a {
            background-color: var(--primary-color);
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .admin-menu a:hover {
            background-color: var(--secondary-color);
        }
        
        .application-list {
            margin-top: 20px;
        }
        
        .application-item {
            padding: 15px;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: var(--bg-color);
        }
        
        .application-item:hover {
            background-color: #f0f0f0;
        }
        
        .application-info {
            flex: 1;
        }
        
        .application-actions {
            margin-left: 20px;
        }
        
        .status-pending {
            color: #ff9800;
            font-weight: 600;
        }
        
        .status-approved {
            color: #4caf50;
            font-weight: 600;
        }
        
        .status-rejected {
            color: #f44336;
            font-weight: 600;
        }
        
        .dashboard-stats {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background-color: var(--bg-color);
            border-radius: 5px;
            padding: 20px;
            flex: 1;
            min-width: 200px;
            text-align: center;
            border-left: 4px solid var(--primary-color);
        }
        
        .stat-card.pending {
            border-left-color: #ff9800;
        }
        
        .stat-card.approved {
            border-left-color: #4caf50;
        }
        
        .stat-card.rejected {
            border-left-color: #f44336;
        }
        
        .stat-card h4 {
            margin-bottom: 10px;
            color: #555;
        }
        
        .stat-card .stat-number {
            font-size: 32px;
            font-weight: 700;
        }
        
        .view-all {
            display: block;
            text-align: center;
            margin-top: 20px;
            color: var(--primary-color);
            font-weight: 600;
        }
        
        /* Стили для управления галереей */
        .gallery-add-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }

        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
        }

        .form-group {
            flex: 1;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .posts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .post-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            background: white;
        }

        .post-thumbnail {
            width: 100%;
            height: 150px;
            object-fit: cover;
        }

        .post-info {
            padding: 15px;
        }

        .post-info h5 {
            margin: 0 0 10px 0;
            color: #333;
        }

        .post-info p {
            margin: 5px 0;
            font-size: 14px;
            color: #666;
        }

        .post-actions {
            padding: 15px;
            border-top: 1px solid #eee;
            text-align: right;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
        }

        @media (max-width: 768px) {
            .dashboard-stats {
                flex-direction: column;
                gap: 10px;
            }

            .admin-menu {
                flex-direction: column;
            }

            .form-row {
                flex-direction: column;
                gap: 10px;
            }

            .posts-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <h1>PLAYLAND</h1>
                <p>Ванильный Minecraft сервер</p>
            </div>
            <nav>
                <ul>
                    <li><a href="{{ url_for('index') }}">Главная</a></li>
                    <li><a href="{{ url_for('application') }}">Подать заявку</a></li>
                    {% if current_user.is_authenticated %}
                        <li><a href="{{ url_for('profile') }}">Профиль</a></li>
                        {% if current_user.is_admin %}
                            <li><a href="{{ url_for('admin') }}" class="active">Админ-панель</a></li>
                        {% endif %}
                        <li><a href="{{ url_for('logout') }}">Выйти</a></li>
                    {% else %}
                        <li><a href="{{ url_for('login') }}">Войти</a></li>
                        <li><a href="{{ url_for('register') }}">Регистрация</a></li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </header>

    <section class="admin-panel">
        <div class="container">
            <h2>Панель администратора</h2>
            
            <div class="admin-menu">
                <a href="{{ url_for('admin') }}">Обзор</a>
                <a href="#gallery-section">Управление галереей</a>
                <a href="#applications-section">Заявки</a>
            </div>
            
            <div class="admin-card">
                <h3>Статистика</h3>
                
                <div class="dashboard-stats">
                    <div class="stat-card pending">
                        <h4>В ожидании</h4>
                        <div class="stat-number">{{ pending_applications|length }}</div>
                    </div>
                    <div class="stat-card approved">
                        <h4>Одобрено</h4>
                        <div class="stat-number">{{ approved_count if approved_count is defined else 0 }}</div>
                    </div>
                    <div class="stat-card rejected">
                        <h4>Отклонено</h4>
                        <div class="stat-number">{{ rejected_count if rejected_count is defined else 0 }}</div>
                    </div>
                    <div class="stat-card">
                        <h4>Всего заявок</h4>
                        <div class="stat-number">{{ total_count if total_count is defined else 0 }}</div>
                    </div>
                </div>
            </div>

            <!-- Секция управления галереей -->
            <div class="admin-card" id="gallery-section">
                <h3>Управление галереей ({{ gallery_posts|length }} постов)</h3>

                <!-- Форма добавления поста -->
                <div class="gallery-add-form">
                    <h4>Добавить новый пост</h4>
                    <form id="addGalleryForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="postTitle">Название:</label>
                                <input type="text" id="postTitle" name="title" required>
                            </div>
                            <div class="form-group">
                                <label for="postAuthor">Автор:</label>
                                <input type="text" id="postAuthor" name="author" value="Администратор">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="postCategory">Категория:</label>
                                <select id="postCategory" name="category">
                                    <option value="builds">Постройки</option>
                                    <option value="events">События</option>
                                    <option value="landscape">Ландшафты</option>
                                    <option value="players">Игроки</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="postImageUrl">URL изображения:</label>
                                <input type="url" id="postImageUrl" name="image_url" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="postDescription">Описание:</label>
                            <textarea id="postDescription" name="description" rows="3"></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">Добавить пост</button>
                    </form>
                </div>

                <!-- Список постов -->
                <div class="gallery-posts-list">
                    <h4>Текущие посты</h4>
                    {% if gallery_posts %}
                        <div class="posts-grid">
                            {% for post in gallery_posts %}
                            <div class="post-item" data-post-id="{{ post.id }}">
                                <img src="{{ post.image_url }}" alt="{{ post.title }}" class="post-thumbnail">
                                <div class="post-info">
                                    <h5>{{ post.title }}</h5>
                                    <p><strong>Автор:</strong> {{ post.author }}</p>
                                    <p><strong>Категория:</strong> {{ post.category }}</p>
                                    <p><strong>Дата:</strong> {{ post.created_at }}</p>
                                    {% if post.description %}
                                        <p><strong>Описание:</strong> {{ post.description }}</p>
                                    {% endif %}
                                </div>
                                <div class="post-actions">
                                    <button class="btn btn-danger btn-sm" onclick="deletePost('{{ post.id }}')">Удалить</button>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p>В галерее пока нет постов.</p>
                    {% endif %}
                </div>
            </div>

            <div class="admin-card" id="applications-section">
                <h3>Заявки, ожидающие рассмотрения (0)</h3>
                
                {% if pending_applications %}
                    <div class="application-list">
                        {% for app in pending_applications %}
                            <div class="application-item">
                                <div class="application-info">
                                    <p><strong>Никнейм:</strong> {{ app.nickname }}</p>
                                    <p><strong>Discord:</strong> {{ app.discord or 'Не указан' }}</p>
                                    <p><strong>Дата подачи:</strong> {{ app.timestamp.strftime('%d.%m.%Y %H:%M') if app.timestamp else 'Неизвестно' }}</p>
                                </div>
                                <div class="application-actions">
                                    <button class="btn btn-primary" disabled>Рассмотреть (Demo)</button>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                    
                    {% if pending_applications|length > 5 %}
                        <p class="view-all">Показать все заявки (Demo)</p>
                    {% endif %}
                {% else %}
                    <p>Нет заявок, ожидающих рассмотрения.</p>
                {% endif %}
            </div>
            
            <div class="admin-card">
                <h3>Последние заявки</h3>
                
                {% if recent_applications %}
                    <div class="application-list">
                        {% for app in recent_applications %}
                            <div class="application-item">
                                <div class="application-info">
                                    <p><strong>Никнейм:</strong> {{ app.nickname }}</p>
                                    <p><strong>Статус:</strong> 
                                        <span class="status-{{ app.status }}">
                                            {% if app.status == 'pending' %}
                                                В ожидании
                                            {% elif app.status == 'approved' %}
                                                Одобрена
                                            {% elif app.status == 'rejected' %}
                                                Отклонена
                                            {% else %}
                                                {{ app.status }}
                                            {% endif %}
                                        </span>
                                    </p>
                                    <p><strong>Дата подачи:</strong> {{ app.timestamp.strftime('%d.%m.%Y %H:%M') if app.timestamp else 'Неизвестно' }}</p>
                                </div>
                                <div class="application-actions">
                                    <button class="btn btn-secondary" disabled>Подробнее (Demo)</button>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                    
                    <p class="view-all">Показать все заявки (Demo)</p>
                {% else %}
                    <p>Нет заявок в системе.</p>
                {% endif %}
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h2>PLAYLAND</h2>
                    <p>© 2023-2024 Все права защищены</p>
                </div>
                <div class="footer-links">
                    <h3>Полезные ссылки</h3>
                    <ul>
                        <li><a href="#">Правила сервера</a></li>
                        <li><a href="#">Контакты</a></li>
                        <li><a href="https://t.me/playland_bot" target="_blank">Telegram Бот</a></li>
                        <li><a href="#" target="_blank">Discord сервер</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </footer>

    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    <script>
        // Функционал для управления галереей
        document.addEventListener('DOMContentLoaded', function() {
            const addForm = document.getElementById('addGalleryForm');

            if (addForm) {
                addForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const formData = new FormData(addForm);
                    const data = {
                        title: formData.get('title'),
                        author: formData.get('author'),
                        category: formData.get('category'),
                        image_url: formData.get('image_url'),
                        description: formData.get('description')
                    };

                    fetch('/api/gallery/add', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(data)
                    })
                    .then(response => response.json())
                    .then(result => {
                        if (result.success) {
                            alert('Пост успешно добавлен в галерею!');
                            location.reload(); // Перезагружаем страницу для обновления списка
                        } else {
                            alert('Ошибка при добавлении поста: ' + result.error);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Произошла ошибка при добавлении поста');
                    });
                });
            }
        });

        // Функция удаления поста
        function deletePost(postId) {
            if (confirm('Вы уверены, что хотите удалить этот пост?')) {
                fetch('/api/gallery/delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ post_id: postId })
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        alert('Пост успешно удален!');
                        location.reload(); // Перезагружаем страницу для обновления списка
                    } else {
                        alert('Ошибка при удалении поста: ' + result.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Произошла ошибка при удалении поста');
                });
            }
        }
    </script>
</body>
</html>