{% extends "base.html" %}

{% block title %}PLAYLAND - Внутренняя ошибка сервера{% endblock %}

{% block namespace %}error500{% endblock %}

{% block extra_head %}
<style>
    :root {
        --pixel-font: 'Press Start 2P', cursive;
        --main-font: 'Roboto', sans-serif;
        --glow-orange: #ff8800;
        --bright-orange: #ffaa44;
        --dark-bg: #121212;
    }

    .error-page {
        min-height: 100vh;
        background-image: url("{{ url_for('static', filename='images/grass_background.jpg') }}");
        background-size: cover;
        background-position: center center;
        background-attachment: fixed;
        position: relative;
        overflow: hidden;
    }

    .error-page::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        backdrop-filter: blur(2px);
        z-index: 1;
    }

    .error-container {
        position: relative;
        z-index: 2;
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
        padding-top: 100px;
    }

    .error-card {
        background: rgba(26, 26, 26, 0.95);
        border: 2px solid var(--glow-orange);
        border-radius: 15px;
        padding: 60px 50px;
        width: 100%;
        max-width: 700px;
        text-align: center;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5), 0 0 30px rgba(255, 136, 0, 0.2);
        position: relative;
        overflow: hidden;
        animation: cardSlideIn 0.8s cubic-bezier(0.68, -0.55, 0.27, 1.55) forwards;
        opacity: 0;
        transform: translateY(50px);
    }

    @keyframes cardSlideIn {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .error-card::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        border: 2px solid transparent;
        border-radius: 15px;
        background: linear-gradient(45deg, var(--glow-orange), transparent, var(--glow-orange));
        background-size: 400% 400%;
        opacity: 0.3;
        z-index: -1;
        animation: borderGlow 3s ease infinite;
    }

    @keyframes borderGlow {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    .error-icon {
        font-size: 6rem;
        color: var(--glow-orange);
        margin-bottom: 30px;
        text-shadow: 0 0 20px rgba(255, 136, 0, 0.5);
        animation: iconShake 1s ease-in-out infinite;
    }

    @keyframes iconShake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }

    .error-code {
        font-family: var(--pixel-font);
        font-size: 4rem;
        color: var(--glow-orange);
        margin-bottom: 20px;
        text-shadow: 0 0 15px rgba(255, 136, 0, 0.5);
        animation: codeGlow 2s ease-in-out infinite alternate;
    }

    @keyframes codeGlow {
        from { text-shadow: 0 0 10px rgba(255, 136, 0, 0.5); }
        to { text-shadow: 0 0 30px var(--glow-orange), 0 0 40px var(--glow-orange); }
    }

    .error-title {
        font-family: var(--pixel-font);
        font-size: 1.5rem;
        color: var(--glow-orange);
        margin-bottom: 25px;
        text-shadow: 0 0 10px rgba(255, 136, 0, 0.3);
    }

    .error-message {
        color: #f0f0f0;
        font-size: 1.1rem;
        margin-bottom: 40px;
        line-height: 1.6;
        font-family: var(--main-font);
    }

    .error-actions {
        display: flex;
        gap: 20px;
        justify-content: center;
        flex-wrap: wrap;
        margin-bottom: 40px;
    }

    .btn-minecraft {
        display: inline-block;
        padding: 18px 35px;
        background-color: #00ff00;
        color: #000;
        text-decoration: none;
        font-family: var(--pixel-font);
        font-size: 0.9em;
        text-transform: uppercase;
        border: none;
        position: relative;
        overflow: hidden;
        transition: all 0.3s;
        box-shadow: 0 4px 0 #006400, 0 0 20px rgba(0, 255, 0, 0.4);
        border-radius: 5px;
        cursor: pointer;
        letter-spacing: 1px;
    }

    .btn-minecraft:hover {
        transform: translateY(-3px);
        box-shadow: 0 7px 0 #006400, 0 0 30px rgba(0, 255, 0, 0.6);
        text-decoration: none;
        color: #000;
    }

    .btn-outline {
        background-color: transparent;
        border: 2px solid var(--glow-orange);
        color: var(--glow-orange);
        box-shadow: 0 4px 0 rgba(255, 136, 0, 0.3), 0 0 20px rgba(255, 136, 0, 0.2);
        padding: 16px 33px;
        font-family: var(--pixel-font);
        font-size: 0.9em;
        text-transform: uppercase;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 5px;
        cursor: pointer;
        letter-spacing: 1px;
        transition: all 0.3s;
    }

    .btn-outline:hover {
        box-shadow: 0 7px 0 rgba(255, 136, 0, 0.3), 0 0 30px rgba(255, 136, 0, 0.4);
        background-color: rgba(255, 136, 0, 0.1);
        color: var(--glow-orange);
        text-decoration: none;
        transform: translateY(-3px);
    }

    .error-details {
        background: rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(255, 136, 0, 0.3);
        border-radius: 10px;
        padding: 25px;
        text-align: left;
        margin-top: 30px;
    }

    .error-details h4 {
        font-family: var(--pixel-font);
        font-size: 1rem;
        color: var(--glow-orange);
        margin-bottom: 15px;
        text-align: center;
    }

    .error-details p {
        color: #f0f0f0;
        margin: 8px 0;
        font-family: 'Roboto Mono', monospace;
        font-size: 0.9rem;
    }

    .error-details p strong {
        color: var(--glow-orange);
        margin-right: 10px;
    }

    .error-status {
        background: rgba(255, 136, 0, 0.1);
        border: 1px solid rgba(255, 136, 0, 0.3);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
        text-align: center;
    }

    .error-status p {
        color: var(--glow-orange);
        font-family: var(--pixel-font);
        font-size: 0.8rem;
        margin: 0;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    /* Частицы */
    .particles-container {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        z-index: 0;
    }

    .particle {
        position: absolute;
        opacity: 0;
        animation: floatParticle linear infinite;
    }

    @keyframes floatParticle {
        0% {
            transform: translateY(100vh) translateX(0);
            opacity: 0;
        }
        10% {
            opacity: 0.7;
        }
        90% {
            opacity: 0.7;
        }
        100% {
            transform: translateY(-20vh) translateX(calc(var(--rand-x, 0) * 40vw - 20vw));
            opacity: 0;
        }
    }

    /* Адаптивность */
    @media (max-width: 768px) {
        .error-container {
            padding: 15px;
            padding-top: 80px;
        }

        .error-card {
            padding: 40px 30px;
            max-width: 100%;
        }

        .error-code {
            font-size: 3rem;
        }

        .error-title {
            font-size: 1.2rem;
        }

        .btn-minecraft, .btn-outline {
            padding: 15px 25px;
            font-size: 0.8em;
        }

        .error-actions {
            flex-direction: column;
            align-items: center;
        }

        .error-icon {
            font-size: 4rem;
        }
    }

    @media (max-width: 480px) {
        .error-card {
            padding: 30px 20px;
        }

        .error-code {
            font-size: 2.5rem;
        }

        .error-title {
            font-size: 1rem;
        }

        .error-message {
            font-size: 1rem;
        }

        .btn-minecraft, .btn-outline {
            padding: 12px 20px;
            font-size: 0.7em;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="error-page">
    <div class="particles-container">
        <!-- Частицы будут добавлены через JavaScript -->
    </div>

    <div class="error-container">
        <div class="error-card">
            <div class="error-icon">
                <i class="fas fa-exclamation-circle"></i>
            </div>

            <div class="error-code">500</div>

            <div class="error-title">Внутренняя ошибка сервера</div>

            <div class="error-status">
                <p>Сервер временно недоступен</p>
            </div>

            <div class="error-message">
                <p>Ой! Что-то пошло не так на нашей стороне.</p>
                <p>Мы уже знаем об этой проблеме и работаем над ее устранением.</p>
                <p>Пожалуйста, попробуйте обновить страницу через несколько минут.</p>
            </div>

            <div class="error-actions">
                <a href="{{ url_for('index') }}" class="btn-minecraft">Главная страница</a>
                <a href="javascript:location.reload()" class="btn-outline">Обновить страницу</a>
            </div>

            {% if config.DEBUG and e %}
            <div class="error-details">
                <h4>Детали ошибки (режим отладки):</h4>
                <p><strong>Тип:</strong> {{ e.__class__.__name__ }}</p>
                <p><strong>Сообщение:</strong> {{ e }}</p>
                <p><strong>Время:</strong> {{ moment().format('DD.MM.YYYY HH:mm:ss') if moment else 'Неизвестно' }}</p>
            </div>
            {% else %}
            <div class="error-details">
                <h4>Что можно сделать?</h4>
                <p>• Обновите страницу через несколько минут</p>
                <p>• Проверьте подключение к интернету</p>
                <p>• Попробуйте очистить кэш браузера</p>
                <p>• Обратитесь к администрации, если проблема не исчезает</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Создание частиц (оранжевые для 500)
    function createParticles() {
        const container = document.querySelector('.particles-container');
        if (!container) return;

        const particleCount = window.innerWidth < 768 ? 15 : 30;

        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.innerHTML = '⬛';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
            particle.style.animationDelay = Math.random() * 10 + 's';
            particle.style.fontSize = (Math.random() * 10 + 10) + 'px';
            particle.style.color = `rgba(255, 136, 0, ${Math.random() * 0.5 + 0.2})`;
            particle.style.setProperty('--rand-x', Math.random());

            container.appendChild(particle);
        }
    }

    createParticles();
});
</script>
{% endblock %} 