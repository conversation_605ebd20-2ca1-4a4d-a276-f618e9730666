{% extends "base.html" %}

{% block title %}PLAYLAND - Заявка в команду{% endblock %}

{% block namespace %}team_application{% endblock %}

{% block extra_head %}
<style>
    .hero {
        background-image: url("{{ url_for('static', filename='images/grass_background.jpg') }}");
        background-size: cover;
        background-position: center center;
        background-attachment: fixed;
        height: 50vh;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        padding: 20px;
        padding-top: 100px;
        box-sizing: border-box;
        position: relative;
        overflow: hidden;
    }
    
    .hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.6);
        backdrop-filter: blur(2px);
        z-index: 1;
    }
    
    .hero-content {
        position: relative;
        z-index: 2;
        max-width: 900px;
        width: 100%;
    }
    
    .hero h1 {
        font-family: 'Press Start 2P', cursive;
        font-size: 3.5em;
        color: #00ff00;
        text-shadow: 2px 2px 0 #000000, 0 0 15px #00ff00;
        margin-bottom: 20px;
        line-height: 1.3;
        animation: textPopIn 0.8s cubic-bezier(0.68, -0.55, 0.27, 1.55) 0.5s forwards, heroGlow 2.5s infinite alternate 1s;
        opacity: 0;
        transform: scale(0.5);
    }
    
    @keyframes textPopIn {
        to {
            opacity: 1;
            transform: scale(1);
        }
    }
    
    @keyframes heroGlow {
        0% {
            text-shadow: 2px 2px 0 #000000, 0 0 5px rgba(0, 255, 0, 0.5); 
        }
        100% {
            text-shadow: 2px 2px 0 #000000, 0 0 15px #00ff00, 0 0 30px #00ff00, 0 0 40px #00ff00;
        }
    }

    .content-section {
        padding: 80px 20px;
        background-color: rgba(0, 0, 0, 0.8);
    }
    
    .content-section h2 {
        font-family: 'Press Start 2P', cursive;
        font-size: 2.2em;
        color: #00ff00;
        text-align: center;
        margin-bottom: 40px;
        text-shadow: 0 0 10px #00ff00;
    }
    
    .application-container {
        max-width: 800px;
        margin: 0 auto;
        background-color: rgba(0, 0, 0, 0.5);
        border: 2px solid #00ff00;
        border-radius: 10px;
        padding: 40px;
        box-shadow: 0 0 20px rgba(0, 255, 0, 0.2);
    }
    
    .form-group {
        margin-bottom: 25px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 10px;
        font-family: 'Press Start 2P', cursive;
        font-size: 0.9em;
        color: #00ff00;
    }
    
    .form-control {
        width: 100%;
        padding: 12px 15px;
        background-color: rgba(0, 0, 0, 0.7);
        border: 2px solid rgba(0, 255, 0, 0.5);
        border-radius: 5px;
        color: #ffffff;
        font-family: 'Roboto', sans-serif;
        font-size: 1em;
        transition: all 0.3s ease;
        box-sizing: border-box;
    }
    
    .form-control:focus {
        border-color: #00ff00;
        outline: none;
        box-shadow: 0 0 15px rgba(0, 255, 0, 0.3);
    }
    
    textarea.form-control {
        min-height: 120px;
        resize: vertical;
    }
    
    .form-check {
        display: flex;
        align-items: center;
        margin-top: 5px;
    }
    
    .form-check-input {
        margin-right: 10px;
        width: 18px;
        height: 18px;
        accent-color: #00ff00;
    }
    
    .form-check-label {
        font-size: 0.9em;
        color: #f0f0f0;
    }
    
    .error-message {
        color: #ff6b6b;
        margin-top: 5px;
        font-size: 0.9em;
    }
    
    .info-block {
        background-color: rgba(0, 255, 0, 0.1);
        border-left: 3px solid #00ff00;
        padding: 15px;
        margin-bottom: 30px;
        color: #f0f0f0;
        line-height: 1.6;
    }
    
    .info-block h3 {
        font-family: 'Press Start 2P', cursive;
        font-size: 1.1em;
        color: #00ff00;
        margin-top: 0;
        margin-bottom: 10px;
    }
    
    .requirements-list {
        list-style-type: none;
        padding: 0;
        margin: 0;
    }
    
    .requirements-list li {
        padding: 8px 0 8px 30px;
        position: relative;
    }
    
    .requirements-list li::before {
        content: '✓';
        color: #00ff00;
        position: absolute;
        left: 0;
        top: 8px;
        font-weight: bold;
    }
    
    .btn-minecraft {
        display: inline-block;
        padding: 18px 35px;
        background-color: #00ff00;
        color: #000;
        text-decoration: none;
        font-family: 'Press Start 2P', cursive;
        font-size: 1em;
        text-transform: uppercase;
        border: none;
        position: relative;
        overflow: hidden;
        transition: all 0.3s;
        box-shadow: 0 4px 0 #006400, 0 0 20px rgba(0, 255, 0, 0.4);
        border-radius: 3px;
        cursor: pointer;
        margin-top: 20px;
    }

    .btn-minecraft:hover {
        transform: translateY(-5px);
        box-shadow: 0 9px 0 #006400, 0 0 30px rgba(0, 255, 0, 0.6);
    }
    
    .form-submit {
        text-align: center;
        margin-top: 40px;
    }

    @media (max-width: 768px) {
        .hero h1 {
            font-size: 2.2em;
        }
        
        .content-section h2 {
            font-size: 1.8em;
        }
        
        .application-container {
            padding: 20px;
        }
        
        .btn-minecraft {
            padding: 15px 25px;
            font-size: 0.8em;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="hero">
    <div class="particles-container" id="particles"></div>
    <div class="hero-content">
        <h1 class="animate-fade-in-up">ЗАЯВКА В КОМАНДУ</h1>
    </div>
</section>

<section class="content-section">
    <div class="application-container animate-fade-in-up">
        <div class="info-block">
            <h3>Требования к кандидатам</h3>
            <ul class="requirements-list">
                <li>Возраст от 16 лет</li>
                <li>Опыт игры на нашем сервере не менее 1 месяца</li>
                <li>Отсутствие нарушений правил</li>
                <li>Готовность уделять серверу минимум 10 часов в неделю</li>
                <li>Ответственность и коммуникабельность</li>
                <li>Умение работать в команде</li>
            </ul>
        </div>
        
        <form class="application-form" method="POST" action="{{ url_for('team_application') }}">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            
            <div class="form-group">
                <label for="minecraft_nickname">Ваш никнейм в Minecraft*</label>
                <input type="text" class="form-control" id="minecraft_nickname" name="minecraft_nickname" value="{{ form_data.minecraft_nickname if form_data else '' }}" required>
                {% if errors and errors.minecraft_nickname %}
                    <div class="error-message">{{ errors.minecraft_nickname }}</div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <label for="discord_tag">Ваш Discord тег*</label>
                <input type="text" class="form-control" id="discord_tag" name="discord_tag" placeholder="username#0000" value="{{ form_data.discord_tag if form_data else '' }}" required>
                {% if errors and errors.discord_tag %}
                    <div class="error-message">{{ errors.discord_tag }}</div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <label for="age">Ваш возраст*</label>
                <input type="number" class="form-control" id="age" name="age" min="16" max="100" value="{{ form_data.age if form_data else '' }}" required>
                {% if errors and errors.age %}
                    <div class="error-message">{{ errors.age }}</div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <label for="playtime">Сколько времени вы играете на нашем сервере?*</label>
                <select class="form-control" id="playtime" name="playtime" required>
                    <option value="" disabled selected>Выберите вариант</option>
                    <option value="less_month" {% if form_data and form_data.playtime == 'less_month' %}selected{% endif %}>Менее месяца</option>
                    <option value="1-3_months" {% if form_data and form_data.playtime == '1-3_months' %}selected{% endif %}>1-3 месяца</option>
                    <option value="3-6_months" {% if form_data and form_data.playtime == '3-6_months' %}selected{% endif %}>3-6 месяцев</option>
                    <option value="6-12_months" {% if form_data and form_data.playtime == '6-12_months' %}selected{% endif %}>6-12 месяцев</option>
                    <option value="more_year" {% if form_data and form_data.playtime == 'more_year' %}selected{% endif %}>Более года</option>
                </select>
                {% if errors and errors.playtime %}
                    <div class="error-message">{{ errors.playtime }}</div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <label for="position">Какую должность хотели бы занять?*</label>
                <select class="form-control" id="position" name="position" required onchange="toggleCustomPosition()">
                    <option value="" disabled selected>Выберите вариант</option>
                    <option value="helper" {% if form_data and form_data.position == 'helper' %}selected{% endif %}>Хелпер</option>
                    <option value="moderator" {% if form_data and form_data.position == 'moderator' %}selected{% endif %}>Модератор</option>
                    <option value="builder" {% if form_data and form_data.position == 'builder' %}selected{% endif %}>Строитель</option>
                    <option value="event_manager" {% if form_data and form_data.position == 'event_manager' %}selected{% endif %}>Организатор ивентов</option>
                    <option value="other" {% if form_data and form_data.position == 'other' %}selected{% endif %}>Другое</option>
                    <option value="any" {% if form_data and form_data.position == 'any' %}selected{% endif %}>Любая (на усмотрение администрации)</option>
                </select>
                {% if errors and errors.position %}
                    <div class="error-message">{{ errors.position }}</div>
                {% endif %}
            </div>
            
            <div class="form-group" id="custom_position_group" style="display: none;">
                <label for="custom_position">Укажите желаемую должность*</label>
                <input type="text" class="form-control" id="custom_position" name="custom_position" value="{{ form_data.custom_position if form_data else '' }}">
                {% if errors and errors.custom_position %}
                    <div class="error-message">{{ errors.custom_position }}</div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <label for="availability">Сколько времени вы готовы уделять работе в команде?*</label>
                <select class="form-control" id="availability" name="availability" required>
                    <option value="" disabled selected>Выберите вариант</option>
                    <option value="less_10h" {% if form_data and form_data.availability == 'less_10h' %}selected{% endif %}>Менее 10 часов в неделю</option>
                    <option value="10-20h" {% if form_data and form_data.availability == '10-20h' %}selected{% endif %}>10-20 часов в неделю</option>
                    <option value="20-30h" {% if form_data and form_data.availability == '20-30h' %}selected{% endif %}>20-30 часов в неделю</option>
                    <option value="more_30h" {% if form_data and form_data.availability == 'more_30h' %}selected{% endif %}>Более 30 часов в неделю</option>
                </select>
                {% if errors and errors.availability %}
                    <div class="error-message">{{ errors.availability }}</div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <label for="experience">Есть ли у вас опыт работы администратором/модератором?*</label>
                <textarea class="form-control" id="experience" name="experience" rows="4" required>{{ form_data.experience if form_data else '' }}</textarea>
                {% if errors and errors.experience %}
                    <div class="error-message">{{ errors.experience }}</div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <label for="motivation">Почему вы хотите вступить в команду сервера?*</label>
                <textarea class="form-control" id="motivation" name="motivation" rows="4" required>{{ form_data.motivation if form_data else '' }}</textarea>
                {% if errors and errors.motivation %}
                    <div class="error-message">{{ errors.motivation }}</div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <label for="ideas">Какие идеи вы бы хотели реализовать на нашем сервере?</label>
                <textarea class="form-control" id="ideas" name="ideas" rows="4">{{ form_data.ideas if form_data else '' }}</textarea>
            </div>
            
            <div class="form-group">
                <label for="additional_info">Дополнительная информация</label>
                <textarea class="form-control" id="additional_info" name="additional_info" rows="4">{{ form_data.additional_info if form_data else '' }}</textarea>
            </div>
            
            <div class="form-group">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="rules_acceptance" name="rules_acceptance" required {% if form_data and form_data.rules_acceptance %}checked{% endif %}>
                    <label class="form-check-label" for="rules_acceptance">Я ознакомился и согласен с <a href="{{ url_for('rules') }}" target="_blank">правилами сервера</a>*</label>
                </div>
                {% if errors and errors.rules_acceptance %}
                    <div class="error-message">{{ errors.rules_acceptance }}</div>
                {% endif %}
            </div>
            
            <div class="form-submit">
                <button type="submit" class="btn-minecraft">Отправить заявку</button>
            </div>
        </form>
    </div>
</section>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Создаем частицы
        const particlesContainer = document.getElementById('particles');
        if (!particlesContainer) return;
        
        const colors = [
            'rgba(0, 255, 0, 0.4)',
            'rgba(50, 205, 50, 0.4)',
            'rgba(173, 255, 47, 0.4)',
            'rgba(152, 251, 152, 0.4)'
        ];
        
        for (let i = 0; i < 50; i++) {
            const size = Math.random() * 6 + 3;
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.width = `${size}px`;
            particle.style.height = `${size}px`;
            particle.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
            particle.style.left = `${Math.random() * 100}%`;
            particle.style.setProperty('--rand-x', Math.random());
            particle.style.boxShadow = `0 0 ${size}px ${particle.style.backgroundColor}`;
            particle.style.animationDuration = `${Math.random() * 20 + 10}s`;
            particle.style.animationDelay = `${Math.random() * 5}s`;
            
            particlesContainer.appendChild(particle);
        }
        
        // Вызываем функцию при загрузке страницы для установки начального состояния
        toggleCustomPosition();
    });
    
    // Функция переключения поля пользовательской должности
    function toggleCustomPosition() {
        const position = document.getElementById('position');
        const customPositionGroup = document.getElementById('custom_position_group');
        
        if (position.value === 'other') {
            customPositionGroup.style.display = 'block';
            document.getElementById('custom_position').setAttribute('required', 'required');
        } else {
            customPositionGroup.style.display = 'none';
            document.getElementById('custom_position').removeAttribute('required');
        }
    }
</script>
{% endblock %} 