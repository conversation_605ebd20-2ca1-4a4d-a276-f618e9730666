#!/usr/bin/env python3
"""
Быстрый запуск PlayLand сервера.
Проверяет настройки и запускает приложение.
"""

import os
import sys
import subprocess
from pathlib import Path

def check_requirements():
    """Проверка требований"""
    print("🔍 Проверка требований...")
    
    # Проверка Python версии
    if sys.version_info < (3, 8):
        print("❌ Требуется Python 3.8 или выше")
        return False
    
    # Проверка файлов
    required_files = [
        "requirements.txt",
        "playland/config.py",
        "playland/tg_bot/bot.py",
        "playland/website/app.py"
    ]
    
    for file_path in required_files:
        if not Path(file_path).exists():
            print(f"❌ Отсутствует файл: {file_path}")
            return False
    
    print("✅ Требования выполнены")
    return True

def check_env():
    """Проверка переменных окружения"""
    print("🔧 Проверка переменных окружения...")
    
    # Проверяем .env файл
    if Path(".env").exists():
        print("✅ Найден .env файл")
        return True
    
    # Проверяем переменные окружения
    if os.getenv("TELEGRAM_BOT_TOKEN"):
        print("✅ TELEGRAM_BOT_TOKEN установлен")
        return True
    
    print("⚠️ Не найден .env файл и не установлен TELEGRAM_BOT_TOKEN")
    print("💡 Создайте .env файл на основе .env.example")
    return False

def install_deps():
    """Установка зависимостей"""
    print("📦 Проверка зависимостей...")
    
    try:
        import telegram
        import flask
        print("✅ Основные зависимости установлены")
        return True
    except ImportError:
        print("📦 Установка зависимостей...")
        try:
            subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
            ], check=True)
            print("✅ Зависимости установлены")
            return True
        except subprocess.CalledProcessError:
            print("❌ Ошибка установки зависимостей")
            return False

def main():
    """Главная функция"""
    print("🚀 PlayLand Server - Быстрый запуск")
    print("=" * 40)
    
    # Проверки
    if not check_requirements():
        sys.exit(1)
    
    if not check_env():
        print("\n💡 Для продолжения:")
        print("1. Скопируйте .env.example в .env")
        print("2. Заполните TELEGRAM_BOT_TOKEN в .env")
        print("3. Запустите снова: python start.py")
        sys.exit(1)
    
    if not install_deps():
        sys.exit(1)
    
    # Запуск
    print("\n🎉 Все готово! Запускаем сервер...")
    print("🌐 Веб-сайт будет доступен на: http://localhost:5000")
    print("🤖 Telegram бот запустится автоматически")
    print("\n⏹️ Для остановки нажмите Ctrl+C")
    print("=" * 40)
    
    try:
        # Запускаем main.py
        subprocess.run([sys.executable, "main.py"], check=True)
    except KeyboardInterrupt:
        print("\n⏹️ Сервер остановлен")
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Ошибка запуска: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
