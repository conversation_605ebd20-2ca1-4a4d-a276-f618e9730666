# Конфигурация Telegram бота PlayLand

# Токен бота (получить у @BotFather)
BOT_TOKEN=your_bot_token_here

# ID администраторов (через запятую)
ADMIN_IDS=123456789,987654321

# URL веб-приложения для подачи заявок
WEBAPP_URL=https://alezoex.github.io/playland_ticket/

# Настройки синхронизации с сайтом
WEBSITE_API_URL=http://localhost:5555/api/sync
SYNC_SECRET_KEY=sync-secret-key-change-in-production

# Настройки базы данных
DATABASE_PATH=playland_bot.db

# Настройки безопасности
SECURITY_ENABLED=true
RATE_LIMIT_ENABLED=true
MAX_REQUESTS_PER_MINUTE=30

# Настройки логирования
LOG_LEVEL=INFO
LOG_FILE=bot.log

# Настройки уведомлений
NOTIFICATION_ENABLED=true
WEBHOOK_URL=

# Дополнительные настройки
DEBUG_MODE=false
MAINTENANCE_MODE=false
