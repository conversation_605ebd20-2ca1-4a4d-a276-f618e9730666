{% extends "base.html" %}

{% block title %}PLAYLAND - Страница не найдена{% endblock %}

{% block namespace %}error404{% endblock %}

{% block extra_head %}
<style>
    :root {
        --pixel-font: 'Press Start 2P', cursive;
        --main-font: 'Roboto', sans-serif;
        --glow-green: #00ff00;
        --bright-green: #32cd32;
        --dark-bg: #121212;
    }

    .error-page {
        min-height: 100vh;
        background-image: url("{{ url_for('static', filename='images/grass_background.jpg') }}");
        background-size: cover;
        background-position: center center;
        background-attachment: fixed;
        position: relative;
        overflow: hidden;
    }

    .error-page::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        backdrop-filter: blur(2px);
        z-index: 1;
    }

    .error-container {
        position: relative;
        z-index: 2;
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 15px;
        padding-top: 80px;
        padding-bottom: 20px;
    }

    .error-card {
        background: rgba(26, 26, 26, 0.95);
        border: 2px solid var(--glow-green);
        border-radius: 15px;
        padding: 25px 20px;
        width: 100%;
        max-width: 650px;
        text-align: center;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5), 0 0 30px rgba(0, 255, 0, 0.2);
        position: relative;
        overflow: hidden;
        animation: cardSlideIn 0.8s cubic-bezier(0.68, -0.55, 0.27, 1.55) forwards;
        opacity: 0;
        transform: translateY(50px);
    }

    @keyframes cardSlideIn {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .error-card::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        border: 2px solid transparent;
        border-radius: 15px;
        background: linear-gradient(45deg, var(--glow-green), transparent, var(--glow-green));
        background-size: 400% 400%;
        opacity: 0.3;
        z-index: -1;
        animation: borderGlow 3s ease infinite;
    }

    @keyframes borderGlow {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    .error-icon {
        font-size: 4rem;
        color: var(--glow-green);
        margin-bottom: 20px;
        text-shadow: 0 0 20px rgba(0, 255, 0, 0.5);
        animation: iconFloat 3s ease-in-out infinite;
    }

    @keyframes iconFloat {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    .error-code {
        font-family: var(--pixel-font);
        font-size: 3rem;
        color: var(--glow-green);
        margin-bottom: 15px;
        text-shadow: 0 0 15px rgba(0, 255, 0, 0.5);
        animation: codeGlow 2s ease-in-out infinite alternate;
    }

    @keyframes codeGlow {
        from { text-shadow: 0 0 10px rgba(0, 255, 0, 0.5); }
        to { text-shadow: 0 0 30px var(--glow-green), 0 0 40px var(--glow-green); }
    }

    .error-title {
        font-family: var(--pixel-font);
        font-size: 1.3rem;
        color: var(--glow-green);
        margin-bottom: 15px;
        text-shadow: 0 0 10px rgba(0, 255, 0, 0.3);
    }

    .error-message {
        color: #f0f0f0;
        font-size: 1rem;
        margin-bottom: 25px;
        line-height: 1.5;
        font-family: var(--main-font);
    }

    .error-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
        margin-bottom: 25px;
    }

    .btn-minecraft {
        display: inline-block;
        padding: 18px 35px;
        background-color: var(--glow-green);
        color: #000;
        text-decoration: none;
        font-family: var(--pixel-font);
        font-size: 0.9em;
        text-transform: uppercase;
        border: none;
        position: relative;
        overflow: hidden;
        transition: all 0.3s;
        box-shadow: 0 4px 0 #006400, 0 0 20px rgba(0, 255, 0, 0.4);
        border-radius: 5px;
        cursor: pointer;
        letter-spacing: 1px;
    }

    .btn-minecraft::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.2);
        transition: transform 0.5s ease;
        transform: skewX(-15deg);
    }

    .btn-minecraft:hover {
        transform: translateY(-3px);
        box-shadow: 0 7px 0 #006400, 0 0 30px rgba(0, 255, 0, 0.6);
        text-decoration: none;
        color: #000;
    }

    .btn-minecraft:hover::before {
        transform: skewX(-15deg) translateX(200%);
    }

    .btn-minecraft:active {
        transform: translateY(0);
        box-shadow: 0 2px 0 #006400;
    }

    .btn-outline {
        background-color: transparent;
        border: 2px solid var(--glow-green);
        color: var(--glow-green);
        box-shadow: 0 4px 0 rgba(0, 255, 0, 0.3), 0 0 20px rgba(0, 255, 0, 0.2);
        padding: 16px 33px;
    }

    .btn-outline:hover {
        box-shadow: 0 7px 0 rgba(0, 255, 0, 0.3), 0 0 30px rgba(0, 255, 0, 0.4);
        background-color: rgba(0, 255, 0, 0.1);
        color: var(--glow-green);
        text-decoration: none;
    }

    .btn-outline:active {
        box-shadow: 0 2px 0 rgba(0, 255, 0, 0.3);
        transform: translateY(0);
    }

    .error-suggestions {
        background: rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(0, 255, 0, 0.3);
        border-radius: 10px;
        padding: 15px;
        text-align: left;
        margin-top: 10px;
    }

    .error-suggestions h4 {
        font-family: var(--pixel-font);
        font-size: 1rem;
        color: var(--glow-green);
        margin-bottom: 15px;
        text-align: center;
        text-shadow: 0 0 10px rgba(0, 255, 0, 0.3);
    }

    .error-suggestions ul {
        list-style: none;
        padding: 0;
        margin: 0 0 15px 0;
    }

    .error-suggestions li {
        color: #f0f0f0;
        margin-bottom: 8px;
        padding-left: 20px;
        position: relative;
        font-family: var(--main-font);
        line-height: 1.4;
        font-size: 0.9rem;
    }

    .error-suggestions li::before {
        content: '→';
        position: absolute;
        left: 0;
        color: var(--glow-green);
        font-weight: bold;
        font-size: 1rem;
    }

    .error-suggestions li.sub-item {
        margin-left: 15px;
        margin-bottom: 6px;
        color: #ccc;
        font-size: 0.85rem;
    }

    .error-suggestions li.sub-item::before {
        content: '';
    }

    .quick-links {
        border-top: 1px solid rgba(0, 255, 0, 0.2);
        padding-top: 15px;
        margin-top: 15px;
    }

    .quick-links h5 {
        font-family: var(--pixel-font);
        font-size: 0.85rem;
        color: var(--glow-green);
        margin-bottom: 12px;
        text-align: center;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .links-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 8px;
    }

    .quick-link {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8px 12px;
        background: rgba(0, 255, 0, 0.1);
        border: 1px solid rgba(0, 255, 0, 0.3);
        border-radius: 6px;
        color: var(--glow-green);
        text-decoration: none;
        font-family: var(--main-font);
        font-size: 0.8rem;
        font-weight: 600;
        transition: all 0.3s ease;
        text-align: center;
    }

    .quick-link:hover {
        background: rgba(0, 255, 0, 0.2);
        border-color: var(--glow-green);
        color: var(--bright-green);
        text-decoration: none;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 255, 0, 0.2);
    }

    /* Частицы */
    .particles-container {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        z-index: 0;
    }

    .particle {
        position: absolute;
        opacity: 0;
        animation: floatParticle linear infinite;
    }

    @keyframes floatParticle {
        0% {
            transform: translateY(100vh) translateX(0);
            opacity: 0;
        }
        10% {
            opacity: 0.7;
        }
        90% {
            opacity: 0.7;
        }
        100% {
            transform: translateY(-20vh) translateX(calc(var(--rand-x, 0) * 40vw - 20vw));
            opacity: 0;
        }
    }

    /* Адаптивность */
    @media (max-width: 768px) {
        .error-container {
            padding: 15px;
            padding-top: 80px;
        }

        .error-card {
            padding: 40px 30px;
            max-width: 100%;
        }

        .error-code {
            font-size: 3rem;
        }

        .error-title {
            font-size: 1.2rem;
        }

        .btn-minecraft, .btn-outline {
            padding: 15px 25px;
            font-size: 0.8em;
        }

        .error-actions {
            flex-direction: column;
            align-items: center;
        }

        .error-icon {
            font-size: 4rem;
        }

        .error-suggestions {
            padding: 25px 20px;
        }

        .error-suggestions h4 {
            font-size: 1rem;
        }

        .links-grid {
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 8px;
        }

        .quick-link {
            padding: 10px 12px;
            font-size: 0.8rem;
        }
    }

    @media (max-width: 480px) {
        .error-card {
            padding: 30px 20px;
        }

        .error-code {
            font-size: 2.5rem;
        }

        .error-title {
            font-size: 1rem;
        }

        .error-message {
            font-size: 1rem;
        }

        .btn-minecraft, .btn-outline {
            padding: 12px 20px;
            font-size: 0.7em;
        }

        .error-suggestions {
            padding: 20px 15px;
        }

        .error-suggestions h4 {
            font-size: 0.9rem;
        }

        .error-suggestions li {
            font-size: 0.9rem;
            margin-bottom: 10px;
        }

        .error-suggestions li.sub-item {
            font-size: 0.8rem;
            margin-left: 15px;
        }

        .links-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 6px;
        }

        .quick-link {
            padding: 8px 10px;
            font-size: 0.75rem;
        }

        .quick-links h5 {
            font-size: 0.8rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="error-page">
    <div class="particles-container">
        <!-- Частицы будут добавлены через JavaScript -->
    </div>

    <div class="error-container">
        <div class="error-card">
            <div class="error-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>

            <div class="error-code">404</div>

            <div class="error-title">Страница не найдена</div>

            <div class="error-message">
                <p>Упс! Кажется, такой страницы не существует на нашем сервере.</p>
                <p>Возможно, вы неправильно ввели адрес или страница была перемещена.</p>
            </div>

            <div class="error-actions">
                <a href="{{ url_for('index') }}" class="btn-minecraft">Главная страница</a>
                <a href="javascript:history.back()" class="btn-outline">Назад</a>
            </div>

            <div class="error-suggestions">
                <h4>Что можно сделать?</h4>
                <ul>
                    <li>Проверьте правильность написания URL-адреса</li>
                    <li>Вернитесь на главную страницу через кнопку выше</li>
                    <li>Воспользуйтесь навигационным меню сайта</li>
                    <li>Обратитесь к администрации, если проблема повторяется</li>
                </ul>

                <div class="quick-links">
                    <h5>Быстрые ссылки:</h5>
                    <div class="links-grid">
                        <a href="{{ url_for('index') }}" class="quick-link">🏠 Главная</a>
                        <a href="{{ url_for('gallery') }}" class="quick-link">🖼️ Галерея</a>
                        <a href="{{ url_for('team') }}" class="quick-link">👥 Команда</a>
                        <a href="{{ url_for('faq') }}" class="quick-link">❓ FAQ</a>
                        <a href="{{ url_for('rules') }}" class="quick-link">📋 Правила</a>
                        <a href="{{ url_for('application') }}" class="quick-link">📝 Заявка</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Создание частиц
    function createParticles() {
        const container = document.querySelector('.particles-container');
        if (!container) return;

        const particleCount = window.innerWidth < 768 ? 20 : 40;

        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.innerHTML = '⬛';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
            particle.style.animationDelay = Math.random() * 10 + 's';
            particle.style.fontSize = (Math.random() * 10 + 10) + 'px';
            particle.style.color = `rgba(0, 255, 0, ${Math.random() * 0.5 + 0.2})`;
            particle.style.setProperty('--rand-x', Math.random());

            container.appendChild(particle);
        }
    }

    createParticles();
});
</script>
{% endblock %} 