{% extends "base.html" %}

{% block title %}Пополнение баланса - PlayLand{% endblock %}

{% block head_extra %}
<style>
    :root {
        --minecraft-grass: #5B9C3B;
        --minecraft-dirt: #8C5E39;
        --minecraft-stone: #919191;
        --minecraft-wood: #73553C;
        --minecraft-planks: #B88E5F;
        --minecraft-emerald: #17DD62;
    }
    
    .balance-page {
        max-width: 700px;
        margin: 30px auto;
        padding: 0;
        animation: fadeIn 0.8s ease-out forwards;
        position: relative;
    }
    
    .balance-header {
        background-color: rgba(0, 0, 0, 0.7);
        padding: 25px;
        position: relative;
        border: 4px solid var(--minecraft-stone);
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        overflow: hidden;
        text-align: center;
    }
    
    .balance-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: linear-gradient(90deg, transparent, var(--minecraft-emerald), transparent);
        animation: headerGlow 3s infinite;
    }
    
    @keyframes headerGlow {
        0%, 100% { opacity: 0.5; }
        50% { opacity: 1; }
    }
    
    .balance-header h1 {
        font-family: var(--pixel-font);
        font-size: 2em;
        color: var(--minecraft-emerald);
        text-shadow: 2px 2px 0 #000, 0 0 10px var(--minecraft-emerald);
        margin-bottom: 15px;
        text-align: center;
        text-transform: uppercase;
        letter-spacing: 2px;
    }
    
    .balance-content {
        background-color: rgba(0, 0, 0, 0.6);
        border: 4px solid var(--minecraft-dirt);
        padding: 25px;
        position: relative;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        overflow: hidden;
    }
    
    .balance-content::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: repeating-linear-gradient(
            45deg,
            rgba(255,255,255,0.03),
            rgba(255,255,255,0.03) 10px,
            rgba(0,0,0,0.05) 10px,
            rgba(0,0,0,0.05) 20px
        );
        pointer-events: none;
        z-index: 0;
    }
    
    .balance-form {
        position: relative;
        z-index: 1;
    }
    
    .balance-form p {
        margin-bottom: 20px;
        font-size: 1.1em;
        line-height: 1.6;
    }
    
    .current-balance {
        background-color: rgba(0, 0, 0, 0.4);
        border-radius: 0;
        border-left: 4px solid var(--minecraft-emerald);
        padding: 15px;
        margin-bottom: 30px;
        position: relative;
        overflow: hidden;
    }
    
    .current-balance::after {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(
            transparent, 
            transparent, 
            transparent, 
            rgba(23, 221, 98, 0.1)
        );
        transform: rotate(30deg);
        animation: shineSweep 3s infinite;
        pointer-events: none;
    }
    
    @keyframes shineSweep {
        0% { transform: translateX(-100%) rotate(30deg); }
        100% { transform: translateX(100%) rotate(30deg); }
    }
    
    .current-balance h3 {
        font-family: var(--pixel-font);
        font-size: 1.2em;
        margin-top: 0;
        color: white;
        text-shadow: 1px 1px 0 #000;
    }
    
    .text-emerald {
        color: var(--minecraft-emerald);
        text-shadow: 0 0 5px rgba(23, 221, 98, 0.5);
    }
    
    .form-group {
        margin-bottom: 25px;
        position: relative;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 10px;
        font-family: var(--pixel-font);
        font-size: 0.9em;
        color: var(--minecraft-emerald);
        text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.8);
    }
    
    .form-group input[type="text"],
    .form-group input[type="number"] {
        width: 100%;
        padding: 15px;
        background-color: rgba(0, 0, 0, 0.5);
        border: 2px solid var(--minecraft-wood);
        color: white;
        border-radius: 0;
        font-family: var(--main-font);
        font-size: 1.1em;
        transition: all 0.3s ease;
        box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.5);
    }
    
    .form-group input:focus {
        outline: none;
        box-shadow: 0 0 15px rgba(23, 221, 98, 0.3), inset 0 0 10px rgba(0, 0, 0, 0.5);
        border-color: var(--minecraft-emerald);
    }
    
    .amount-presets {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-bottom: 20px;
    }
    
    .preset-btn {
        flex: 1;
        min-width: 80px;
        padding: 10px;
        background-color: rgba(0, 0, 0, 0.5);
        border: 2px solid var(--minecraft-wood);
        color: white;
        font-family: var(--pixel-font);
        font-size: 0.9em;
        cursor: pointer;
        transition: all 0.2s ease;
        text-align: center;
    }
    
    .preset-btn:hover {
        background-color: rgba(91, 156, 59, 0.2);
        border-color: var(--minecraft-emerald);
        transform: translateY(-3px);
    }
    
    .preset-btn:active {
        transform: translateY(1px);
    }
    
    .payment-methods {
        margin-top: 30px;
        border-top: 2px dashed rgba(255, 255, 255, 0.1);
        padding-top: 20px;
    }
    
    .payment-methods h3 {
        font-family: var(--pixel-font);
        font-size: 1.2em;
        margin-bottom: 15px;
        color: white;
        text-shadow: 1px 1px 0 #000;
    }
    
    .payment-options {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin-bottom: 30px;
    }
    
    .payment-option {
        flex: 1;
        min-width: 140px;
        background-color: rgba(0, 0, 0, 0.3);
        padding: 15px;
        border: 2px solid transparent;
        cursor: pointer;
        transition: all 0.2s ease;
        text-align: center;
    }
    
    .payment-option:hover {
        background-color: rgba(0, 0, 0, 0.5);
        transform: translateY(-3px);
    }
    
    .payment-option.selected {
        border-color: var(--minecraft-emerald);
        background-color: rgba(23, 221, 98, 0.1);
    }
    
    .payment-option img {
        height: 40px;
        margin-bottom: 10px;
        filter: grayscale(70%);
        transition: all 0.3s ease;
    }
    
    .payment-option:hover img,
    .payment-option.selected img {
        filter: grayscale(0%);
    }
    
    .payment-option span {
        display: block;
        font-size: 0.85em;
        color: #ccc;
    }
    
    .submit-btn {
        display: inline-block;
        width: 100%;
        padding: 15px 25px;
        background-color: var(--minecraft-emerald);
        color: white;
        border: none;
        text-decoration: none;
        font-family: var(--pixel-font);
        font-size: 1.1rem;
        position: relative;
        text-align: center;
        text-shadow: 2px 2px 0px rgba(0, 0, 0, 0.8);
        box-shadow: inset -4px -4px 0px 0px rgba(0, 0, 0, 0.3), 
                    inset 4px 4px 0px 0px rgba(255, 255, 255, 0.3);
        cursor: pointer;
        transition: all 0.1s;
        transform-style: preserve-3d;
        image-rendering: pixelated;
        letter-spacing: 1px;
        overflow: hidden;
        margin-top: 30px;
    }
    
    .submit-btn::before {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        top: 8px;
        left: 0;
        background-color: rgba(0, 0, 0, 0.3);
        z-index: -1;
        transform: translateZ(-1px);
        transition: all 0.2s;
    }
    
    .submit-btn:hover {
        transform: translate(0, -3px);
    }
    
    .submit-btn:hover::before {
        top: 11px;
    }
    
    .submit-btn:active {
        transform: translate(0, 5px);
    }
    
    .submit-btn:active::before {
        top: 3px;
    }
    
    .submit-btn i {
        margin-right: 10px;
    }
    
    .back-link {
        display: block;
        text-align: center;
        margin-top: 20px;
        color: white;
        text-decoration: none;
        font-family: var(--pixel-font);
        font-size: 0.8em;
        transition: all 0.3s;
        position: relative;
        z-index: 1;
    }
    
    .back-link:hover {
        color: var(--minecraft-emerald);
        text-shadow: 0 0 10px var(--minecraft-emerald);
    }
    
    .info-block {
        background-color: rgba(0, 0, 0, 0.3);
        border-left: 3px solid var(--minecraft-stone);
        padding: 15px;
        margin: 20px 0;
        font-size: 0.9em;
    }
    
    .info-block h4 {
        margin-top: 0;
        font-family: var(--pixel-font);
        font-size: 1em;
    }
    
    .info-block ul {
        padding-left: 20px;
        margin-bottom: 0;
    }
    
    .info-block li {
        margin-bottom: 5px;
    }
    
    @media (max-width: 768px) {
        .balance-page {
            padding: 0 15px;
            margin: 20px 15px;
        }
        .payment-option {
            min-width: 100px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="balance-page">
    <div class="balance-header">
        <h1>Пополнение баланса</h1>
    </div>
    
    <div class="balance-content">
        <div class="balance-form">
            <div class="current-balance">
                <h3>Текущий баланс: <span class="text-emerald">{{ "{:,}".format(current_user.balance or 0).replace(",", " ") }} ₽</span></h3>
            </div>
            
            <p>Выберите сумму пополнения и способ оплаты. После успешной оплаты средства будут зачислены на ваш игровой аккаунт.</p>
            
            <form action="{{ url_for('create_payment') }}" method="POST">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                
                <div class="form-group">
                    <label for="amount">Сумма пополнения (₽)</label>
                    <input type="number" id="amount" name="amount" min="10" step="10" value="100" required>
                </div>
                
                <div class="amount-presets">
                    <div class="preset-btn" data-amount="100">100 ₽</div>
                    <div class="preset-btn" data-amount="200">200 ₽</div>
                    <div class="preset-btn" data-amount="500">500 ₽</div>
                    <div class="preset-btn" data-amount="1000">1000 ₽</div>
                </div>
                
                <div class="payment-methods">
                    <h3>Способ оплаты</h3>
                    
                    <div class="payment-options">
                        <div class="payment-option selected" data-method="card">
                            <img src="{{ url_for('static', filename='images/payment/card.png') }}" alt="Банковская карта">
                            <span>Банковская карта</span>
                        </div>
                        <div class="payment-option" data-method="qiwi">
                            <img src="{{ url_for('static', filename='images/payment/qiwi.png') }}" alt="QIWI">
                            <span>QIWI</span>
                        </div>
                        <div class="payment-option" data-method="yoomoney">
                            <img src="{{ url_for('static', filename='images/payment/yoomoney.png') }}" alt="ЮMoney">
                            <span>ЮMoney</span>
                        </div>
                        <div class="payment-option" data-method="sbp">
                            <img src="{{ url_for('static', filename='images/payment/sbp.png') }}" alt="СБП">
                            <span>СБП</span>
                        </div>
                    </div>
                    
                    <input type="hidden" name="payment_method" id="payment_method" value="card">
                </div>
                
                <div class="info-block">
                    <h4><i class="fas fa-info-circle"></i> Информация о пополнении:</h4>
                    <ul>
                        <li>Минимальная сумма пополнения: 10 ₽</li>
                        <li>Средства зачисляются моментально после оплаты</li>
                        <li>При возникновении проблем обратитесь в <a href="#" style="color: var(--minecraft-emerald);">поддержку</a></li>
                    </ul>
                </div>
                
                <button type="submit" class="submit-btn">
                    <i class="fas fa-coins"></i> Пополнить баланс
                </button>
            </form>
            
            <a href="{{ url_for('profile.view_profile') }}" class="back-link">⬅ Вернуться в профиль</a>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Обработка выбора пресетов суммы
    const presetBtns = document.querySelectorAll('.preset-btn');
    const amountInput = document.getElementById('amount');
    
    presetBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const amount = this.getAttribute('data-amount');
            amountInput.value = amount;
        });
    });
    
    // Обработка выбора способа оплаты
    const paymentOptions = document.querySelectorAll('.payment-option');
    const paymentMethodInput = document.getElementById('payment_method');
    
    paymentOptions.forEach(option => {
        option.addEventListener('click', function() {
            // Убираем выделение со всех опций
            paymentOptions.forEach(o => o.classList.remove('selected'));
            // Выделяем выбранную опцию
            this.classList.add('selected');
            // Устанавливаем значение в скрытое поле
            paymentMethodInput.value = this.getAttribute('data-method');
        });
    });
});
</script>
{% endblock %} 