#!/usr/bin/env python3
"""
Утилиты для PlayLand веб-приложения
"""

from urllib.parse import urlparse, urljoin
from flask import request, url_for, current_app
import secrets
import hashlib
import os
from datetime import datetime, timezone
import requests
from PIL import Image
import io

def is_safe_url(target):
    """Проверяет, является ли URL безопасным для перенаправления"""
    ref_url = urlparse(request.host_url)
    test_url = urlparse(urljoin(request.host_url, target))
    return test_url.scheme in ('http', 'https') and ref_url.netloc == test_url.netloc

def generate_csrf_token():
    """Генерирует CSRF токен"""
    return secrets.token_hex(16)

def generate_secure_filename(filename):
    """Генерирует безопасное имя файла"""
    if not filename:
        return None
    
    # Получаем расширение файла
    _, ext = os.path.splitext(filename)
    
    # Генерируем уникальное имя
    unique_name = secrets.token_hex(16)
    
    return f"{unique_name}{ext.lower()}"

def allowed_file(filename, allowed_extensions=None):
    """Проверяет, разрешен ли тип файла"""
    if allowed_extensions is None:
        allowed_extensions = current_app.config.get('ALLOWED_EXTENSIONS', {'png', 'jpg', 'jpeg', 'gif', 'webp'})
    
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in allowed_extensions

def save_uploaded_image(file, upload_folder, max_size=(1920, 1080), quality=85):
    """Сохраняет загруженное изображение с оптимизацией"""
    if not file or not allowed_file(file.filename):
        return None
    
    try:
        # Генерируем безопасное имя файла
        filename = generate_secure_filename(file.filename)
        if not filename:
            return None
        
        # Открываем изображение
        image = Image.open(file.stream)
        
        # Конвертируем в RGB если необходимо
        if image.mode in ('RGBA', 'LA', 'P'):
            background = Image.new('RGB', image.size, (255, 255, 255))
            if image.mode == 'P':
                image = image.convert('RGBA')
            background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
            image = background
        
        # Изменяем размер если необходимо
        if image.size[0] > max_size[0] or image.size[1] > max_size[1]:
            image.thumbnail(max_size, Image.Resampling.LANCZOS)
        
        # Создаем папку если не существует
        os.makedirs(upload_folder, exist_ok=True)
        
        # Сохраняем изображение
        filepath = os.path.join(upload_folder, filename)
        image.save(filepath, 'JPEG', quality=quality, optimize=True)
        
        return filename
        
    except Exception as e:
        current_app.logger.error(f"Ошибка при сохранении изображения: {e}")
        return None

def validate_minecraft_username(username):
    """Проверяет валидность Minecraft никнейма через API Mojang"""
    if not username or len(username) < 3 or len(username) > 16:
        return False, "Неверная длина никнейма"
    
    try:
        # Проверяем через API Mojang
        response = requests.get(
            f"https://api.mojang.com/users/profiles/minecraft/{username}",
            timeout=5
        )
        
        if response.status_code == 200:
            data = response.json()
            return True, data.get('id', '')
        elif response.status_code == 404:
            return False, "Игрок не найден"
        else:
            return False, "Ошибка проверки никнейма"
            
    except requests.RequestException:
        # Если API недоступно, проверяем только формат
        import re
        if re.match(r'^[a-zA-Z0-9_]{3,16}$', username):
            return True, None
        return False, "Неверный формат никнейма"

def get_minecraft_avatar(username_or_uuid, size=64):
    """Получает аватар игрока Minecraft"""
    try:
        # Если это UUID, используем его напрямую
        if len(username_or_uuid) == 32 or len(username_or_uuid) == 36:
            uuid = username_or_uuid.replace('-', '')
        else:
            # Получаем UUID по никнейму
            response = requests.get(
                f"https://api.mojang.com/users/profiles/minecraft/{username_or_uuid}",
                timeout=5
            )
            if response.status_code != 200:
                return None
            uuid = response.json().get('id')
        
        # Получаем аватар
        avatar_url = f"https://crafatar.com/avatars/{uuid}?size={size}&overlay"
        return avatar_url
        
    except Exception:
        return None

def send_email(to, subject, template, **kwargs):
    """Отправляет email (заглушка для разработки)"""
    # В реальном приложении здесь будет отправка email
    current_app.logger.info(f"Email to {to}: {subject}")
    return True

def format_datetime(dt, format='%d.%m.%Y %H:%M'):
    """Форматирует дату и время"""
    if not dt:
        return ''
    
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=timezone.utc)
    
    return dt.strftime(format)

def time_ago(dt):
    """Возвращает строку "время назад" """
    if not dt:
        return ''
    
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=timezone.utc)
    
    now = datetime.now(timezone.utc)
    diff = now - dt
    
    seconds = diff.total_seconds()
    
    if seconds < 60:
        return 'только что'
    elif seconds < 3600:
        minutes = int(seconds // 60)
        return f'{minutes} мин. назад'
    elif seconds < 86400:
        hours = int(seconds // 3600)
        return f'{hours} ч. назад'
    elif seconds < 2592000:  # 30 дней
        days = int(seconds // 86400)
        return f'{days} дн. назад'
    else:
        return format_datetime(dt, '%d.%m.%Y')

def generate_pagination_info(page, per_page, total):
    """Генерирует информацию для пагинации"""
    total_pages = (total + per_page - 1) // per_page
    
    return {
        'page': page,
        'per_page': per_page,
        'total': total,
        'total_pages': total_pages,
        'has_prev': page > 1,
        'has_next': page < total_pages,
        'prev_num': page - 1 if page > 1 else None,
        'next_num': page + 1 if page < total_pages else None,
        'start_index': (page - 1) * per_page + 1,
        'end_index': min(page * per_page, total)
    }

def get_client_ip():
    """Получает IP адрес клиента"""
    if request.environ.get('HTTP_X_FORWARDED_FOR') is None:
        return request.environ['REMOTE_ADDR']
    else:
        return request.environ['HTTP_X_FORWARDED_FOR']

def get_user_agent():
    """Получает User-Agent клиента"""
    return request.headers.get('User-Agent', '')

def hash_password(password):
    """Хеширует пароль"""
    from werkzeug.security import generate_password_hash
    return generate_password_hash(password)

def check_password(password_hash, password):
    """Проверяет пароль"""
    from werkzeug.security import check_password_hash
    return check_password_hash(password_hash, password)

def generate_api_key():
    """Генерирует API ключ"""
    return secrets.token_urlsafe(32)

def validate_image_url(url):
    """Проверяет, является ли URL изображением"""
    try:
        response = requests.head(url, timeout=5)
        content_type = response.headers.get('content-type', '')
        return content_type.startswith('image/')
    except:
        return False

def clean_html(text):
    """Очищает HTML теги из текста"""
    import re
    clean = re.compile('<.*?>')
    return re.sub(clean, '', text)

def truncate_text(text, length=100, suffix='...'):
    """Обрезает текст до указанной длины"""
    if not text:
        return ''
    
    if len(text) <= length:
        return text
    
    return text[:length].rsplit(' ', 1)[0] + suffix

def get_file_size(filepath):
    """Получает размер файла в байтах"""
    try:
        return os.path.getsize(filepath)
    except:
        return 0

def format_file_size(size_bytes):
    """Форматирует размер файла в читаемый вид"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024.0 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"

def is_admin_user(user):
    """Проверяет, является ли пользователь администратором"""
    if not user or not user.is_authenticated:
        return False
    return user.is_admin

def is_moderator_user(user):
    """Проверяет, является ли пользователь модератором"""
    if not user or not user.is_authenticated:
        return False
    return user.is_moderator

def log_user_action(user, action, details=None):
    """Логирует действие пользователя"""
    current_app.logger.info(f"User {user.id} ({user.username}): {action}" + 
                           (f" - {details}" if details else ""))
