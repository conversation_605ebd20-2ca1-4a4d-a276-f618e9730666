{% extends "base.html" %}

{% block title %}Мои тикеты - PLAYLAND{% endblock %}

{% block content %}
<div class="tickets-container">
    <div class="tickets-header">
        <h1 class="page-title">
            <i class="fas fa-ticket-alt"></i>
            Мои тикеты поддержки
        </h1>
        <button class="btn btn-primary" onclick="createNewTicket()">
            <i class="fas fa-plus"></i>
            Создать тикет
        </button>
    </div>

    <div class="tickets-stats">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-circle text-warning"></i>
            </div>
            <div class="stat-info">
                <span class="stat-number">{{ tickets|selectattr('status', 'equalto', 'open')|list|length }}</span>
                <span class="stat-label">Открытых</span>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-reply text-success"></i>
            </div>
            <div class="stat-info">
                <span class="stat-number">{{ tickets|selectattr('status', 'equalto', 'answered')|list|length }}</span>
                <span class="stat-label">Отвеченных</span>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-check-circle text-secondary"></i>
            </div>
            <div class="stat-info">
                <span class="stat-number">{{ tickets|selectattr('status', 'equalto', 'closed')|list|length }}</span>
                <span class="stat-label">Закрытых</span>
            </div>
        </div>
    </div>

    <div class="tickets-list">
        {% if tickets %}
            {% for ticket in tickets %}
            <div class="ticket-card" onclick="openTicket({{ ticket.id }})">
                <div class="ticket-status">
                    {% if ticket.status == 'open' %}
                        <span class="status-badge status-open">
                            <i class="fas fa-circle"></i> Открыт
                        </span>
                    {% elif ticket.status == 'answered' %}
                        <span class="status-badge status-answered">
                            <i class="fas fa-reply"></i> Отвечен
                        </span>
                    {% elif ticket.status == 'closed' %}
                        <span class="status-badge status-closed">
                            <i class="fas fa-check-circle"></i> Закрыт
                        </span>
                    {% endif %}
                </div>
                
                <div class="ticket-content">
                    <h3 class="ticket-title">{{ ticket.title }}</h3>
                    <p class="ticket-preview">{{ ticket.last_message }}</p>
                    
                    <div class="ticket-meta">
                        <span class="ticket-date">
                            <i class="fas fa-calendar"></i>
                            {{ ticket.created_at }}
                        </span>
                        <span class="ticket-messages">
                            <i class="fas fa-comments"></i>
                            {{ ticket.messages_count }} сообщений
                        </span>
                    </div>
                </div>
                
                <div class="ticket-arrow">
                    <i class="fas fa-chevron-right"></i>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-ticket-alt"></i>
                </div>
                <h3>У вас пока нет тикетов</h3>
                <p>Создайте тикет, если у вас есть вопросы или проблемы</p>
                <button class="btn btn-primary" onclick="createNewTicket()">
                    <i class="fas fa-plus"></i>
                    Создать первый тикет
                </button>
            </div>
        {% endif %}
    </div>
</div>

<!-- Модальное окно создания тикета -->
<div class="modal" id="createTicketModal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>Создать новый тикет</h2>
            <button class="modal-close" onclick="closeModal()">&times;</button>
        </div>
        
        <form id="createTicketForm" class="modal-body">
            <div class="form-group">
                <label for="ticketTitle">Тема тикета</label>
                <input type="text" id="ticketTitle" name="title" required 
                       placeholder="Кратко опишите вашу проблему">
            </div>
            
            <div class="form-group">
                <label for="ticketType">Тип обращения</label>
                <select id="ticketType" name="application_type" required>
                    <option value="">Выберите тип</option>
                    <option value="support">Техническая поддержка</option>
                    <option value="question">Вопрос по правилам</option>
                    <option value="complaint">Жалоба</option>
                    <option value="suggestion">Предложение</option>
                    <option value="other">Другое</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="ticketContent">Подробное описание</label>
                <textarea id="ticketContent" name="content" rows="5" required
                          placeholder="Опишите вашу проблему или вопрос подробно"></textarea>
            </div>
            
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">Отмена</button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-paper-plane"></i>
                    Создать тикет
                </button>
            </div>
        </form>
    </div>
</div>

<style>
.tickets-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
}

.tickets-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.page-title {
    color: #00ff00;
    margin: 0;
    font-size: 2rem;
}

.tickets-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(0, 255, 0, 0.3);
    border-radius: 10px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.stat-icon {
    font-size: 2rem;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    color: #00ff00;
}

.stat-label {
    color: #ccc;
    font-size: 0.9rem;
}

.tickets-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.ticket-card {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(0, 255, 0, 0.3);
    border-radius: 10px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.ticket-card:hover {
    border-color: #00ff00;
    box-shadow: 0 5px 15px rgba(0, 255, 0, 0.2);
    transform: translateY(-2px);
}

.ticket-status {
    flex-shrink: 0;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: bold;
    white-space: nowrap;
}

.status-open { background: rgba(255, 193, 7, 0.2); color: #ffc107; }
.status-answered { background: rgba(40, 167, 69, 0.2); color: #28a745; }
.status-closed { background: rgba(108, 117, 125, 0.2); color: #6c757d; }

.ticket-content {
    flex: 1;
}

.ticket-title {
    color: #00ff00;
    margin: 0 0 8px 0;
    font-size: 1.2rem;
}

.ticket-preview {
    color: #ccc;
    margin: 0 0 10px 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.ticket-meta {
    display: flex;
    gap: 20px;
    font-size: 0.85rem;
    color: #999;
}

.ticket-arrow {
    color: #00ff00;
    font-size: 1.2rem;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #ccc;
}

.empty-icon {
    font-size: 4rem;
    color: #666;
    margin-bottom: 20px;
}

.empty-state h3 {
    color: #00ff00;
    margin-bottom: 10px;
}

/* Модальное окно */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.95);
    border: 1px solid rgba(0, 255, 0, 0.3);
    border-radius: 15px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid rgba(0, 255, 0, 0.3);
}

.modal-header h2 {
    color: #00ff00;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    color: #ccc;
    font-size: 2rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: #00ff00;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}

@media (max-width: 768px) {
    .tickets-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .tickets-stats {
        grid-template-columns: 1fr;
    }
    
    .ticket-card {
        flex-direction: column;
        align-items: flex-start;
        text-align: left;
    }
    
    .ticket-meta {
        flex-direction: column;
        gap: 5px;
    }
}
</style>

<script>
function openTicket(ticketId) {
    window.location.href = `/ticket/${ticketId}`;
}

function createNewTicket() {
    document.getElementById('createTicketModal').style.display = 'block';
}

function closeModal() {
    document.getElementById('createTicketModal').style.display = 'none';
}

// Закрытие модального окна при клике вне его
window.onclick = function(event) {
    const modal = document.getElementById('createTicketModal');
    if (event.target === modal) {
        closeModal();
    }
}

// Обработка формы создания тикета
document.getElementById('createTicketForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    try {
        const response = await fetch('/api/ticket/create', {
            method: 'POST',
            body: formData
        });
        
        if (response.ok) {
            closeModal();
            location.reload();
        } else {
            alert('Ошибка создания тикета');
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Ошибка создания тикета');
    }
});
</script>
{% endblock %}
