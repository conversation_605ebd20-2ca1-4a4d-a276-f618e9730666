#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import logging
import json
import sqlite3
import datetime
import re
import uuid
import asyncio
import httpx
import html
from dotenv import load_dotenv
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, WebAppInfo, KeyboardButton, ReplyKeyboardMarkup
from telegram.ext import Application, CommandHandler, MessageHandler, CallbackQueryHandler, ContextTypes, filters
from telegram.constants import ParseMode

# Импорт модуля безопасности
try:
    from security_improvements import SecurityManager, rate_limit, admin_required, validate_input, safe_log_user_action, safe_log_error
    SECURITY_AVAILABLE = True
except ImportError:
    try:
        from .security_improvements import SecurityManager, rate_limit, admin_required, validate_input, safe_log_user_action, safe_log_error
        SECURITY_AVAILABLE = True
    except ImportError:
        # Fallback если модуль безопасности недоступен
        SECURITY_AVAILABLE = False
        def rate_limit(action="message"):
            def decorator(func):
                return func
            return decorator
        def admin_required(func):
            return func
        def validate_input(input_type):
            def decorator(func):
                return func
            return decorator
        def safe_log_user_action(user_id, action, details=""):
            logging.info(f"User {user_id} performed action: {action}")
        def safe_log_error(error, context=""):
            logging.error(f"Error in {context}: {str(error)}")

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("bot.log", encoding="utf-8"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Проверка доступности Discord интеграции
try:
    import discord
    from discord.ext import commands
    from discord_integration import add_role_to_user, test_discord_connection
    DISCORD_AVAILABLE = True
except ImportError:
    DISCORD_AVAILABLE = False
    def test_discord_connection():
        logger.warning("Модуль Discord не установлен")
        return False
    # Заглушка для отсутствующего модуля
    async def add_role_to_user(discord_username, nickname):
        logger.warning("Модуль Discord не установлен, роль не может быть добавлена")
        return "? Не удалось добавить роль в Discord сервере (модуль отсутствует)"

# Загрузка переменных окружения
load_dotenv()

# Получение токена бота из переменных окружения
BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN")
if not BOT_TOKEN:
    logger.error("Не указан токен бота в переменных окружения (TELEGRAM_BOT_TOKEN)")
    sys.exit(1)

# Настройка базы данных - используем объединенную БД
DB_PATH = os.getenv("DB_PATH", "website/instance/playland_unified.db")

# Настройка API сервера
API_URL = os.getenv("API_URL", "http://localhost:5000/api")
API_KEY = os.getenv("API_KEY", "")

# Настройка Discord
DISCORD_SERVER_ID = os.getenv("DISCORD_SERVER_ID", "")
DISCORD_ROLE_ID = os.getenv("DISCORD_ROLE_ID", "")

# Настройка администраторов
ADMIN_IDS = set()
admin_ids_str = os.getenv("ADMIN_IDS", "")
if admin_ids_str:
    try:
        # Разделяем строку по запятым и удаляем пробелы
        admin_ids_list = [id.strip() for id in admin_ids_str.split(',')]
        for admin_id in admin_ids_list:
            if admin_id:  # Проверяем, что строка не пустая
                try:
                    ADMIN_IDS.add(int(admin_id))
                    logger.info(f"Добавлен администратор с ID {admin_id}")
                except ValueError:
                    logger.error(f"Некорректный ID администратора: {admin_id}")
    except Exception as e:
        logger.error(f"Ошибка при обработке ADMIN_IDS: {e}")

# Настройка стикеров удалена для более чистого UX

# Настройка веб-приложения
WEBAPP_URL = os.getenv("WEBAPP_URL", "https://alezoex.github.io/playland_ticket/")

# Импорт модуля синхронизации с сайтом
try:
    from website_sync import (
        sync_user_to_website,
        create_application_on_website,
        create_support_ticket,
        send_ticket_message,
        get_news_for_telegram,
        test_website_connection,
        sync_user,
        format_application_for_website
    )
    WEBSITE_SYNC_AVAILABLE = True
    logger.info("Website sync module loaded successfully")
except ImportError as e:
    WEBSITE_SYNC_AVAILABLE = False
    logger.warning(f"Website sync module not available: {e}")

    # Заглушки для функций синхронизации
    async def sync_user_to_website(*args, **kwargs):
        return True
    async def create_application_on_website(*args, **kwargs):
        return None
    async def create_support_ticket(*args, **kwargs):
        return None
    async def send_ticket_message(*args, **kwargs):
        return True
    async def get_news_for_telegram(*args, **kwargs):
        return []
    async def test_website_connection():
        return False
    def sync_user(func):
        return func
    def format_application_for_website(data):
        return data

# Глобальная статистика
STATS = {
    "total_applications": 0,
    "approved_applications": 0,
    "rejected_applications": 0,
    "pending_applications": 0
}

# Функции для работы с базой данных
def init_database():
    """Инициализация объединенной базы данных"""
    try:
        # Создаем директорию instance если её нет
        os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Создаем объединенную таблицу пользователей (совместимую с обеими системами)
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            telegram_id INTEGER UNIQUE,
            username TEXT,
            email TEXT UNIQUE,
            password_hash TEXT,
            first_name TEXT,
            last_name TEXT,
            nickname TEXT,
            discord_id TEXT,
            discord_tag TEXT,
            avatar_url TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
            registration_date TEXT,
            is_admin BOOLEAN DEFAULT FALSE,
            is_activated BOOLEAN DEFAULT FALSE,
            is_banned BOOLEAN DEFAULT FALSE,
            ban_reason TEXT,
            last_activity TEXT,
            status TEXT DEFAULT 'pending',
            balance REAL DEFAULT 0.0,
            is_whitelisted BOOLEAN DEFAULT FALSE,
            whitelist_status TEXT DEFAULT 'pending'
        )
        ''')

        # Создаем объединенную таблицу заявок
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS applications (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            telegram_user_id INTEGER,
            minecraft_nickname TEXT NOT NULL,
            discord_tag TEXT,
            reason TEXT,
            status TEXT DEFAULT 'pending',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            timestamp TEXT,
            processed_by INTEGER,
            processed_at DATETIME,
            rejection_reason TEXT,
            source TEXT DEFAULT 'telegram',
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (telegram_user_id) REFERENCES users (telegram_id)
        )
        ''')

        # Проверяем и добавляем недостающие столбцы для существующих таблиц
        try:
            # Проверяем наличие столбцов в таблице applications
            cursor.execute("PRAGMA table_info(applications)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'rejection_reason' not in columns:
                cursor.execute('ALTER TABLE applications ADD COLUMN rejection_reason TEXT')
                logger.info("Добавлен столбец rejection_reason в таблицу applications")

            # Добавляем алиасы для совместимости (если нужно)
            if 'nickname' not in columns and 'minecraft_nickname' in columns:
                logger.info("Используется столбец minecraft_nickname вместо nickname")

            if 'discord' not in columns and 'discord_tag' in columns:
                logger.info("Используется столбец discord_tag вместо discord")

        except Exception as e:
            logger.warning(f"Ошибка при проверке столбцов: {e}")

        # Создаем таблицу статистики
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS stats (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            total_applications INTEGER DEFAULT 0,
            approved_applications INTEGER DEFAULT 0,
            rejected_applications INTEGER DEFAULT 0,
            pending_applications INTEGER DEFAULT 0,
            last_updated TEXT
        )
        ''')

        # Создаем таблицу для хранения состояний пользователей
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS user_states (
            user_id INTEGER PRIMARY KEY,
            state TEXT,
            data TEXT,
            timestamp TEXT
        )
        ''')

        conn.commit()
        logger.info("База данных успешно инициализирована")

        # Загружаем статистику
        load_stats_from_db()

        return True
    except Exception as e:
        logger.error(f"Ошибка при инициализации базы данных: {e}")
        return False
    finally:
        if conn:
            conn.close()

def execute_sqlite_query(query, params=(), fetch_all=False, fetch_one=False):
    """Выполнение запроса к базе данных SQLite"""
    conn = None
    try:
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row  # Для доступа к столбцам по имени
        cursor = conn.cursor()
        cursor.execute(query, params)

        result = None
        if fetch_all:
            result = [dict(row) for row in cursor.fetchall()]
        elif fetch_one:
            row = cursor.fetchone()
            if row:
                result = dict(row)
        else:
            conn.commit()
            result = cursor.lastrowid

        return result
    except Exception as e:
        if conn:
            conn.rollback()
        # Безопасное логирование без утечки чувствительных данных
        safe_log_error(e, "database_query")
        raise
    finally:
        if conn:
            conn.close()

def load_stats_from_db():
    """Загрузка статистики из базы данных"""
    try:
        query = 'SELECT * FROM stats ORDER BY id DESC LIMIT 1'
        stats = execute_sqlite_query(query, fetch_one=True)

        if stats:
            STATS['total_applications'] = stats.get('total_applications', 0)
            STATS['approved_applications'] = stats.get('approved_applications', 0)
            STATS['rejected_applications'] = stats.get('rejected_applications', 0)
            STATS['pending_applications'] = stats.get('pending_applications', 0)
            logger.info(f"Статистика загружена из базы данных: {STATS}")
        else:
            # Если статистики нет, создаем запись
            query = '''
            INSERT INTO stats (total_applications, approved_applications, rejected_applications,
                              pending_applications, last_updated)
            VALUES (?, ?, ?, ?, ?)
            '''
            params = (0, 0, 0, 0, datetime.datetime.now().isoformat())
            execute_sqlite_query(query, params)
            logger.info("Создана новая запись статистики в базе данных")
    except Exception as e:
        logger.error(f"Ошибка при загрузке статистики из базы данных: {e}")

def update_stats_in_db(stats):
    """Обновление статистики в базе данных"""
    try:
        query = '''
        INSERT INTO stats (total_applications, approved_applications, rejected_applications,
                          pending_applications, last_updated)
        VALUES (?, ?, ?, ?, ?)
        '''
        params = (
            stats['total_applications'],
            stats['approved_applications'],
            stats['rejected_applications'],
            stats['pending_applications'],
            datetime.datetime.now().isoformat()
        )
        execute_sqlite_query(query, params)
        logger.info(f"Статистика обновлена в базе данных: {stats}")
    except Exception as e:
        logger.error(f"Ошибка при обновлении статистики в базе данных: {e}")

def register_user(user_id, username, first_name, last_name):
    """Регистрация пользователя в объединенной базе данных"""
    try:
        # Проверяем, существует ли пользователь по telegram_id
        query = 'SELECT * FROM users WHERE telegram_id = ?'
        user = execute_sqlite_query(query, (user_id,), fetch_one=True)

        if user:
            # Обновляем информацию о пользователе
            query = '''
            UPDATE users
            SET username = ?, first_name = ?, last_name = ?, last_activity = ?, last_seen = ?
            WHERE telegram_id = ?
            '''
            params = (username, first_name, last_name,
                     datetime.datetime.now().isoformat(),
                     datetime.datetime.now().isoformat(),
                     user_id)
            execute_sqlite_query(query, params)
            logger.info(f"Обновлена информация о пользователе {user_id}")
        else:
            # Добавляем нового пользователя
            query = '''
            INSERT INTO users (telegram_id, username, first_name, last_name,
                             registration_date, last_activity, created_at, last_seen)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            '''
            now = datetime.datetime.now().isoformat()
            params = (
                user_id,
                username,
                first_name,
                last_name,
                now,
                now,
                now,
                now
            )
            execute_sqlite_query(query, params)
            logger.info(f"Зарегистрирован новый пользователь {user_id}")

        return True
    except Exception as e:
        safe_log_error(e, f"register_user_{user_id}")
        return False

def get_applications_by_status(status):
    """Получение заявок по статусу"""
    try:
        query = 'SELECT * FROM applications WHERE status = ? ORDER BY timestamp DESC'
        applications = execute_sqlite_query(query, (status,), fetch_all=True)
        return applications
    except Exception as e:
        logger.error(f"Ошибка при получении заявок со статусом {status}: {e}")
        return []

def update_application_status(application_id, status, processed_by, rejection_reason=None):
    """Обновление статуса заявки"""
    try:
        query = '''
        UPDATE applications
        SET status = ?, processed_by = ?, processed_at = ?, rejection_reason = ?
        WHERE id = ?
        '''
        params = (status, processed_by, datetime.datetime.now().isoformat(), rejection_reason, application_id)
        execute_sqlite_query(query, params)

        # Обновляем статистику
        if status == 'approved':
            STATS['approved_applications'] += 1
            STATS['pending_applications'] -= 1
        elif status == 'rejected':
            STATS['rejected_applications'] += 1
            STATS['pending_applications'] -= 1

        update_stats_in_db(STATS)

        logger.info(f"Обновлен статус заявки {application_id} на {status}")
        return True
    except Exception as e:
        logger.error(f"Ошибка при обновлении статуса заявки {application_id}: {e}")
        return False

# Обработчик команды /start
@sync_user
async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Обработчик команды /start"""
    user = update.effective_user
    user_id = user.id

    # Регистрируем пользователя в локальной БД
    register_user(user_id, user.username, user.first_name, user.last_name)

    # Синхронизируем с сайтом (если доступно)
    if WEBSITE_SYNC_AVAILABLE:
        try:
            await sync_user_to_website(user_id, user.username, user.first_name, user.last_name)
            logger.info(f"User {user_id} synced with website")
        except Exception as e:
            logger.error(f"Failed to sync user {user_id} with website: {e}")

    # Сбрасываем состояние пользователя
    user_states[user_id] = UserState.NONE
    if user_id in temp_applications:
        del temp_applications[user_id]

    # Отправляем приветственное сообщение с постоянными кнопками
    persistent_keyboard = create_persistent_keyboard()

    await update.message.reply_text(
        "🎮 <b>Добро пожаловать в PlayLand!</b>\n\n"
        "Используйте кнопки ниже для быстрого доступа к функциям бота, "
        "или нажмите \"🏠 Главное меню\" для полного меню с дополнительными опциями.\n\n"
        f"{'🔗 Синхронизация с сайтом активна' if WEBSITE_SYNC_AVAILABLE else '⚠️ Синхронизация с сайтом недоступна'}",
        parse_mode="HTML",
        reply_markup=persistent_keyboard
    )

    # Также показываем главное меню с inline кнопками
    await show_main_menu(update, context)

async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Обработчик команды /help"""
    await update.message.reply_text(
        "📚 <b>Список доступных команд:</b>\n\n"
        "🎮 <b>Основные команды:</b>\n"
        "/start - Главное меню бота\n"
        "/help - Показать эту справку\n"
        "/apply - Инструкции по подаче заявки\n"
        "/status - Проверить статус вашей заявки\n"
        "/info - Информация о сервере\n"
        "/stats - Статистика заявок\n\n"
        "📝 <b>Подача заявки:</b>\n"
        "• Используйте веб-форму через кнопку \"📝 Подать заявку\"\n"
        "• Заполните все обязательные поля\n"
        "• Дождитесь рассмотрения администрацией\n\n"
        "🌐 <b>Дополнительная информация:</b>\n"
        "• Карта сервера и правила доступны на нашем веб-сайте\n"
        "• Используйте кнопки меню для быстрого доступа к функциям\n\n"
        "❓ <b>Нужна помощь?</b> Используйте кнопку \"❓ Помощь\" в главном меню.",
        parse_mode="HTML"
    )

async def my_application(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Обработчик команды для проверки статуса заявки"""
    user_id = update.effective_user.id

    # Проверяем заявку пользователя из объединенной БД
    try:
        query = 'SELECT * FROM applications WHERE telegram_user_id = ? ORDER BY created_at DESC LIMIT 1'
        application = execute_sqlite_query(query, (user_id,), fetch_one=True)
    except Exception as e:
        safe_log_error(e, f"get_user_application_{user_id}")
        return

    if not application:
        await update.message.reply_text("У вас нет активной заявки. Используйте кнопку 'Подать заявку', чтобы создать новую.")
        return

    # Определяем статус заявки
    status = application.get('status', 'pending')
    if status == 'pending':
        status_text = "⏳ В процессе проверки"
    elif status == 'approved':
        status_text = "✅ Одобрена"
    elif status == 'rejected':
        status_text = "❌ Отклонена"
    else:
        status_text = "❓ Неизвестный статус"

    # Форматируем дату
    timestamp = application.get('timestamp', '')
    try:
        timestamp_dt = datetime.datetime.fromisoformat(timestamp)
        timestamp_formatted = timestamp_dt.strftime('%d.%m.%Y %H:%M')
    except:
        timestamp_formatted = timestamp

    # Формируем сообщение
    response = (
        f"📋 <b>Информация о вашей заявке:</b>\n\n"
        f"Никнейм: <code>{application.get('minecraft_nickname', 'Не указан')}</code>\n"
        f"Discord: <code>{application.get('discord_tag', 'Не указан')}</code>\n"
        f"Статус: {status_text}\n"
        f"Дата заявки: {timestamp_formatted}\n"
    )

    await update.message.reply_text(response, parse_mode="HTML")

@rate_limit("admin")
@admin_required
async def admin_panel(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Главная админ-панель с полным функционалом"""
    user_id = update.effective_user.id

    # Логируем доступ к админ-панели
    safe_log_user_action(user_id, "admin_panel_access")

    # Сбрасываем состояние админа
    admin_states[user_id] = AdminState.NONE
    if user_id in admin_temp_data:
        del admin_temp_data[user_id]

    await show_admin_main_menu(update, context)

async def show_admin_main_menu(update_or_query, context, edit_message=False):
    """Показывает главное меню админ-панели"""
    try:
        # Получаем статистику заявок
        pending_count = len(get_applications_by_status('pending'))
        approved_count = len(get_applications_by_status('approved'))
        rejected_count = len(get_applications_by_status('rejected'))
        total_count = pending_count + approved_count + rejected_count

        # Создаем клавиатуру админ-панели
        keyboard = [
            [InlineKeyboardButton(f"📋 Рассмотреть заявки ({pending_count})", callback_data="admin_review_applications")],
            [
                InlineKeyboardButton("📊 Статистика", callback_data="admin_statistics"),
                InlineKeyboardButton("🔍 Поиск", callback_data="admin_search")
            ],
            [
                InlineKeyboardButton("✅ Одобренные", callback_data="admin_view_approved"),
                InlineKeyboardButton("❌ Отклоненные", callback_data="admin_view_rejected")
            ],
            [
                InlineKeyboardButton("📜 История", callback_data="admin_view_history"),
                InlineKeyboardButton("🔧 Тест заявка", callback_data="test_application")
            ],
            [
                InlineKeyboardButton("⚙️ Настройки", callback_data="admin_settings"),
                create_main_menu_button()
            ]
        ]

        reply_markup = InlineKeyboardMarkup(keyboard)

        message_text = (
            "🔧 <b>Админ-панель PlayLand</b>\n\n"
            f"📊 <b>Статистика заявок:</b>\n"
            f"📝 Всего заявок: <b>{total_count}</b>\n"
            f"⏳ В обработке: <b>{pending_count}</b>\n"
            f"✅ Одобрено: <b>{approved_count}</b>\n"
            f"❌ Отклонено: <b>{rejected_count}</b>\n\n"
            f"💡 Выберите действие для управления заявками:"
        )

        if edit_message and hasattr(update_or_query, 'edit_message_text'):
            await update_or_query.edit_message_text(message_text, parse_mode="HTML", reply_markup=reply_markup)
        else:
            if hasattr(update_or_query, 'message'):
                await update_or_query.message.reply_text(message_text, parse_mode="HTML", reply_markup=reply_markup)
            else:
                await update_or_query.reply_text(message_text, parse_mode="HTML", reply_markup=reply_markup)

    except Exception as e:
        logger.error(f"Ошибка при отображении админ-панели: {e}")
        error_message = "❌ Произошла ошибка при отображении админ-панели. Попробуйте позже."

        if edit_message and hasattr(update_or_query, 'edit_message_text'):
            await update_or_query.edit_message_text(error_message)
        else:
            if hasattr(update_or_query, 'message'):
                await update_or_query.message.reply_text(error_message)
            else:
                await update_or_query.reply_text(error_message)

async def show_application_review(query, context, application_index=0):
    """Показывает заявку для рассмотрения"""
    user_id = query.from_user.id

    try:
        # Получаем заявки в ожидании
        pending_applications = get_applications_by_status('pending')

        if not pending_applications:
            keyboard = [[InlineKeyboardButton("🔙 Назад в админ-панель", callback_data="admin_main_menu")]]
            await query.edit_message_text(
                "📭 <b>Нет заявок на рассмотрение</b>\n\n"
                "В данный момент все заявки обработаны.",
                parse_mode="HTML",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
            return

        # Проверяем индекс
        if application_index >= len(pending_applications):
            application_index = 0
        elif application_index < 0:
            application_index = len(pending_applications) - 1

        # Сохраняем текущий индекс
        admin_temp_data[user_id] = {'current_app_index': application_index}

        # Получаем текущую заявку
        app = pending_applications[application_index]
        app_user_id = app.get('telegram_user_id') or app.get('user_id')  # Поддержка старых и новых заявок

        # Получаем информацию о пользователе
        user_query = 'SELECT * FROM users WHERE telegram_id = ?'
        user_info = execute_sqlite_query(user_query, (app_user_id,), fetch_one=True)

        # Форматируем дату
        timestamp = app.get('timestamp', '')
        try:
            timestamp_dt = datetime.datetime.fromisoformat(timestamp)
            timestamp_formatted = timestamp_dt.strftime('%d.%m.%Y в %H:%M')
        except:
            timestamp_formatted = timestamp

        # Обрезаем длинную причину
        reason = app.get('reason', 'Не указана')
        reason_display = reason if len(reason) <= 200 else reason[:197] + "..."

        # Формируем сообщение
        message = (
            f"📄 <b>Заявка #{app.get('id')}</b>\n"
            f"📍 <b>Заявка {application_index + 1} из {len(pending_applications)}</b>\n\n"
            f"👤 <b>Пользователь:</b>\n"
            f"• Имя: {user_info.get('first_name', '')} {user_info.get('last_name', '')}\n"
            f"• Username: @{user_info.get('username', 'нет')}\n"
            f"• ID: <code>{app_user_id}</code>\n\n"
            f"🎮 <b>Никнейм:</b> <code>{app.get('minecraft_nickname') or app.get('nickname', 'Не указан')}</code>\n"
            f"💬 <b>Discord:</b> <code>{app.get('discord_tag') or app.get('discord', 'Не указан')}</code>\n"
            f"📝 <b>Причина:</b>\n<i>{reason_display}</i>\n\n"
            f"📅 <b>Дата подачи:</b> {timestamp_formatted}"
        )

        # Создаем клавиатуру
        keyboard = [
            [
                InlineKeyboardButton("✅ Принять", callback_data=f"admin_approve_{app.get('id')}"),
                InlineKeyboardButton("❌ Отклонить", callback_data=f"admin_reject_{app.get('id')}")
            ]
        ]

        # Навигация между заявками
        nav_buttons = []
        if len(pending_applications) > 1:
            if application_index > 0:
                nav_buttons.append(InlineKeyboardButton("⬅️ Предыдущая", callback_data="admin_prev_app"))
            if application_index < len(pending_applications) - 1:
                nav_buttons.append(InlineKeyboardButton("➡️ Следующая", callback_data="admin_next_app"))

            if nav_buttons:
                keyboard.append(nav_buttons)

        # Дополнительные действия
        keyboard.extend([
            [
                InlineKeyboardButton("📋 Полная причина", callback_data=f"admin_full_reason_{app.get('id')}"),
                InlineKeyboardButton("👤 Профиль", callback_data=f"admin_user_profile_{app_user_id}")
            ],
            [InlineKeyboardButton("🔙 Назад в админ-панель", callback_data="admin_main_menu")]
        ])

        reply_markup = InlineKeyboardMarkup(keyboard)
        await query.edit_message_text(message, parse_mode="HTML", reply_markup=reply_markup)

    except Exception as e:
        logger.error(f"Ошибка при показе заявки для рассмотрения: {e}")
        keyboard = [[InlineKeyboardButton("🔙 Назад в админ-панель", callback_data="admin_main_menu")]]
        await query.edit_message_text(
            "❌ <b>Ошибка</b>\n\nНе удалось загрузить заявку для рассмотрения.",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

async def approve_application(query, context, app_id):
    """Одобряет заявку"""
    user_id = query.from_user.id

    try:
        # Показываем индикатор загрузки
        await query.edit_message_text(
            "⏳ <b>Обработка заявки...</b>\n\nОдобрение заявки, пожалуйста подождите.",
            parse_mode="HTML"
        )

        # Получаем информацию о заявке
        app_query = 'SELECT * FROM applications WHERE id = ?'
        application = execute_sqlite_query(app_query, (app_id,), fetch_one=True)

        if not application:
            await query.edit_message_text(
                "❌ <b>Ошибка</b>\n\nЗаявка не найдена.",
                parse_mode="HTML",
                reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Назад", callback_data="admin_review_applications")]])
            )
            return

        # Обновляем статус заявки
        update_query = 'UPDATE applications SET status = ?, admin_id = ?, processed_at = ? WHERE id = ?'
        execute_sqlite_query(update_query, ('approved', user_id, datetime.datetime.now().isoformat(), app_id))

        # Обновляем статистику
        STATS['pending_applications'] -= 1
        STATS['approved_applications'] += 1
        update_stats_in_db(STATS)

        # Получаем информацию о пользователе по telegram_user_id
        user_telegram_id = application.get('telegram_user_id') or application.get('user_id')
        user_query = 'SELECT * FROM users WHERE telegram_id = ?'
        user_info = execute_sqlite_query(user_query, (user_telegram_id,), fetch_one=True)

        # Уведомляем пользователя
        try:
            await context.bot.send_message(
                chat_id=user_telegram_id,
                text=(
                    "🎉 <b>Поздравляем!</b>\n\n"
                    "Ваша заявка на сервер PlayLand была <b>одобрена</b>!\n\n"
                    f"🎮 <b>Никнейм:</b> <code>{application.get('minecraft_nickname') or application.get('nickname', 'Не указан')}</code>\n"
                    f"🌐 <b>IP сервера:</b> <code>play.playland.ru</code>\n\n"
                    "Добро пожаловать в наше сообщество! 🎮"
                ),
                parse_mode="HTML"
            )
        except Exception as e:
            logger.error(f"Ошибка при отправке уведомления пользователю {application.get('user_id')}: {e}")

        # Показываем результат админу
        keyboard = [
            [InlineKeyboardButton("➡️ Следующая заявка", callback_data="admin_review_applications")],
            [InlineKeyboardButton("🔙 Назад в админ-панель", callback_data="admin_main_menu")]
        ]

        await query.edit_message_text(
            f"✅ <b>Заявка одобрена!</b>\n\n"
            f"📄 <b>Заявка #{app_id}</b>\n"
            f"👤 <b>Пользователь:</b> {user_info.get('first_name', '') if user_info else ''} {user_info.get('last_name', '') if user_info else ''}\n"
            f"🎮 <b>Никнейм:</b> <code>{application.get('minecraft_nickname') or application.get('nickname', 'Не указан')}</code>\n\n"
            f"✅ Пользователь уведомлен о принятии заявки.",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

        logger.info(f"Администратор {user_id} одобрил заявку #{app_id}")

    except Exception as e:
        logger.error(f"Ошибка при одобрении заявки {app_id}: {e}")
        keyboard = [[InlineKeyboardButton("🔙 Назад", callback_data="admin_review_applications")]]
        await query.edit_message_text(
            "❌ <b>Ошибка</b>\n\nНе удалось одобрить заявку. Попробуйте позже.",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

async def reject_application(query, context, app_id, rejection_reason=None):
    """Отклоняет заявку"""
    user_id = query.from_user.id

    try:
        # Показываем индикатор загрузки
        await query.edit_message_text(
            "⏳ <b>Обработка заявки...</b>\n\nОтклонение заявки, пожалуйста подождите.",
            parse_mode="HTML"
        )

        # Получаем информацию о заявке
        app_query = 'SELECT * FROM applications WHERE id = ?'
        application = execute_sqlite_query(app_query, (app_id,), fetch_one=True)

        if not application:
            await query.edit_message_text(
                "❌ <b>Ошибка</b>\n\nЗаявка не найдена.",
                parse_mode="HTML",
                reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Назад", callback_data="admin_review_applications")]])
            )
            return

        # Обновляем статус заявки с причиной отказа
        update_application_status(app_id, 'rejected', user_id, rejection_reason)

        # Получаем информацию о пользователе по telegram_user_id
        user_telegram_id = application.get('telegram_user_id') or application.get('user_id')
        user_query = 'SELECT * FROM users WHERE telegram_id = ?'
        user_info = execute_sqlite_query(user_query, (user_telegram_id,), fetch_one=True)

        # Формируем сообщение для пользователя
        user_message = (
            "😔 <b>Уведомление о заявке</b>\n\n"
            "К сожалению, ваша заявка на сервер PlayLand была <b>отклонена</b>.\n\n"
            f"🎮 <b>Никнейм:</b> <code>{application.get('minecraft_nickname') or application.get('nickname', 'Не указан')}</code>\n"
        )

        if rejection_reason:
            user_message += f"\n📝 <b>Причина отказа:</b>\n<i>{rejection_reason}</i>\n"

        user_message += (
            "\n💡 <b>Что делать дальше:</b>\n"
            "• Вы можете подать новую заявку\n"
            "• Убедитесь, что указали корректные данные\n"
            "• Напишите более подробную причину\n\n"
            "Удачи в следующий раз! 🍀"
        )

        # Уведомляем пользователя
        try:
            user_telegram_id = application.get('telegram_user_id') or application.get('user_id')
            await context.bot.send_message(
                chat_id=user_telegram_id,
                text=user_message,
                parse_mode="HTML"
            )
        except Exception as e:
            logger.error(f"Ошибка при отправке уведомления пользователю {user_telegram_id}: {e}")

        # Показываем результат админу
        keyboard = [
            [InlineKeyboardButton("➡️ Следующая заявка", callback_data="admin_review_applications")],
            [InlineKeyboardButton("🔙 Назад в админ-панель", callback_data="admin_main_menu")]
        ]

        admin_message = (
            f"❌ <b>Заявка отклонена</b>\n\n"
            f"📄 <b>Заявка #{app_id}</b>\n"
            f"👤 <b>Пользователь:</b> {user_info.get('first_name', '') if user_info else ''} {user_info.get('last_name', '') if user_info else ''}\n"
            f"🎮 <b>Никнейм:</b> <code>{application.get('minecraft_nickname') or application.get('nickname', 'Не указан')}</code>\n"
        )

        if rejection_reason:
            admin_message += f"\n📝 <b>Причина отказа:</b> <i>{rejection_reason}</i>\n"

        admin_message += "\n📨 Пользователь уведомлен об отклонении заявки."

        await query.edit_message_text(
            admin_message,
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

        logger.info(f"Администратор {user_id} отклонил заявку #{app_id}" + (f" с причиной: {rejection_reason}" if rejection_reason else ""))

    except Exception as e:
        logger.error(f"Ошибка при отклонении заявки {app_id}: {e}")
        keyboard = [[InlineKeyboardButton("🔙 Назад", callback_data="admin_review_applications")]]
        await query.edit_message_text(
            "❌ <b>Ошибка</b>\n\nНе удалось отклонить заявку. Попробуйте позже.",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

# УДАЛЕНА СТАРАЯ ФУНКЦИЯ handle_webapp_data - используем только handle_web_app_data

# Обработчик сообщений
async def handle_message(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Обработчик текстовых сообщений."""
    user = update.effective_user
    user_id = user.id

    logger.info(f"📨 Получено сообщение от пользователя {user_id}")

    # Проверяем, есть ли данные веб-приложения
    if update.message.web_app_data:
        logger.info(f"🌐 Обнаружены данные веб-приложения от пользователя {user_id}")
        await handle_web_app_data(update, context)
        return

    # Проверяем обычное текстовое сообщение
    text = update.message.text
    logger.info(f"💬 Текстовое сообщение от {user_id}: {text}")

    # Регистрируем пользователя
    register_user(user_id, user.username, user.first_name, user.last_name)

    # Многошаговый процесс больше не используется - только веб-форма

    # Проверяем состояние админа для ввода причины отказа
    admin_state = admin_states.get(user_id, AdminState.NONE)
    if admin_state == AdminState.WAITING_REJECTION_REASON and user_id in ADMIN_IDS:
        await handle_rejection_reason_input(update, context, text)
        return

    # Проверяем состояние создания тикета
    user_state = user_states.get(user_id, UserState.NONE)
    if user_state == UserState.TICKET_TITLE:
        await handle_ticket_title_input(update, context, text)
        return
    elif user_state == UserState.TICKET_CONTENT:
        await handle_ticket_content_input(update, context, text)
        return

    # Обработка команд в тексте сообщения
    if text:
        # Обработка постоянных кнопок
        if text == "📝 Подать заявку":
            await update.message.reply_text(
                "📝 <b>Подача заявки</b>\n\n"
                "Для подачи заявки используйте веб-форму.\n"
                "Нажмите кнопку \"📝 Подать заявку\" в главном меню для открытия формы.",
                parse_mode="HTML"
            )
            await show_main_menu(update, context)

        elif text == "📋 Моя заявка":
            await my_application(update, context)

        elif text == "📊 Статистика":
            await stats_command(update, context)

        elif text == "ℹ️ О сервере":
            await info_command(update, context)

        elif text == "❓ Помощь":
            await help_command(update, context)

        elif text == "🎫 Поддержка":
            await support_command(update, context)

        elif text == "🏠 Главное меню":
            await show_main_menu(update, context)

        # Обработка текстовых команд (старая логика)
        elif "заявка" in text.lower():
            # Показываем информацию о заявке
            await my_application(update, context)

        elif "админ" in text.lower() and user_id in ADMIN_IDS:
            # Показываем админ-панель
            await admin_panel(update, context)

        elif "заявки в обработке" in text and user_id in ADMIN_IDS:
            # Показываем информацию о заявках в обработке
            try:
                # Получаем количество заявок со статусом pending из БД
                query = 'SELECT COUNT(*) as count FROM applications WHERE status = "pending"'
                result = execute_sqlite_query(query, fetch_one=True)
                pending_count = result.get('count', 0) if result else 0

                if pending_count == 0:
                    await update.message.reply_text("📭 Нет заявок, ожидающих рассмотрения.")
                    return

                pending_message = (
                    f"📋 <b>Заявки в обработке ({pending_count}):</b>\n\n"
                    f"У вас есть {pending_count} заявок, ожидающих рассмотрения.\n"
                    f"Для рассмотрения заявок используйте команду /admin."
                )

                await update.message.reply_text(pending_message, parse_mode="HTML")
            except Exception as e:
                logger.error(f"Ошибка при получении заявок в обработке: {e}")
                await update.message.reply_text("Произошла ошибка при получении заявок. Попробуйте позже.")

        elif "вернуться в главное меню" in text.lower() or "меню" in text.lower():
            # Возвращаемся в главное меню - просто вызываем команду start
            await start(update, context)
        else:
            await update.message.reply_text(
                "🤔 Я вас не понимаю.\n\n"
                "Используйте кнопки меню или команды:\n"
                "/start - главное меню\n"
                "/help - справка"
            )

# Обработчики для многошагового процесса подачи заявки
async def handle_nickname_input(update: Update, context: ContextTypes.DEFAULT_TYPE, nickname: str):
    """Обработка ввода никнейма"""
    user_id = update.effective_user.id

    # Валидация никнейма
    if len(nickname) < 3 or len(nickname) > 16:
        keyboard = [
            [create_help_button("nickname")],
            [create_back_button()]
        ]
        await update.message.reply_text(
            "❌ <b>Неправильная длина никнейма</b>\n\n"
            "Никнейм должен содержать от 3 до 16 символов.\n"
            f"Ваш никнейм: <code>{nickname}</code> ({len(nickname)} символов)\n\n"
            "✍️ Попробуйте еще раз:",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
        return

    if not re.match(r'^[a-zA-Z0-9_]+$', nickname):
        keyboard = [
            [create_help_button("nickname")],
            [create_back_button()]
        ]
        await update.message.reply_text(
            "❌ <b>Недопустимые символы в никнейме</b>\n\n"
            "Никнейм может содержать только:\n"
            "• Латинские буквы (a-z, A-Z)\n"
            "• Цифры (0-9)\n"
            "• Знак подчеркивания (_)\n\n"
            "✍️ Попробуйте еще раз:",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
        return

    # Сохраняем никнейм и переходим к следующему шагу
    temp_applications[user_id]['nickname'] = nickname
    user_states[user_id] = UserState.APPLYING_DISCORD

    keyboard = [
        [create_help_button("discord")],
        [create_back_button()]
    ]

    await update.message.reply_text(
        f"✅ <b>Никнейм принят:</b> <code>{nickname}</code>\n\n"
        "📝 <b>Подача заявки - Шаг 2 из 3</b>\n\n"
        "💬 <b>Введите ваш Discord тег</b>\n\n"
        "📋 <i>Примеры правильных тегов:</i>\n"
        "• <code>username</code> (новый формат)\n"
        "• <code>username#1234</code> (старый формат)\n\n"
        "💡 <b>Пример:</b> <code>steve_player</code>\n\n"
        "✍️ Напишите ваш Discord тег:",
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

async def handle_discord_input(update: Update, context: ContextTypes.DEFAULT_TYPE, discord_tag: str):
    """Обработка ввода Discord тега"""
    user_id = update.effective_user.id

    # Базовая валидация Discord тега
    if len(discord_tag) < 2 or len(discord_tag) > 37:  # Discord username limits
        keyboard = [
            [create_help_button("discord")],
            [create_back_button()]
        ]
        await update.message.reply_text(
            "❌ <b>Неправильная длина Discord тега</b>\n\n"
            "Discord тег должен содержать от 2 до 37 символов.\n"
            f"Ваш тег: <code>{discord_tag}</code> ({len(discord_tag)} символов)\n\n"
            "✍️ Попробуйте еще раз:",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
        return

    # Сохраняем Discord и переходим к следующему шагу
    temp_applications[user_id]['discord'] = discord_tag
    user_states[user_id] = UserState.APPLYING_REASON

    keyboard = [
        [InlineKeyboardButton("💡 Примеры причин", callback_data="help_reason_examples")],
        [create_back_button()]
    ]

    await update.message.reply_text(
        f"✅ <b>Discord принят:</b> <code>{discord_tag}</code>\n\n"
        "📝 <b>Подача заявки - Шаг 3 из 3</b>\n\n"
        "📝 <b>Расскажите, почему хотите играть на нашем сервере</b>\n\n"
        "💡 <i>Хорошие причины:</i>\n"
        "• Интерес к совместной игре\n"
        "• Желание строить и развиваться\n"
        "• Поиск дружелюбного сообщества\n"
        "• Рекомендация друзей\n\n"
        "✍️ Напишите вашу причину (минимум 10 символов):",
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

async def handle_reason_input(update: Update, context: ContextTypes.DEFAULT_TYPE, reason: str):
    """Обработка ввода причины"""
    user_id = update.effective_user.id

    # Валидация причины
    if len(reason) < 10:
        keyboard = [
            [InlineKeyboardButton("💡 Примеры причин", callback_data="help_reason_examples")],
            [create_back_button()]
        ]
        await update.message.reply_text(
            "❌ <b>Слишком короткая причина</b>\n\n"
            "Пожалуйста, напишите более подробно (минимум 10 символов).\n"
            f"Сейчас: {len(reason)} символов\n\n"
            "✍️ Попробуйте еще раз:",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
        return

    if len(reason) > 500:
        keyboard = [
            [InlineKeyboardButton("💡 Примеры причин", callback_data="help_reason_examples")],
            [create_back_button()]
        ]
        await update.message.reply_text(
            "❌ <b>Слишком длинная причина</b>\n\n"
            "Пожалуйста, сократите текст (максимум 500 символов).\n"
            f"Сейчас: {len(reason)} символов\n\n"
            "✍️ Попробуйте еще раз:",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
        return

    # Сохраняем причину и показываем предварительный просмотр
    temp_applications[user_id]['reason'] = reason
    user_states[user_id] = UserState.CONFIRMING_APPLICATION

    # Показываем предварительный просмотр заявки
    await show_application_preview(update, context)

async def handle_rejection_reason_input(update: Update, context: ContextTypes.DEFAULT_TYPE, reason: str):
    """Обработка ввода причины отказа администратором"""
    user_id = update.effective_user.id

    # Проверяем, что пользователь - админ
    if user_id not in ADMIN_IDS:
        return

    # Получаем ID заявки из временных данных
    temp_data = admin_temp_data.get(user_id, {})
    app_id = temp_data.get('rejecting_app_id')

    if not app_id:
        await update.message.reply_text(
            "❌ <b>Ошибка</b>\n\nНе найдена заявка для отклонения. Попробуйте еще раз.",
            parse_mode="HTML"
        )
        admin_states[user_id] = AdminState.NONE
        if user_id in admin_temp_data:
            del admin_temp_data[user_id]
        return

    # Валидация причины
    if len(reason.strip()) < 5:
        await update.message.reply_text(
            "❌ <b>Слишком короткая причина</b>\n\n"
            "Причина отказа должна содержать минимум 5 символов.\n"
            "Пожалуйста, напишите более подробную причину:",
            parse_mode="HTML"
        )
        return

    if len(reason.strip()) > 500:
        await update.message.reply_text(
            "❌ <b>Слишком длинная причина</b>\n\n"
            "Причина отказа не должна превышать 500 символов.\n"
            "Пожалуйста, сократите текст:",
            parse_mode="HTML"
        )
        return

    # Очищаем состояние админа
    admin_states[user_id] = AdminState.NONE
    if user_id in admin_temp_data:
        del admin_temp_data[user_id]

    # Создаем фиктивный query объект для вызова reject_application
    class FakeQuery:
        def __init__(self, user):
            self.from_user = user

        async def edit_message_text(self, text, parse_mode=None, reply_markup=None):
            await update.message.reply_text(text, parse_mode=parse_mode, reply_markup=reply_markup)

    fake_query = FakeQuery(update.effective_user)

    # Отклоняем заявку с указанной причиной
    await reject_application(fake_query, context, app_id, reason.strip())

async def handle_ticket_title_input(update: Update, context: ContextTypes.DEFAULT_TYPE, title: str):
    """Обрабатывает ввод темы тикета"""
    user_id = update.effective_user.id

    if user_id not in temp_tickets:
        await update.message.reply_text(
            "❌ Сессия создания тикета истекла. Начните заново.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🎫 Создать тикет", callback_data="create_support_ticket")]
            ])
        )
        return

    title = title.strip()

    if len(title) < 5 or len(title) > 100:
        await update.message.reply_text(
            "❌ <b>Неправильная длина темы</b>\n\n"
            "Тема должна содержать от 5 до 100 символов.\n"
            f"Сейчас: {len(title)} символов\n\n"
            "✍️ Попробуйте еще раз:",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("❌ Отмена", callback_data="cancel_ticket_creation")]
            ])
        )
        return

    temp_tickets[user_id]['title'] = title
    user_states[user_id] = UserState.TICKET_CONTENT

    await update.message.reply_text(
        f"✅ <b>Тема принята:</b> {title}\n\n"
        "Теперь опишите вашу проблему или вопрос <b>подробно</b>:\n\n"
        "💡 <i>Чем подробнее опишете проблему, тем быстрее получите помощь</i>",
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup([
            [InlineKeyboardButton("❌ Отмена", callback_data="cancel_ticket_creation")]
        ])
    )

async def handle_ticket_content_input(update: Update, context: ContextTypes.DEFAULT_TYPE, content: str):
    """Обрабатывает ввод содержимого тикета"""
    user_id = update.effective_user.id

    if user_id not in temp_tickets:
        await update.message.reply_text(
            "❌ Сессия создания тикета истекла. Начните заново.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🎫 Создать тикет", callback_data="create_support_ticket")]
            ])
        )
        return

    content = content.strip()

    if len(content) < 10 or len(content) > 1000:
        await update.message.reply_text(
            "❌ <b>Неправильная длина описания</b>\n\n"
            "Описание должно содержать от 10 до 1000 символов.\n"
            f"Сейчас: {len(content)} символов\n\n"
            "✍️ Попробуйте еще раз:",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("❌ Отмена", callback_data="cancel_ticket_creation")]
            ])
        )
        return

    temp_tickets[user_id]['content'] = content

    # Показываем предварительный просмотр
    ticket_data = temp_tickets[user_id]

    keyboard = [
        [InlineKeyboardButton("✅ Создать тикет", callback_data="confirm_ticket_creation")],
        [InlineKeyboardButton("✏️ Изменить тему", callback_data="edit_ticket_title")],
        [InlineKeyboardButton("✏️ Изменить описание", callback_data="edit_ticket_content")],
        [InlineKeyboardButton("❌ Отмена", callback_data="cancel_ticket_creation")]
    ]

    await update.message.reply_text(
        f"📋 <b>Предварительный просмотр тикета:</b>\n\n"
        f"<b>Тема:</b> {ticket_data['title']}\n\n"
        f"<b>Описание:</b>\n{ticket_data['content']}\n\n"
        "Всё верно?",
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

async def create_test_application(query, context):
    """Создает тестовую заявку для проверки системы"""
    user_id = query.from_user.id
    user = query.from_user

    try:
        # Проверяем, есть ли уже активная заявка
        existing_query = 'SELECT * FROM applications WHERE user_id = ? AND status = "pending" ORDER BY timestamp DESC LIMIT 1'
        existing_app = execute_sqlite_query(existing_query, (user_id,), fetch_one=True)

        if existing_app:
            keyboard = [
                [InlineKeyboardButton("📋 Посмотреть заявку", callback_data="my_application")],
                [create_back_button()]
            ]
            await query.edit_message_text(
                "⚠️ <b>У вас уже есть активная заявка</b>\n\n"
                "Вы можете подать новую заявку только после рассмотрения текущей.",
                parse_mode="HTML",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
            return

        # Регистрируем пользователя
        register_user(user_id, user.username, user.first_name, user.last_name)

        # Создаем тестовую заявку
        test_nickname = f"TestUser{user_id % 1000}"
        test_discord = "testuser#1234"
        test_reason = "Это тестовая заявка для проверки работы системы."

        # Сохраняем заявку в базу данных
        insert_query = '''
        INSERT INTO applications (user_id, nickname, discord, reason, status, timestamp)
        VALUES (?, ?, ?, ?, ?, ?)
        '''
        params = (
            user_id,
            test_nickname,
            test_discord,
            test_reason,
            'pending',
            datetime.datetime.now().isoformat()
        )

        application_id = execute_sqlite_query(insert_query, params)

        # Обновление статистики
        STATS['total_applications'] += 1
        STATS['pending_applications'] += 1
        update_stats_in_db(STATS)

        # Отправляем подтверждение
        confirmation_message = (
            "✅ <b>Тестовая заявка создана!</b>\n\n"
            f"📄 <b>Номер заявки:</b> #{application_id}\n"
            f"🎮 <b>Никнейм:</b> <code>{test_nickname}</code>\n"
            f"💬 <b>Discord:</b> <code>{test_discord}</code>\n"
            f"📝 <b>Причина:</b> <i>{test_reason}</i>\n\n"
            "⏳ <b>Статус:</b> В процессе проверки\n"
            "📅 <b>Дата подачи:</b> " + datetime.datetime.now().strftime('%d.%m.%Y в %H:%M') + "\n\n"
            "🔔 Это тестовая заявка для проверки работы системы.\n"
            "📋 Проверить статус можно через кнопку 'Моя заявка'."
        )

        keyboard = [
            [InlineKeyboardButton("📋 Моя заявка", callback_data="my_application")],
            [create_back_button()]
        ]

        await query.edit_message_text(
            confirmation_message,
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

        # Уведомляем администраторов
        for admin_id in ADMIN_IDS:
            try:
                await context.bot.send_message(
                    chat_id=admin_id,
                    text=(
                        "🔧 <b>Новая тестовая заявка!</b>\n\n"
                        f"👤 <b>От пользователя:</b> {user.first_name} {user.last_name or ''}\n"
                        f"🆔 <b>ID:</b> <code>{user_id}</code>\n"
                        f"👤 <b>Username:</b> @{user.username or 'нет'}\n"
                        f"🎮 <b>Никнейм:</b> <code>{test_nickname}</code>\n"
                        f"💬 <b>Discord:</b> <code>{test_discord}</code>\n"
                        f"📝 <b>Причина:</b> <i>{test_reason}</i>\n\n"
                        f"Используйте команду /admin для рассмотрения заявки."
                    ),
                    parse_mode="HTML"
                )
            except Exception as e:
                logger.error(f"Ошибка при отправке уведомления админу {admin_id}: {e}")

        logger.info(f"✅ Тестовая заявка создана для пользователя {user_id}, ID заявки: {application_id}")

    except Exception as e:
        logger.error(f"❌ Ошибка при создании тестовой заявки: {e}")
        keyboard = [[create_back_button()]]
        await query.edit_message_text(
            "❌ <b>Ошибка</b>\n\nНе удалось создать тестовую заявку. Попробуйте позже.",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

async def show_application_preview(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Показывает предварительный просмотр заявки перед отправкой"""
    user_id = update.effective_user.id
    app_data = temp_applications.get(user_id, {})

    nickname = app_data.get('nickname', 'Не указан')
    discord = app_data.get('discord', 'Не указан')
    reason = app_data.get('reason', 'Не указана')

    # Обрезаем длинную причину для предварительного просмотра
    reason_preview = reason if len(reason) <= 100 else reason[:97] + "..."

    keyboard = [
        [
            InlineKeyboardButton("✅ Отправить заявку", callback_data="confirm_application"),
            InlineKeyboardButton("✏️ Изменить", callback_data="edit_application")
        ],
        [create_back_button()]
    ]

    message_text = (
        "📋 <b>Предварительный просмотр заявки</b>\n\n"
        f"🎮 <b>Никнейм:</b> <code>{nickname}</code>\n"
        f"💬 <b>Discord:</b> <code>{discord}</code>\n"
        f"📝 <b>Причина:</b> <i>{reason_preview}</i>\n\n"
        "✅ Проверьте данные и подтвердите отправку заявки.\n"
        "После отправки заявка будет рассмотрена администрацией."
    )

    await update.message.reply_text(
        message_text,
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

async def submit_application(query, context, user_id):
    """Отправляет заявку в базу данных"""
    try:
        app_data = temp_applications.get(user_id, {})

        if not app_data or not all(key in app_data for key in ['nickname', 'discord', 'reason']):
            await query.edit_message_text(
                "❌ <b>Ошибка</b>\n\nДанные заявки неполные. Попробуйте заполнить заявку заново.",
                parse_mode="HTML",
                reply_markup=InlineKeyboardMarkup([[create_back_button()]])
            )
            return

        # Показываем индикатор загрузки
        await query.edit_message_text(
            "⏳ <b>Отправка заявки...</b>\n\nПожалуйста, подождите.",
            parse_mode="HTML"
        )

        # Создание новой заявки в БД (используем правильные названия столбцов)
        insert_query = '''
        INSERT INTO applications (telegram_user_id, minecraft_nickname, discord_tag, reason, status, timestamp)
        VALUES (?, ?, ?, ?, ?, ?)
        '''
        params = (
            user_id,
            app_data['nickname'],
            app_data['discord'],
            app_data['reason'],
            'pending',
            datetime.datetime.now().isoformat()
        )

        application_id = execute_sqlite_query(insert_query, params)

        # Обновление статистики
        STATS['total_applications'] += 1
        STATS['pending_applications'] += 1
        update_stats_in_db(STATS)

        # Очищаем временные данные
        user_states[user_id] = UserState.NONE
        if user_id in temp_applications:
            del temp_applications[user_id]

        # Отправляем сообщение об успехе
        keyboard = [
            [InlineKeyboardButton("📋 Проверить статус", callback_data="my_application")],
            [create_main_menu_button()]
        ]

        await query.edit_message_text(
            "✅ <b>Заявка успешно отправлена!</b>\n\n"
            f"🎮 <b>Никнейм:</b> <code>{app_data['nickname']}</code>\n"
            f"💬 <b>Discord:</b> <code>{app_data['discord']}</code>\n\n"
            "📋 Ваша заявка передана на рассмотрение администрации.\n"
            "⏱️ Обычно рассмотрение занимает от 1 до 24 часов.\n\n"
            "🔔 Мы уведомим вас о результате рассмотрения!",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

        logger.info(f"Заявка от пользователя {user_id} успешно создана")

        # Уведомление администраторов о новой заявке
        user_info = query.from_user
        for admin_id in ADMIN_IDS:
            try:
                await context.bot.send_message(
                    chat_id=admin_id,
                    text=f"📝 <b>Новая заявка на рассмотрение!</b>\n\n"
                         f"👤 <b>От пользователя:</b> {user_info.first_name} {user_info.last_name or ''}\n"
                         f"🆔 <b>ID:</b> <code>{user_id}</code>\n"
                         f"👤 <b>Username:</b> @{user_info.username or 'нет'}\n"
                         f"🎮 <b>Никнейм:</b> <code>{app_data['nickname']}</code>\n"
                         f"💬 <b>Discord:</b> <code>{app_data['discord']}</code>\n"
                         f"📝 <b>Причина:</b> <i>{app_data['reason']}</i>\n\n"
                         f"Используйте команду /admin для рассмотрения заявки.",
                    parse_mode="HTML"
                )
                logger.info(f"Уведомление о новой заявке отправлено администратору {admin_id}")
            except Exception as e:
                logger.error(f"Ошибка при отправке уведомления администратору {admin_id}: {e}")

    except Exception as e:
        safe_log_error(e, "create_application")
        logger.exception("Полный стек ошибки:")

        keyboard = [[create_back_button()]]
        await query.edit_message_text(
            "❌ <b>Ошибка при отправке заявки</b>\n\n"
            "Произошла техническая ошибка. Пожалуйста, попробуйте позже или обратитесь к администратору.",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

# Дополнительные команды
async def apply_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Обработчик команды /apply"""
    await update.message.reply_text(
        "📝 Для подачи заявки используйте кнопку 'Подать заявку' в главном меню.\n"
        "Отправьте команду /start для отображения меню."
    )

async def info_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Обработчик команды /info"""
    await update.message.reply_text(
        "ℹ️ <b>Информация о сервере PlayLand</b>\n\n"
        "🎮 Версия Minecraft: 1.20.1\n"
        "🌍 Режим игры: Survival\n"
        "👥 Максимум игроков: 50\n"
        "🔧 Модификации: Vanilla+\n\n"
        "📋 Для подачи заявки используйте команду /start",
        parse_mode="HTML"
    )

async def stats_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Обработчик команды /stats"""
    try:
        # Получаем актуальную статистику из БД
        pending_count = len(get_applications_by_status('pending'))
        approved_count = len(get_applications_by_status('approved'))
        rejected_count = len(get_applications_by_status('rejected'))
        total_count = pending_count + approved_count + rejected_count

        await update.message.reply_text(
            f"📊 <b>Статистика сервера PlayLand</b>\n\n"
            f"📝 Всего заявок: {total_count}\n"
            f"✅ Одобрено: {approved_count}\n"
            f"❌ Отклонено: {rejected_count}\n"
            f"⏳ В обработке: {pending_count}\n",
            parse_mode="HTML"
        )
    except Exception as e:
        logger.error(f"Ошибка при получении статистики: {e}")
        await update.message.reply_text("❌ Ошибка при получении статистики. Попробуйте позже.")

@sync_user
async def support_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Обработчик команды /support - система поддержки"""
    user = update.effective_user
    user_id = user.id

    keyboard = [
        [InlineKeyboardButton("🎫 Создать тикет", callback_data="create_support_ticket")],
        [InlineKeyboardButton("📋 Мои тикеты", callback_data="my_tickets")],
        [InlineKeyboardButton("❓ Часто задаваемые вопросы", callback_data="support_faq")]
    ]

    await update.message.reply_text(
        "🎫 <b>Система поддержки PlayLand</b>\n\n"
        "Здесь вы можете:\n"
        "• Создать тикет для обращения в поддержку\n"
        "• Просмотреть свои существующие тикеты\n"
        "• Найти ответы на частые вопросы\n\n"
        f"{'🔗 Тикеты синхронизируются с сайтом' if WEBSITE_SYNC_AVAILABLE else '⚠️ Только локальные тикеты'}",
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

# Временные данные для тикетов
temp_tickets = {}

# Функции валидации
def validate_nickname(nickname):
    """Валидация никнейма Minecraft"""
    if not nickname or len(nickname) < 3 or len(nickname) > 16:
        return False
    return bool(re.match(r'^[a-zA-Z0-9_]+$', nickname))

def validate_discord(discord_tag):
    """Валидация Discord тега"""
    if not discord_tag or len(discord_tag) < 2 or len(discord_tag) > 37:
        return False
    # Поддерживаем как старый формат (username#1234), так и новый (username)
    old_format = re.match(r'^.{2,32}#\d{4}$', discord_tag)
    new_format = re.match(r'^[a-zA-Z0-9_.]{2,32}$', discord_tag)
    return bool(old_format or new_format)

def validate_reason(reason):
    """Валидация причины"""
    if not reason or len(reason) < 10 or len(reason) > 500:
        return False
    return True

# Обработчик данных веб-приложения
@rate_limit("webapp")
async def handle_web_app_data(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Обработчик данных, полученных из веб-приложения"""
    user_id = update.effective_user.id

    logger.info(f"🔔 ОБРАБОТЧИК WEBAPP ВЫЗВАН! Пользователь: {user_id}")
    logger.info(f"🔍 Тип update: {type(update)}")
    logger.info(f"🔍 Есть effective_message: {hasattr(update, 'effective_message') and update.effective_message is not None}")

    if update.effective_message:
        logger.info(f"🔍 Есть web_app_data: {hasattr(update.effective_message, 'web_app_data') and update.effective_message.web_app_data is not None}")

    safe_log_user_action(user_id, "webapp_data_received")

    try:
        # Получаем данные из веб-приложения
        web_app_data = update.effective_message.web_app_data
        if not web_app_data:
            safe_log_error(Exception("No webapp data"), "handle_web_app_data")
            await update.effective_message.reply_text(
                "❌ <b>Ошибка</b>\n\nНе получены данные из веб-формы. Попробуйте еще раз.",
                parse_mode="HTML"
            )
            return

        # Валидация данных веб-приложения (если доступен модуль безопасности)
        if SECURITY_AVAILABLE:
            security_manager = getattr(context.bot_data, 'security_manager', None)
            if security_manager:
                is_valid, validated_data = security_manager.validate_webapp_data(web_app_data.data)
                if not is_valid:
                    safe_log_error(Exception("Invalid webapp signature"), "handle_web_app_data")
                    await update.effective_message.reply_text(
                        "❌ <b>Ошибка безопасности</b>\n\nДанные не прошли проверку подлинности.",
                        parse_mode="HTML"
                    )
                    return

        logger.info(f"📦 Сырые данные веб-приложения: {web_app_data.data}")

        # Парсим JSON данные
        data = json.loads(web_app_data.data)
        logger.info(f"📋 Распарсенные данные из веб-приложения от пользователя {user_id}: {data}")

        # Валидируем данные
        nickname = data.get('nickname', '').strip()
        discord = data.get('discord', '').strip()
        reason = data.get('reason', '').strip()

        logger.info(f"🔍 Валидация данных: nickname='{nickname}', discord='{discord}', reason='{reason[:50]}...'")

        # Валидация никнейма
        if not validate_nickname(nickname):
            logger.warning(f"❌ Валидация никнейма не прошла: {nickname}")
            await update.effective_message.reply_text(
                "❌ <b>Ошибка валидации</b>\n\n"
                "Никнейм не соответствует требованиям:\n"
                "• От 3 до 16 символов\n"
                "• Только латинские буквы, цифры и знак _\n"
                "• Без пробелов и специальных символов",
                parse_mode="HTML"
            )
            return

        # Валидация Discord (если указан)
        if discord and not validate_discord(discord):
            logger.warning(f"❌ Валидация Discord не прошла: {discord}")
            await update.effective_message.reply_text(
                "❌ <b>Ошибка валидации</b>\n\n"
                "Discord тег не соответствует требованиям:\n"
                "• От 2 до 37 символов\n"
                "• Может содержать буквы, цифры, точки, подчеркивания\n"
                "• Может быть в формате username или username#1234",
                parse_mode="HTML"
            )
            return

        # Валидация причины (если указана) - убираем обязательность
        if reason and len(reason.strip()) > 500:
            logger.warning(f"❌ Причина слишком длинная: {len(reason)} символов")
            await update.effective_message.reply_text(
                "❌ <b>Ошибка валидации</b>\n\n"
                "Причина не должна превышать 500 символов.",
                parse_mode="HTML"
            )
            return

        # Проверяем, есть ли уже активная заявка
        try:
            existing_query = 'SELECT * FROM applications WHERE telegram_user_id = ? AND status = "pending" ORDER BY timestamp DESC LIMIT 1'
            existing_app = execute_sqlite_query(existing_query, (user_id,), fetch_one=True)

            if existing_app:
                logger.info(f"⚠️ У пользователя {user_id} уже есть активная заявка")
                await update.effective_message.reply_text(
                    "⚠️ <b>У вас уже есть активная заявка</b>\n\n"
                    "Вы можете подать новую заявку только после рассмотрения текущей.\n"
                    "Проверьте статус вашей заявки через бота.",
                    parse_mode="HTML"
                )
                return
        except Exception as e:
            logger.error(f"Ошибка при проверке существующей заявки: {e}")

        # Регистрируем пользователя
        user = update.effective_user
        register_user(user_id, user.username, user.first_name, user.last_name)

        # Сохраняем заявку в базу данных
        insert_query = '''
        INSERT INTO applications (telegram_user_id, minecraft_nickname, discord_tag, reason, status, timestamp)
        VALUES (?, ?, ?, ?, ?, ?)
        '''
        params = (
            user_id,
            nickname,
            discord if discord else None,
            reason if reason else None,
            'pending',
            datetime.datetime.now().isoformat()
        )

        application_id = execute_sqlite_query(insert_query, params)
        logger.info(f"💾 Заявка сохранена в БД с ID: {application_id}")

        # Обновление статистики
        STATS['total_applications'] += 1
        STATS['pending_applications'] += 1
        update_stats_in_db(STATS)

        # Отправляем подтверждение пользователю
        confirmation_message = (
            "✅ <b>Заявка успешно отправлена!</b>\n\n"
            f"📄 <b>Номер заявки:</b> #{application_id}\n"
            f"🎮 <b>Никнейм:</b> <code>{nickname}</code>\n"
        )

        if discord:
            confirmation_message += f"💬 <b>Discord:</b> <code>{discord}</code>\n"

        if reason:
            reason_preview = reason if len(reason) <= 100 else reason[:97] + "..."
            confirmation_message += f"📝 <b>Причина:</b> <i>{reason_preview}</i>\n"

        confirmation_message += (
            "\n⏳ <b>Статус:</b> В процессе проверки\n"
            "📅 <b>Дата подачи:</b> " + datetime.datetime.now().strftime('%d.%m.%Y в %H:%M') + "\n\n"
            "🔔 Вы получите уведомление, когда администрация рассмотрит вашу заявку.\n"
            "📋 Проверить статус можно через кнопку 'Моя заявка' в боте."
        )

        await update.effective_message.reply_text(confirmation_message, parse_mode="HTML")

        # Уведомляем администраторов
        user_info = update.effective_user
        for admin_id in ADMIN_IDS:
            try:
                await context.bot.send_message(
                    chat_id=admin_id,
                    text=(
                        "📝 <b>Новая заявка через веб-форму!</b>\n\n"
                        f"👤 <b>От пользователя:</b> {user_info.first_name} {user_info.last_name or ''}\n"
                        f"🆔 <b>ID:</b> <code>{user_id}</code>\n"
                        f"👤 <b>Username:</b> @{user_info.username or 'нет'}\n"
                        f"🎮 <b>Никнейм:</b> <code>{nickname}</code>\n"
                        f"💬 <b>Discord:</b> <code>{discord or 'не указан'}</code>\n"
                        f"📝 <b>Причина:</b> <i>{reason or 'не указана'}</i>\n\n"
                        f"Используйте команду /admin для рассмотрения заявки."
                    ),
                    parse_mode="HTML"
                )
            except Exception as e:
                logger.error(f"Ошибка при отправке уведомления админу {admin_id}: {e}")

        logger.info(f"✅ Заявка из веб-приложения успешно обработана для пользователя {user_id}, ID заявки: {application_id}")

    except json.JSONDecodeError as e:
        logger.error(f"❌ Ошибка при парсинге JSON из веб-приложения: {e}")
        logger.error(f"Сырые данные: {web_app_data.data if 'web_app_data' in locals() else 'Нет данных'}")
        await update.effective_message.reply_text(
            "❌ <b>Ошибка обработки данных</b>\n\n"
            "Не удалось обработать данные из веб-формы. Попробуйте еще раз.",
            parse_mode="HTML"
        )
    except Exception as e:
        logger.error(f"❌ Ошибка при обработке данных веб-приложения: {e}")
        logger.exception("Полная трассировка ошибки:")
        await update.effective_message.reply_text(
            "❌ <b>Ошибка сервера</b>\n\n"
            "Произошла ошибка при обработке вашей заявки. Попробуйте позже.",
            parse_mode="HTML"
        )

# Состояния пользователей для многошагового процесса
user_states = {}

# Константы для состояний
class UserState:
    NONE = "none"
    APPLYING_NICKNAME = "applying_nickname"
    APPLYING_DISCORD = "applying_discord"
    APPLYING_REASON = "applying_reason"
    CONFIRMING_APPLICATION = "confirming_application"
    CREATING_TICKET = "creating_ticket"
    TICKET_TITLE = "ticket_title"
    TICKET_CONTENT = "ticket_content"

# Временные данные заявок
temp_applications = {}

# Состояния админ-панели
admin_states = {}
admin_temp_data = {}

# Состояния пользователей для многошагового процесса
user_states = {}
current_application_index = {}

class AdminState:
    NONE = "none"
    REVIEWING_APPLICATIONS = "reviewing_applications"
    ADDING_REJECTION_COMMENT = "adding_rejection_comment"
    SEARCHING_APPLICATIONS = "searching_applications"
    WAITING_REJECTION_REASON = "waiting_rejection_reason"

# Временные данные для админов
admin_temp_data = {}

def create_back_button(callback_data="main_menu"):
    """Создает кнопку 'Назад' для навигации"""
    return InlineKeyboardButton("🔙 Назад", callback_data=callback_data)

def create_main_menu_button():
    """Создает кнопку 'В главное меню'"""
    return InlineKeyboardButton("🏠 Главное меню", callback_data="main_menu")

def create_help_button(section="general"):
    """Создает кнопку помощи для конкретного раздела"""
    return InlineKeyboardButton("❓ Помощь", callback_data=f"help_{section}")

def create_persistent_keyboard():
    """Создает постоянную клавиатуру с основными функциями"""
    keyboard = [
        [
            KeyboardButton("📝 Подать заявку"),
            KeyboardButton("📋 Моя заявка")
        ],
        [
            KeyboardButton("🎫 Поддержка"),
            KeyboardButton("📊 Статистика")
        ],
        [
            KeyboardButton("ℹ️ О сервере"),
            KeyboardButton("❓ Помощь")
        ],
        [
            KeyboardButton("🏠 Главное меню")
        ]
    ]
    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=False)

async def show_main_menu(update_or_query, context, edit_message=False):
    """Показывает главное меню бота"""
    user_id = update_or_query.from_user.id if hasattr(update_or_query, 'from_user') else update_or_query.message.from_user.id

    # Создаем клавиатуру с кнопками в 2x2 формате
    keyboard = [
        [
            InlineKeyboardButton("📝 Подать заявку", web_app=WebAppInfo(url=WEBAPP_URL)),
            InlineKeyboardButton("📋 Моя заявка", callback_data="my_application")
        ],
        [
            InlineKeyboardButton("🎫 Поддержка", callback_data="create_support_ticket"),
            InlineKeyboardButton("📊 Статистика", callback_data="server_stats")
        ],
        [
            InlineKeyboardButton("ℹ️ О сервере", callback_data="server_info"),
            InlineKeyboardButton("❓ Помощь", callback_data="help_general")
        ]
    ]

    # Если пользователь администратор, добавляем кнопку админ-панели
    if user_id in ADMIN_IDS:
        keyboard.append([InlineKeyboardButton("🔧 Админ-панель", callback_data="admin_panel")])

    reply_markup = InlineKeyboardMarkup(keyboard)

    message_text = (
        "🎮 <b>Добро пожаловать в PlayLand!</b>\n\n"
        "Это официальный бот сервера PlayLand. Здесь вы можете:\n\n"
        "📝 <b>Подать заявку</b> через удобную веб-форму\n"
        "📋 <b>Проверить статус</b> вашей заявки\n"
        "ℹ️ <b>Узнать информацию</b> о сервере\n"
        "📊 <b>Посмотреть статистику</b> заявок\n"
        "❓ <b>Получить помощь</b> по использованию бота\n\n"
        "💡 <b>Для подачи заявки нажмите кнопку \"📝 Подать заявку\" - откроется красивая веб-форма!</b>\n\n"
        "Выберите нужное действие из меню ниже:"
    )

    if edit_message and hasattr(update_or_query, 'edit_message_text'):
        await update_or_query.edit_message_text(message_text, parse_mode="HTML", reply_markup=reply_markup)
    else:
        if hasattr(update_or_query, 'message'):
            await update_or_query.message.reply_text(message_text, parse_mode="HTML", reply_markup=reply_markup)
        else:
            await update_or_query.reply_text(message_text, parse_mode="HTML", reply_markup=reply_markup)

# Обработчик callback кнопок
async def button_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Обработчик нажатий на inline кнопки"""
    query = update.callback_query
    await query.answer()

    user_id = query.from_user.id
    data = query.data

    # Главное меню
    if data == "main_menu":
        user_states[user_id] = UserState.NONE
        if user_id in temp_applications:
            del temp_applications[user_id]
        await show_main_menu(query, context, edit_message=True)
        return

    # Тестовая заявка для проверки системы (только для админов)
    elif data == "test_application":
        if user_id in ADMIN_IDS:
            await create_test_application(query, context)
        else:
            await query.edit_message_text(
                "🚫 <b>Доступ запрещен</b>\n\n"
                "Тестовая функция доступна только администраторам.\n"
                "Используйте веб-форму для подачи заявки.",
                parse_mode="HTML",
                reply_markup=InlineKeyboardMarkup([[create_back_button()]])
            )

    # Просмотр заявки
    elif data == "my_application":
        try:
            app_query = 'SELECT * FROM applications WHERE telegram_user_id = ? ORDER BY timestamp DESC LIMIT 1'
            application = execute_sqlite_query(app_query, (user_id,), fetch_one=True)
        except Exception as e:
            logger.error(f"Ошибка при получении заявки из БД: {e}")
            keyboard = [[create_back_button()]]
            await query.edit_message_text(
                "❌ <b>Ошибка</b>\n\nНе удалось получить данные заявки.\nПопробуйте позже.",
                parse_mode="HTML",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
            return

        if not application:
            keyboard = [
                [InlineKeyboardButton("📝 Подать заявку", callback_data="apply_start")],
                [create_back_button()]
            ]
            await query.edit_message_text(
                "📭 <b>У вас нет заявок</b>\n\n"
                "Вы еще не подавали заявку на сервер.\n"
                "Хотите подать заявку сейчас?",
                parse_mode="HTML",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
            return

        # Определяем статус заявки
        status = application.get('status', 'pending')
        if status == 'pending':
            status_text = "⏳ В процессе проверки"
            status_emoji = "⏳"
        elif status == 'approved':
            status_text = "✅ Одобрена"
            status_emoji = "✅"
        elif status == 'rejected':
            status_text = "❌ Отклонена"
            status_emoji = "❌"
        else:
            status_text = "❓ Неизвестный статус"
            status_emoji = "❓"

        # Форматируем дату
        timestamp = application.get('timestamp', '')
        try:
            timestamp_dt = datetime.datetime.fromisoformat(timestamp)
            timestamp_formatted = timestamp_dt.strftime('%d.%m.%Y в %H:%M')
        except:
            timestamp_formatted = timestamp

        # Формируем сообщение (используем правильные названия столбцов)
        response = (
            f"{status_emoji} <b>Ваша заявка</b>\n\n"
            f"🎮 <b>Никнейм:</b> <code>{application.get('minecraft_nickname', 'Не указан')}</code>\n"
            f"💬 <b>Discord:</b> <code>{application.get('discord_tag', 'Не указан')}</code>\n"
            f"📝 <b>Причина:</b> <i>{application.get('reason', 'Не указана')}</i>\n\n"
            f"📅 <b>Подана:</b> {timestamp_formatted}\n"
            f"📊 <b>Статус:</b> {status_text}\n"
        )

        keyboard = []
        if status == 'pending':
            response += "\n💡 <i>Ваша заявка рассматривается администрацией. Ожидайте ответа.</i>"
        elif status == 'approved':
            response += "\n🎉 <i>Поздравляем! Добро пожаловать на сервер PlayLand!</i>"
        elif status == 'rejected':
            response += "\n😔 <i>К сожалению, ваша заявка была отклонена.</i>"

            # Показываем причину отказа, если она есть
            rejection_reason = application.get('rejection_reason')
            if rejection_reason:
                response += f"\n\n📝 <b>Причина отказа:</b>\n<i>{rejection_reason}</i>"

            response += "\n\n💡 Вы можете подать новую заявку, исправив указанные недочеты."
            keyboard.append([InlineKeyboardButton("📝 Подать новую заявку", web_app=WebAppInfo(url=WEBAPP_URL))])

        keyboard.append([create_back_button()])
        await query.edit_message_text(response, parse_mode="HTML", reply_markup=InlineKeyboardMarkup(keyboard))

    # Система поддержки - тикеты
    elif data == "create_support_ticket":
        user_states[user_id] = UserState.TICKET_TITLE
        temp_tickets[user_id] = {}

        await query.edit_message_text(
            "🎫 <b>Создание тикета поддержки</b>\n\n"
            "Пожалуйста, укажите <b>тему</b> вашего обращения:\n"
            "(например: 'Проблема с входом на сервер', 'Вопрос по правилам')",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("❌ Отмена", callback_data="cancel_ticket_creation")]
            ])
        )

    elif data == "my_tickets":
        await query.edit_message_text(
            "📋 <b>Мои тикеты</b>\n\n"
            "⚠️ Функция в разработке.\n"
            "Пока что тикеты доступны только на сайте.\n\n"
            "🌐 Перейдите на сайт в раздел \"Поддержка\"" if WEBSITE_SYNC_AVAILABLE else "📧 Обратитесь к администрации",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🔙 Назад", callback_data="main_menu")]
            ])
        )

    elif data == "support_faq":
        await query.edit_message_text(
            "❓ <b>Часто задаваемые вопросы</b>\n\n"
            "🤔 <b>Как создать тикет?</b>\n"
            "Используйте команду /support или кнопку 'Создать тикет'\n\n"
            "🤔 <b>Как быстро отвечают?</b>\n"
            "Обычно в течение 1-24 часов\n\n"
            "🤔 <b>Можно ли отменить тикет?</b>\n"
            "Да, напишите об этом в тикете\n\n"
            "🤔 <b>Где посмотреть историю?</b>\n"
            "На сайте в разделе 'Поддержка'",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🎫 Создать тикет", callback_data="create_support_ticket")],
                [InlineKeyboardButton("🔙 Назад", callback_data="main_menu")]
            ])
        )

    elif data == "cancel_ticket_creation":
        user_states[user_id] = UserState.NONE
        if user_id in temp_tickets:
            del temp_tickets[user_id]

        await query.edit_message_text(
            "❌ <b>Создание тикета отменено</b>\n\n"
            "Вы можете создать тикет в любое время через команду /support",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🔙 В главное меню", callback_data="main_menu")]
            ])
        )

    elif data == "confirm_ticket_creation":
        if user_id not in temp_tickets:
            await query.edit_message_text("❌ Данные тикета не найдены.")
            return

        ticket_data = temp_tickets[user_id]

        try:
            # Создаем тикет на сайте (если доступно)
            ticket_id = None
            if WEBSITE_SYNC_AVAILABLE:
                ticket_id = await create_support_ticket(
                    user_id,
                    ticket_data['title'],
                    ticket_data['content']
                )

            if ticket_id:
                # Тикет создан на сайте
                await query.edit_message_text(
                    f"✅ <b>Тикет #{ticket_id} создан успешно!</b>\n\n"
                    f"<b>Тема:</b> {ticket_data['title']}\n\n"
                    "Ваше обращение передано в службу поддержки.\n"
                    "Ответ будет отправлен в этот чат.\n\n"
                    "🌐 Вы также можете отслеживать тикет на сайте в разделе 'Поддержка'.",
                    parse_mode="HTML",
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton("📋 Мои тикеты", callback_data="my_tickets")],
                        [InlineKeyboardButton("🔙 В главное меню", callback_data="main_menu")]
                    ])
                )

                # Уведомляем админов
                for admin_id in ADMIN_IDS:
                    try:
                        await context.bot.send_message(
                            chat_id=admin_id,
                            text=f"🎫 <b>Новый тикет поддержки!</b>\n\n"
                                 f"<b>Тикет:</b> #{ticket_id}\n"
                                 f"<b>От:</b> {query.from_user.first_name} {query.from_user.last_name or ''}\n"
                                 f"<b>Тема:</b> {ticket_data['title']}\n\n"
                                 f"Проверьте тикет на сайте или используйте /admin",
                            parse_mode="HTML"
                        )
                    except Exception as e:
                        logger.error(f"Failed to notify admin {admin_id}: {e}")
            else:
                # Создаем локальный тикет (fallback)
                await query.edit_message_text(
                    "✅ <b>Тикет создан локально!</b>\n\n"
                    f"<b>Тема:</b> {ticket_data['title']}\n\n"
                    "Ваше обращение передано в службу поддержки.\n"
                    "⚠️ Синхронизация с сайтом недоступна.",
                    parse_mode="HTML",
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton("🔙 В главное меню", callback_data="main_menu")]
                    ])
                )

            # Очищаем временные данные
            del temp_tickets[user_id]
            user_states[user_id] = UserState.NONE

        except Exception as e:
            logger.error(f"Error creating ticket: {e}")
            await query.edit_message_text(
                "❌ Ошибка при создании тикета. Попробуйте позже.",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("🔄 Попробовать снова", callback_data="create_support_ticket")],
                    [InlineKeyboardButton("🔙 В главное меню", callback_data="main_menu")]
                ])
            )

    elif data == "edit_ticket_title":
        if user_id in temp_tickets:
            user_states[user_id] = UserState.TICKET_TITLE
            await query.edit_message_text(
                f"✏️ <b>Изменение темы тикета</b>\n\n"
                f"Текущая тема: <b>{temp_tickets[user_id].get('title', 'Не указана')}</b>\n\n"
                "✍️ Введите новую тему:",
                parse_mode="HTML",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("❌ Отмена", callback_data="cancel_ticket_creation")]
                ])
            )

    elif data == "edit_ticket_content":
        if user_id in temp_tickets:
            user_states[user_id] = UserState.TICKET_CONTENT
            current_content = temp_tickets[user_id].get('content', 'Не указано')
            content_preview = current_content if len(current_content) <= 100 else current_content[:97] + "..."

            await query.edit_message_text(
                f"✏️ <b>Изменение описания тикета</b>\n\n"
                f"Текущее описание: <i>{content_preview}</i>\n\n"
                "✍️ Введите новое описание:",
                parse_mode="HTML",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("❌ Отмена", callback_data="cancel_ticket_creation")]
                ])
            )

    # Информация о сервере
    elif data == "server_info":
        keyboard = [
            [InlineKeyboardButton("📊 Статистика", callback_data="server_stats")],
            [InlineKeyboardButton("📋 Правила сервера", callback_data="server_rules")],
            [create_back_button()]
        ]
        await query.edit_message_text(
            "ℹ️ <b>Информация о сервере PlayLand</b>\n\n"
            "🎮 <b>Версия Minecraft:</b> 1.20.1\n"
            "🌍 <b>Режим игры:</b> Survival\n"
            "👥 <b>Максимум игроков:</b> 50\n"
            "🔧 <b>Модификации:</b> Vanilla+\n"
            "🌐 <b>IP сервера:</b> <code>play.playland.ru</code>\n\n"
            "🎯 <b>Особенности:</b>\n"
            "• Дружелюбное сообщество\n"
            "• Активная администрация\n"
            "• Регулярные события\n"
            "• Защита территорий\n\n"
            "📝 Для игры на сервере необходимо подать заявку",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    # Статистика сервера
    elif data == "server_stats":
        try:
            pending_count = len(get_applications_by_status('pending'))
            approved_count = len(get_applications_by_status('approved'))
            rejected_count = len(get_applications_by_status('rejected'))
            total_count = pending_count + approved_count + rejected_count

            keyboard = [[create_back_button("server_info")]]
            await query.edit_message_text(
                f"📊 <b>Статистика сервера PlayLand</b>\n\n"
                f"📝 <b>Всего заявок:</b> {total_count}\n"
                f"✅ <b>Одобрено:</b> {approved_count}\n"
                f"❌ <b>Отклонено:</b> {rejected_count}\n"
                f"⏳ <b>В обработке:</b> {pending_count}\n\n"
                f"👥 <b>Активных игроков:</b> {approved_count}\n"
                f"🎯 <b>Процент одобрения:</b> {round(approved_count/max(total_count, 1)*100, 1)}%",
                parse_mode="HTML",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
        except Exception as e:
            logger.error(f"Ошибка при получении статистики: {e}")
            keyboard = [[create_back_button("server_info")]]
            await query.edit_message_text(
                "❌ <b>Ошибка</b>\n\nНе удалось получить статистику сервера.\nПопробуйте позже.",
                parse_mode="HTML",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )

    # Правила сервера
    elif data == "server_rules":
        keyboard = [[create_back_button("server_info")]]
        await query.edit_message_text(
            "📋 <b>Правила сервера PlayLand</b>\n\n"
            "1️⃣ <b>Уважение к игрокам</b>\n"
            "• Не оскорбляйте других игроков\n"
            "• Помогайте новичкам\n\n"
            "2️⃣ <b>Честная игра</b>\n"
            "• Запрещены читы и моды\n"
            "• Не используйте баги\n\n"
            "3️⃣ <b>Строительство</b>\n"
            "• Не ломайте чужие постройки\n"
            "• Стройте красиво\n\n"
            "4️⃣ <b>Общение</b>\n"
            "• Не спамьте в чат\n"
            "• Используйте адекватные никнеймы\n\n"
            "⚠️ <i>За нарушение правил предусмотрены предупреждения и баны</i>",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    # Система помощи
    elif data.startswith("help_"):
        help_section = data.replace("help_", "")

        if help_section == "general":
            keyboard = [
                [InlineKeyboardButton("📝 Как подать заявку", callback_data="help_apply")],
                [InlineKeyboardButton("📋 Проверка статуса", callback_data="help_status")],
                [InlineKeyboardButton("❓ FAQ", callback_data="help_faq")],
                [create_back_button()]
            ]
            await query.edit_message_text(
                "❓ <b>Справка по боту PlayLand</b>\n\n"
                "🤖 Я помогу вам подать заявку на Minecraft сервер PlayLand.\n\n"
                "📋 <b>Основные функции:</b>\n"
                "• Подача заявки на сервер\n"
                "• Проверка статуса заявки\n"
                "• Информация о сервере\n"
                "• Правила и статистика\n\n"
                "💡 Выберите раздел для получения подробной помощи:",
                parse_mode="HTML",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )

        elif help_section == "nickname":
            keyboard = [[create_back_button("apply_start")]]
            await query.edit_message_text(
                "🎮 <b>Помощь: Никнейм в Minecraft</b>\n\n"
                "📋 <b>Требования к никнейму:</b>\n"
                "• От 3 до 16 символов\n"
                "• Только латинские буквы (a-z, A-Z)\n"
                "• Цифры (0-9)\n"
                "• Знак подчеркивания (_)\n\n"
                "❌ <b>Нельзя использовать:</b>\n"
                "• Пробелы\n"
                "• Русские буквы\n"
                "• Специальные символы (!@#$%^&*)\n\n"
                "✅ <b>Примеры правильных никнеймов:</b>\n"
                "• <code>Steve123</code>\n"
                "• <code>Alex_2024</code>\n"
                "• <code>CoolPlayer</code>\n\n"
                "❌ <b>Примеры неправильных:</b>\n"
                "• <code>Стив123</code> (русские буквы)\n"
                "• <code>Cool Player</code> (пробел)\n"
                "• <code>Al</code> (слишком короткий)",
                parse_mode="HTML",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )

        elif help_section == "discord":
            keyboard = [[create_back_button("apply_start")]]
            await query.edit_message_text(
                "💬 <b>Помощь: Discord тег</b>\n\n"
                "📋 <b>Как найти свой Discord тег:</b>\n"
                "1. Откройте Discord\n"
                "2. Нажмите на свой профиль (внизу слева)\n"
                "3. Скопируйте имя пользователя\n\n"
                "✅ <b>Примеры правильных тегов:</b>\n"
                "• <code>username</code> (новый формат)\n"
                "• <code>username#1234</code> (старый формат)\n"
                "• <code>cool_user</code>\n\n"
                "💡 <b>Зачем нужен Discord:</b>\n"
                "• Связь с администрацией\n"
                "• Уведомления о событиях\n"
                "• Общение с игроками\n\n"
                "❓ Если у вас нет Discord, создайте аккаунт на discord.com",
                parse_mode="HTML",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )

        elif help_section == "apply":
            keyboard = [
                [InlineKeyboardButton("📝 Начать подачу заявки", callback_data="apply_start")],
                [create_back_button("help_general")]
            ]
            await query.edit_message_text(
                "📝 <b>Как подать заявку</b>\n\n"
                "🔢 <b>Процесс состоит из 3 шагов:</b>\n\n"
                "1️⃣ <b>Никнейм в Minecraft</b>\n"
                "   Введите ваш игровой никнейм\n\n"
                "2️⃣ <b>Discord тег</b>\n"
                "   Укажите ваш Discord для связи\n\n"
                "3️⃣ <b>Причина</b>\n"
                "   Расскажите, почему хотите играть на нашем сервере\n\n"
                "✅ <b>После заполнения:</b>\n"
                "• Проверьте данные\n"
                "• Подтвердите отправку\n"
                "• Ожидайте рассмотрения\n\n"
                "⏱️ <b>Время рассмотрения:</b> обычно 1-24 часа",
                parse_mode="HTML",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )

        elif help_section == "status":
            keyboard = [
                [InlineKeyboardButton("📋 Проверить мою заявку", callback_data="my_application")],
                [create_back_button("help_general")]
            ]
            await query.edit_message_text(
                "📋 <b>Проверка статуса заявки</b>\n\n"
                "🔍 <b>Возможные статусы:</b>\n\n"
                "⏳ <b>В процессе проверки</b>\n"
                "   Ваша заявка рассматривается администрацией\n\n"
                "✅ <b>Одобрена</b>\n"
                "   Поздравляем! Вы можете играть на сервере\n\n"
                "❌ <b>Отклонена</b>\n"
                "   Заявка не прошла модерацию, можете подать новую\n\n"
                "📱 <b>Как проверить:</b>\n"
                "• Нажмите 'Моя заявка' в главном меню\n"
                "• Используйте команду /status\n\n"
                "🔔 <b>Уведомления:</b>\n"
                "Бот автоматически уведомит вас об изменении статуса",
                parse_mode="HTML",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )

        elif help_section == "faq":
            keyboard = [[create_back_button("help_general")]]
            await query.edit_message_text(
                "❓ <b>Часто задаваемые вопросы</b>\n\n"
                "🤔 <b>Сколько рассматривается заявка?</b>\n"
                "Обычно от 1 до 24 часов, в зависимости от загруженности администрации.\n\n"
                "🤔 <b>Можно ли подать заявку повторно?</b>\n"
                "Да, если предыдущая была отклонена или после одобрения прошло время.\n\n"
                "🤔 <b>Что делать, если заявку отклонили?</b>\n"
                "Подождите немного и подайте новую заявку с более подробной информацией.\n\n"
                "🤔 <b>Нужен ли лицензионный Minecraft?</b>\n"
                "Да, сервер работает только с лицензионными аккаунтами.\n\n"
                "🤔 <b>Есть ли возрастные ограничения?</b>\n"
                "Рекомендуемый возраст от 12 лет. Главное - соблюдать правила.\n\n"
                "🤔 <b>Как связаться с администрацией?</b>\n"
                "Через Discord или в игре, если ваша заявка одобрена.",
                parse_mode="HTML",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )

    # Подтверждение отправки заявки
    elif data == "confirm_application":
        await submit_application(query, context, user_id)

    # Редактирование заявки
    elif data == "edit_application":
        keyboard = [
            [InlineKeyboardButton("🎮 Изменить никнейм", callback_data="edit_nickname")],
            [InlineKeyboardButton("💬 Изменить Discord", callback_data="edit_discord")],
            [InlineKeyboardButton("📝 Изменить причину", callback_data="edit_reason")],
            [create_back_button("apply_start")]
        ]
        await query.edit_message_text(
            "✏️ <b>Редактирование заявки</b>\n\n"
            "Выберите, что хотите изменить:",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    # Редактирование отдельных полей
    elif data == "edit_nickname":
        user_states[user_id] = UserState.APPLYING_NICKNAME
        keyboard = [
            [create_help_button("nickname")],
            [create_back_button("edit_application")]
        ]
        await query.edit_message_text(
            "🎮 <b>Изменение никнейма</b>\n\n"
            f"Текущий никнейм: <code>{temp_applications.get(user_id, {}).get('nickname', 'Не указан')}</code>\n\n"
            "✍️ Введите новый никнейм:",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    elif data == "edit_discord":
        user_states[user_id] = UserState.APPLYING_DISCORD
        keyboard = [
            [create_help_button("discord")],
            [create_back_button("edit_application")]
        ]
        await query.edit_message_text(
            "💬 <b>Изменение Discord тега</b>\n\n"
            f"Текущий Discord: <code>{temp_applications.get(user_id, {}).get('discord', 'Не указан')}</code>\n\n"
            "✍️ Введите новый Discord тег:",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    elif data == "edit_reason":
        user_states[user_id] = UserState.APPLYING_REASON
        keyboard = [
            [InlineKeyboardButton("💡 Примеры причин", callback_data="help_reason_examples")],
            [create_back_button("edit_application")]
        ]
        current_reason = temp_applications.get(user_id, {}).get('reason', 'Не указана')
        reason_preview = current_reason if len(current_reason) <= 100 else current_reason[:97] + "..."
        await query.edit_message_text(
            "📝 <b>Изменение причины</b>\n\n"
            f"Текущая причина: <i>{reason_preview}</i>\n\n"
            "✍️ Введите новую причину:",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    # Примеры причин
    elif data == "help_reason_examples":
        keyboard = [[create_back_button()]]
        await query.edit_message_text(
            "💡 <b>Примеры хороших причин</b>\n\n"
            "✅ <b>Хорошие примеры:</b>\n"
            "• \"Хочу играть с друзьями и строить красивые дома\"\n"
            "• \"Ищу дружелюбное сообщество для совместной игры\"\n"
            "• \"Интересуюсь выживанием и развитием на сервере\"\n"
            "• \"Мне порекомендовали этот сервер друзья\"\n"
            "• \"Хочу участвовать в событиях и помогать новичкам\"\n\n"
            "❌ <b>Плохие примеры:</b>\n"
            "• \"Хочу играть\" (слишком коротко)\n"
            "• \"Не знаю\" (не информативно)\n"
            "• \"Потому что\" (не объясняет мотивацию)\n\n"
            "💡 <b>Совет:</b> Расскажите, что вам интересно в Minecraft и почему именно наш сервер!",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    # Админ-панель - основные обработчики
    elif data == "admin_panel":
        if user_id in ADMIN_IDS:
            await show_admin_main_menu(query, context, edit_message=True)
        else:
            keyboard = [[create_back_button()]]
            await query.edit_message_text(
                "🚫 <b>Доступ запрещен</b>\n\n"
                "У вас нет прав администратора для доступа к этой панели.",
                parse_mode="HTML",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )

    elif data == "admin_main_menu":
        if user_id in ADMIN_IDS:
            await show_admin_main_menu(query, context, edit_message=True)
        else:
            await query.edit_message_text("🚫 Доступ запрещен.")

    elif data == "admin_review_applications":
        if user_id in ADMIN_IDS:
            await show_application_review(query, context, 0)
        else:
            await query.edit_message_text("🚫 Доступ запрещен.")

    elif data == "admin_next_app":
        if user_id in ADMIN_IDS:
            current_index = admin_temp_data.get(user_id, {}).get('current_app_index', 0)
            await show_application_review(query, context, current_index + 1)
        else:
            await query.edit_message_text("🚫 Доступ запрещен.")

    elif data == "admin_prev_app":
        if user_id in ADMIN_IDS:
            current_index = admin_temp_data.get(user_id, {}).get('current_app_index', 0)
            await show_application_review(query, context, current_index - 1)
        else:
            await query.edit_message_text("🚫 Доступ запрещен.")

    # Обработка одобрения/отклонения заявок
    elif data.startswith("admin_approve_"):
        if user_id in ADMIN_IDS:
            try:
                app_id = int(data.replace("admin_approve_", ""))
                await approve_application(query, context, app_id)
            except ValueError:
                logger.error(f"Ошибка преобразования app_id в int: {data}")
                await query.edit_message_text("❌ Ошибка обработки заявки.")
        else:
            await query.edit_message_text("🚫 Доступ запрещен.")

    elif data.startswith("admin_reject_") and not data.startswith("admin_reject_no_reason_"):
        if user_id in ADMIN_IDS:
            try:
                app_id = int(data.replace("admin_reject_", ""))
                # Устанавливаем состояние ожидания причины отказа
                admin_states[user_id] = AdminState.WAITING_REJECTION_REASON
                admin_temp_data[user_id] = {'rejecting_app_id': app_id}

                keyboard = [
                    [InlineKeyboardButton("❌ Отклонить без причины", callback_data=f"admin_reject_no_reason_{app_id}")],
                    [InlineKeyboardButton("🔙 Назад к заявке", callback_data="admin_review_applications")]
                ]

                await query.edit_message_text(
                    "📝 <b>Указание причины отказа</b>\n\n"
                    "Пожалуйста, напишите причину отказа для пользователя.\n"
                    "Это поможет ему понять, что нужно исправить в следующей заявке.\n\n"
                    "💡 <b>Примеры причин:</b>\n"
                    "• Недостаточно подробная причина\n"
                    "• Не соответствует правилам сервера\n"
                    "• Подозрительная активность\n"
                    "• Неподходящий возраст\n\n"
                    "✍️ Напишите причину отказа или выберите действие:",
                    parse_mode="HTML",
                    reply_markup=InlineKeyboardMarkup(keyboard)
                )
            except ValueError:
                logger.error(f"Ошибка преобразования app_id в int: {data}")
                await query.edit_message_text("❌ Ошибка обработки заявки.")
        else:
            await query.edit_message_text("🚫 Доступ запрещен.")

    # Обработка отказа без причины
    elif data.startswith("admin_reject_no_reason_"):
        if user_id in ADMIN_IDS:
            try:
                app_id = int(data.replace("admin_reject_no_reason_", ""))
                admin_states[user_id] = AdminState.NONE
                if user_id in admin_temp_data:
                    del admin_temp_data[user_id]
                await reject_application(query, context, app_id, None)
            except ValueError:
                logger.error(f"Ошибка преобразования app_id в int: {data}")
                await query.edit_message_text("❌ Ошибка обработки заявки.")
        else:
            await query.edit_message_text("🚫 Доступ запрещен.")

    # Дополнительные админ-функции
    elif data == "admin_statistics":
        if user_id in ADMIN_IDS:
            await show_admin_statistics(query, context)
        else:
            await query.edit_message_text("🚫 Доступ запрещен.")

    elif data == "admin_view_approved":
        if user_id in ADMIN_IDS:
            await show_applications_by_status(query, context, "approved")
        else:
            await query.edit_message_text("🚫 Доступ запрещен.")

    elif data == "admin_view_rejected":
        if user_id in ADMIN_IDS:
            await show_applications_by_status(query, context, "rejected")
        else:
            await query.edit_message_text("🚫 Доступ запрещен.")

    elif data == "admin_view_history":
        if user_id in ADMIN_IDS:
            await show_admin_history(query, context)
        else:
            await query.edit_message_text("🚫 Доступ запрещен.")

    elif data == "admin_search":
        if user_id in ADMIN_IDS:
            await show_admin_search(query, context)
        else:
            await query.edit_message_text("🚫 Доступ запрещен.")

    elif data == "admin_settings":
        if user_id in ADMIN_IDS:
            await show_admin_settings(query, context)
        else:
            await query.edit_message_text("🚫 Доступ запрещен.")

    # Новые функции админ-панели
    elif data == "admin_user_management":
        if user_id in ADMIN_IDS:
            await show_admin_user_management(query, context)
        else:
            await query.edit_message_text("🚫 Доступ запрещен.")

    elif data == "admin_system_info":
        if user_id in ADMIN_IDS:
            await show_admin_system_info(query, context)
        else:
            await query.edit_message_text("🚫 Доступ запрещен.")

    elif data == "admin_security_panel":
        if user_id in ADMIN_IDS:
            await show_admin_security_panel(query, context)
        else:
            await query.edit_message_text("🚫 Доступ запрещен.")

    elif data == "admin_run_security_audit":
        if user_id in ADMIN_IDS:
            await run_security_audit_from_admin(query, context)
        else:
            await query.edit_message_text("🚫 Доступ запрещен.")

    elif data == "admin_export_stats":
        if user_id in ADMIN_IDS:
            await export_admin_statistics(query, context)
        else:
            await query.edit_message_text("🚫 Доступ запрещен.")

    elif data == "admin_cleanup":
        if user_id in ADMIN_IDS:
            await show_admin_cleanup_options(query, context)
        else:
            await query.edit_message_text("🚫 Доступ запрещен.")

async def show_admin_statistics(query, context):
    """Показывает детальную статистику для админов"""
    try:
        # Получаем статистику
        pending_count = len(get_applications_by_status('pending'))
        approved_count = len(get_applications_by_status('approved'))
        rejected_count = len(get_applications_by_status('rejected'))
        total_count = pending_count + approved_count + rejected_count

        # Процент одобрения
        approval_rate = round((approved_count / max(total_count, 1)) * 100, 1)

        # Статистика за последние 7 дней
        week_ago = (datetime.datetime.now() - datetime.timedelta(days=7)).isoformat()
        recent_query = 'SELECT COUNT(*) as count FROM applications WHERE timestamp >= ?'
        recent_result = execute_sqlite_query(recent_query, (week_ago,), fetch_one=True)
        recent_count = recent_result.get('count', 0) if recent_result else 0

        keyboard = [[InlineKeyboardButton("🔙 Назад в админ-панель", callback_data="admin_main_menu")]]

        message = (
            "📊 <b>Детальная статистика PlayLand</b>\n\n"
            f"📝 <b>Всего заявок:</b> {total_count}\n"
            f"⏳ <b>В обработке:</b> {pending_count}\n"
            f"✅ <b>Одобрено:</b> {approved_count}\n"
            f"❌ <b>Отклонено:</b> {rejected_count}\n\n"
            f"📈 <b>Процент одобрения:</b> {approval_rate}%\n"
            f"📅 <b>За последние 7 дней:</b> {recent_count} заявок\n\n"
            f"🎯 <b>Активность:</b>\n"
            f"• Средняя заявка в день: {round(recent_count/7, 1)}\n"
            f"• Активных игроков: {approved_count}"
        )

        await query.edit_message_text(message, parse_mode="HTML", reply_markup=InlineKeyboardMarkup(keyboard))

    except Exception as e:
        logger.error(f"Ошибка при показе статистики: {e}")
        keyboard = [[InlineKeyboardButton("🔙 Назад", callback_data="admin_main_menu")]]
        await query.edit_message_text(
            "❌ <b>Ошибка</b>\n\nНе удалось загрузить статистику.",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

async def show_applications_by_status(query, context, status):
    """Показывает заявки по статусу"""
    try:
        applications = get_applications_by_status(status)

        if not applications:
            status_text = {"approved": "одобренных", "rejected": "отклоненных", "pending": "в обработке"}
            keyboard = [[InlineKeyboardButton("🔙 Назад в админ-панель", callback_data="admin_main_menu")]]
            await query.edit_message_text(
                f"📭 <b>Нет {status_text.get(status, '')} заявок</b>\n\n"
                f"В данный момент нет заявок со статусом '{status}'.",
                parse_mode="HTML",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
            return

        # Показываем первые 10 заявок
        message_lines = []
        status_emoji = {"approved": "✅", "rejected": "❌", "pending": "⏳"}
        status_text = {"approved": "Одобренные заявки", "rejected": "Отклоненные заявки", "pending": "Заявки в обработке"}

        message_lines.append(f"{status_emoji.get(status, '📋')} <b>{status_text.get(status, 'Заявки')}</b>\n")

        for i, app in enumerate(applications[:10]):
            # Получаем информацию о пользователе по telegram_user_id
            user_query = 'SELECT * FROM users WHERE telegram_id = ?'
            user_info = execute_sqlite_query(user_query, (app.get('telegram_user_id'),), fetch_one=True)

            # Форматируем дату
            timestamp = app.get('timestamp', '')
            try:
                timestamp_dt = datetime.datetime.fromisoformat(timestamp)
                date_formatted = timestamp_dt.strftime('%d.%m.%Y')
            except:
                date_formatted = "Неизвестно"

            message_lines.append(
                f"{i+1}. <b>#{app.get('id')}</b> - <code>{app.get('minecraft_nickname', 'Неизвестно')}</code>\n"
                f"   👤 {user_info.get('first_name', '') if user_info else ''} {user_info.get('last_name', '') if user_info else ''}\n"
                f"   📅 {date_formatted}\n"
            )

        if len(applications) > 10:
            message_lines.append(f"\n... и еще {len(applications) - 10} заявок")

        keyboard = [[InlineKeyboardButton("🔙 Назад в админ-панель", callback_data="admin_main_menu")]]

        await query.edit_message_text(
            "\n".join(message_lines),
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    except Exception as e:
        logger.error(f"Ошибка при показе заявок по статусу {status}: {e}")
        keyboard = [[InlineKeyboardButton("🔙 Назад", callback_data="admin_main_menu")]]
        await query.edit_message_text(
            "❌ <b>Ошибка</b>\n\nНе удалось загрузить заявки.",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

async def show_admin_history(query, context):
    """Показывает историю действий администраторов"""
    try:
        # Получаем последние обработанные заявки (исправленный запрос для объединенной БД)
        history_query = '''
        SELECT a.*, u.first_name, u.last_name, u.username
        FROM applications a
        LEFT JOIN users u ON a.telegram_user_id = u.telegram_id
        WHERE a.status IN ('approved', 'rejected') AND a.processed_at IS NOT NULL
        ORDER BY a.processed_at DESC
        LIMIT 15
        '''
        history = execute_sqlite_query(history_query, fetch_all=True)

        if not history:
            keyboard = [[InlineKeyboardButton("🔙 Назад в админ-панель", callback_data="admin_main_menu")]]
            await query.edit_message_text(
                "📭 <b>История пуста</b>\n\n"
                "Пока нет обработанных заявок.",
                parse_mode="HTML",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
            return

        message_lines = ["📜 <b>История действий администраторов</b>\n"]

        for item in history:
            # Форматируем дату обработки
            processed_at = item.get('processed_at', '')
            try:
                processed_dt = datetime.datetime.fromisoformat(processed_at)
                processed_formatted = processed_dt.strftime('%d.%m.%Y %H:%M')
            except:
                processed_formatted = "Неизвестно"

            status_emoji = "✅" if item.get('status') == 'approved' else "❌"
            status_text = "одобрена" if item.get('status') == 'approved' else "отклонена"

            message_lines.append(
                f"{status_emoji} <b>#{item.get('id')}</b> - <code>{item.get('minecraft_nickname', 'Неизвестно')}</code>\n"
                f"   👤 {item.get('first_name', '')} {item.get('last_name', '')}\n"
                f"   📅 {processed_formatted} - {status_text}\n"
            )

        keyboard = [[InlineKeyboardButton("🔙 Назад в админ-панель", callback_data="admin_main_menu")]]

        await query.edit_message_text(
            "\n".join(message_lines),
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    except Exception as e:
        logger.error(f"Ошибка при показе истории: {e}")
        keyboard = [[InlineKeyboardButton("🔙 Назад", callback_data="admin_main_menu")]]
        await query.edit_message_text(
            "❌ <b>Ошибка</b>\n\nНе удалось загрузить историю.",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

async def show_admin_search(query, context):
    """Показывает интерфейс поиска заявок"""
    keyboard = [
        [InlineKeyboardButton("🎮 Поиск по никнейму", callback_data="admin_search_nickname")],
        [InlineKeyboardButton("👤 Поиск по ID пользователя", callback_data="admin_search_user_id")],
        [InlineKeyboardButton("💬 Поиск по Discord", callback_data="admin_search_discord")],
        [InlineKeyboardButton("🔙 Назад в админ-панель", callback_data="admin_main_menu")]
    ]

    await query.edit_message_text(
        "🔍 <b>Поиск заявок</b>\n\n"
        "Выберите тип поиска:",
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

async def show_admin_settings(query, context):
    """Показывает настройки админ-панели"""
    keyboard = [
        [
            InlineKeyboardButton("👥 Управление пользователями", callback_data="admin_user_management"),
            InlineKeyboardButton("📊 Экспорт статистики", callback_data="admin_export_stats")
        ],
        [
            InlineKeyboardButton("🧹 Очистка старых заявок", callback_data="admin_cleanup"),
            InlineKeyboardButton("📝 Массовые действия", callback_data="admin_bulk_actions")
        ],
        [
            InlineKeyboardButton("🔧 Системная информация", callback_data="admin_system_info"),
            InlineKeyboardButton("🛡️ Безопасность", callback_data="admin_security_panel")
        ],
        [InlineKeyboardButton("🔙 Назад в админ-панель", callback_data="admin_main_menu")]
    ]

    await query.edit_message_text(
        "⚙️ <b>Настройки админ-панели</b>\n\n"
        "Выберите действие:",
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

async def show_admin_user_management(query, context):
    """Показывает панель управления пользователями"""
    try:
        # Получаем статистику пользователей
        total_users_query = "SELECT COUNT(*) FROM users"
        total_users = execute_sqlite_query(total_users_query, fetch_one=True)
        total_users_count = total_users[0] if total_users else 0

        # Активные пользователи (с заявками)
        active_users_query = "SELECT COUNT(DISTINCT telegram_user_id) FROM applications"
        active_users = execute_sqlite_query(active_users_query, fetch_one=True)
        active_users_count = active_users[0] if active_users else 0

        # Последние пользователи
        recent_users_query = """
        SELECT telegram_id, username, created_at
        FROM users
        ORDER BY created_at DESC
        LIMIT 5
        """
        recent_users = execute_sqlite_query(recent_users_query, fetch_all=True)

        keyboard = [
            [
                InlineKeyboardButton("🔍 Поиск пользователя", callback_data="admin_search_user"),
                InlineKeyboardButton("📋 Список пользователей", callback_data="admin_list_users")
            ],
            [
                InlineKeyboardButton("🚫 Заблокированные", callback_data="admin_blocked_users"),
                InlineKeyboardButton("👑 Администраторы", callback_data="admin_list_admins")
            ],
            [InlineKeyboardButton("🔙 Назад к настройкам", callback_data="admin_settings")]
        ]

        recent_users_text = ""
        if recent_users:
            recent_users_text = "\n\n📋 <b>Последние пользователи:</b>\n"
            for user in recent_users:
                username = user[1] or "Без имени"
                recent_users_text += f"• @{username} (ID: {user[0]})\n"

        await query.edit_message_text(
            f"👥 <b>Управление пользователями</b>\n\n"
            f"📊 <b>Статистика:</b>\n"
            f"• Всего пользователей: <code>{total_users_count}</code>\n"
            f"• Активных пользователей: <code>{active_users_count}</code>\n"
            f"• Коэффициент активности: <code>{round((active_users_count/max(total_users_count,1))*100, 1)}%</code>"
            f"{recent_users_text}",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    except Exception as e:
        safe_log_error(e, "admin_user_management")
        await query.edit_message_text(
            "❌ <b>Ошибка</b>\n\nНе удалось загрузить информацию о пользователях.",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Назад", callback_data="admin_settings")]])
        )

async def show_admin_system_info(query, context):
    """Показывает системную информацию"""
    try:
        import psutil
        import platform
        from datetime import datetime

        # Системная информация
        system_info = {
            'platform': platform.system(),
            'python_version': platform.python_version(),
            'cpu_percent': psutil.cpu_percent(interval=1),
            'memory_percent': psutil.virtual_memory().percent,
            'disk_percent': psutil.disk_usage('/').percent if platform.system() != 'Windows' else psutil.disk_usage('C:').percent
        }

        # Информация о базе данных
        db_size_query = "SELECT COUNT(*) FROM applications"
        db_apps = execute_sqlite_query(db_size_query, fetch_one=True)
        db_apps_count = db_apps[0] if db_apps else 0

        users_count_query = "SELECT COUNT(*) FROM users"
        db_users = execute_sqlite_query(users_count_query, fetch_one=True)
        db_users_count = db_users[0] if db_users else 0

        keyboard = [
            [
                InlineKeyboardButton("🔄 Обновить", callback_data="admin_system_info"),
                InlineKeyboardButton("📊 Детальная статистика", callback_data="admin_detailed_stats")
            ],
            [InlineKeyboardButton("🔙 Назад к настройкам", callback_data="admin_settings")]
        ]

        await query.edit_message_text(
            f"🔧 <b>Системная информация</b>\n\n"
            f"💻 <b>Система:</b>\n"
            f"• ОС: <code>{system_info['platform']}</code>\n"
            f"• Python: <code>{system_info['python_version']}</code>\n"
            f"• CPU: <code>{system_info['cpu_percent']}%</code>\n"
            f"• RAM: <code>{system_info['memory_percent']}%</code>\n"
            f"• Диск: <code>{system_info['disk_percent']}%</code>\n\n"
            f"🗄️ <b>База данных:</b>\n"
            f"• Заявок: <code>{db_apps_count}</code>\n"
            f"• Пользователей: <code>{db_users_count}</code>\n\n"
            f"🕐 <b>Обновлено:</b> {datetime.now().strftime('%H:%M:%S')}",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    except ImportError:
        await query.edit_message_text(
            "⚠️ <b>Системная информация недоступна</b>\n\n"
            "Модуль psutil не установлен.",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Назад", callback_data="admin_settings")]])
        )
    except Exception as e:
        safe_log_error(e, "admin_system_info")
        await query.edit_message_text(
            "❌ <b>Ошибка</b>\n\nНе удалось получить системную информацию.",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Назад", callback_data="admin_settings")]])
        )

async def show_admin_security_panel(query, context):
    """Показывает панель безопасности"""
    try:
        # Получаем статистику безопасности
        security_events_query = """
        SELECT event_type, COUNT(*) as count
        FROM security_events
        WHERE timestamp > datetime('now', '-24 hours')
        GROUP BY event_type
        ORDER BY count DESC
        """

        try:
            security_events = execute_sqlite_query(security_events_query, fetch_all=True)
        except:
            security_events = []

        keyboard = [
            [
                InlineKeyboardButton("🔍 Аудит безопасности", callback_data="admin_run_security_audit"),
                InlineKeyboardButton("📊 События безопасности", callback_data="admin_security_events")
            ],
            [
                InlineKeyboardButton("🛡️ Настройки безопасности", callback_data="admin_security_settings"),
                InlineKeyboardButton("📝 Логи безопасности", callback_data="admin_security_logs")
            ],
            [InlineKeyboardButton("🔙 Назад к настройкам", callback_data="admin_settings")]
        ]

        events_text = ""
        if security_events:
            events_text = "\n\n📊 <b>События за 24 часа:</b>\n"
            for event in security_events[:5]:  # Показываем топ-5
                events_text += f"• {event[0]}: <code>{event[1]}</code>\n"
        else:
            events_text = "\n\n✅ <b>Нет событий безопасности за 24 часа</b>"

        await query.edit_message_text(
            f"🛡️ <b>Панель безопасности</b>\n\n"
            f"Управление системой безопасности бота."
            f"{events_text}",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    except Exception as e:
        safe_log_error(e, "admin_security_panel")
        await query.edit_message_text(
            "❌ <b>Ошибка</b>\n\nНе удалось загрузить панель безопасности.",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Назад", callback_data="admin_settings")]])
        )

async def run_security_audit_from_admin(query, context):
    """Запускает аудит безопасности из админ-панели"""
    try:
        await query.edit_message_text(
            "🔍 <b>Запуск аудита безопасности...</b>\n\n"
            "⏳ Пожалуйста, подождите...",
            parse_mode="HTML"
        )

        # Запускаем аудит безопасности
        import subprocess
        import sys

        result = subprocess.run([
            sys.executable,
            "playland/tests/security_audit.py"
        ], capture_output=True, text=True, cwd=".")

        if result.returncode == 0:
            # Парсим результат
            output_lines = result.stdout.split('\n')
            score_line = [line for line in output_lines if 'Общий балл:' in line]
            score = "Неизвестно"
            if score_line:
                score = score_line[0].split('Общий балл:')[1].strip()

            keyboard = [
                [
                    InlineKeyboardButton("🔄 Повторить аудит", callback_data="admin_run_security_audit"),
                    InlineKeyboardButton("📊 Детали", callback_data="admin_security_events")
                ],
                [InlineKeyboardButton("🔙 Назад к безопасности", callback_data="admin_security_panel")]
            ]

            await query.edit_message_text(
                f"✅ <b>Аудит безопасности завершен</b>\n\n"
                f"📊 <b>Общий балл:</b> <code>{score}</code>\n\n"
                f"🕐 <b>Время выполнения:</b> {datetime.datetime.now().strftime('%H:%M:%S')}",
                parse_mode="HTML",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
        else:
            await query.edit_message_text(
                "❌ <b>Ошибка при выполнении аудита</b>\n\n"
                f"Код ошибки: {result.returncode}",
                parse_mode="HTML",
                reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Назад", callback_data="admin_security_panel")]])
            )

    except Exception as e:
        safe_log_error(e, "admin_security_audit")
        await query.edit_message_text(
            "❌ <b>Ошибка</b>\n\nНе удалось запустить аудит безопасности.",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Назад", callback_data="admin_security_panel")]])
        )

async def export_admin_statistics(query, context):
    """Экспортирует статистику для администраторов"""
    try:
        # Получаем полную статистику
        stats_data = {
            'timestamp': datetime.datetime.now().isoformat(),
            'applications': {
                'total': len(get_applications_by_status('pending')) + len(get_applications_by_status('approved')) + len(get_applications_by_status('rejected')),
                'pending': len(get_applications_by_status('pending')),
                'approved': len(get_applications_by_status('approved')),
                'rejected': len(get_applications_by_status('rejected'))
            },
            'users': {
                'total': execute_sqlite_query("SELECT COUNT(*) FROM users", fetch_one=True)[0] if execute_sqlite_query("SELECT COUNT(*) FROM users", fetch_one=True) else 0
            }
        }

        # Формируем текстовый отчет
        report_text = f"""📊 ЭКСПОРТ СТАТИСТИКИ PLAYLAND
🕐 Дата: {datetime.datetime.now().strftime('%d.%m.%Y %H:%M:%S')}

📋 ЗАЯВКИ:
• Всего: {stats_data['applications']['total']}
• В ожидании: {stats_data['applications']['pending']}
• Одобрено: {stats_data['applications']['approved']}
• Отклонено: {stats_data['applications']['rejected']}

👥 ПОЛЬЗОВАТЕЛИ:
• Всего: {stats_data['users']['total']}

📈 КОЭФФИЦИЕНТЫ:
• Одобрения: {round((stats_data['applications']['approved'] / max(stats_data['applications']['total'], 1)) * 100, 1)}%
• Отклонения: {round((stats_data['applications']['rejected'] / max(stats_data['applications']['total'], 1)) * 100, 1)}%
"""

        keyboard = [
            [InlineKeyboardButton("🔄 Обновить данные", callback_data="admin_export_stats")],
            [InlineKeyboardButton("🔙 Назад к настройкам", callback_data="admin_settings")]
        ]

        await query.edit_message_text(
            f"📊 <b>Экспорт статистики</b>\n\n"
            f"<pre>{report_text}</pre>",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    except Exception as e:
        safe_log_error(e, "admin_export_stats")
        await query.edit_message_text(
            "❌ <b>Ошибка</b>\n\nНе удалось экспортировать статистику.",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Назад", callback_data="admin_settings")]])
        )

async def show_admin_cleanup_options(query, context):
    """Показывает опции очистки для администраторов"""
    keyboard = [
        [
            InlineKeyboardButton("🗑️ Старые заявки (>30 дней)", callback_data="admin_cleanup_old_apps"),
            InlineKeyboardButton("📝 Логи безопасности", callback_data="admin_cleanup_security_logs")
        ],
        [
            InlineKeyboardButton("👥 Неактивные пользователи", callback_data="admin_cleanup_inactive_users"),
            InlineKeyboardButton("🧹 Временные данные", callback_data="admin_cleanup_temp_data")
        ],
        [InlineKeyboardButton("🔙 Назад к настройкам", callback_data="admin_settings")]
    ]

    await query.edit_message_text(
        "🧹 <b>Очистка системы</b>\n\n"
        "⚠️ <b>Внимание!</b> Операции очистки необратимы.\n"
        "Выберите тип данных для очистки:",
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def main():
    """Инициализация и запуск бота"""
    # Инициализация базы данных
    if not init_database():
        logger.error("Не удалось инициализировать базу данных. Бот не может быть запущен.")
        return

    # Создание экземпляра бота
    application = Application.builder().token(BOT_TOKEN).build()

    # Инициализация менеджера безопасности
    if SECURITY_AVAILABLE:
        security_manager = SecurityManager(BOT_TOKEN)
        application.bot_data['security_manager'] = security_manager
        application.bot_data['admin_ids'] = ADMIN_IDS
        logger.info("✅ Модуль безопасности инициализирован")
    else:
        logger.warning("⚠️ Модуль безопасности недоступен - работа в режиме совместимости")

    # Регистрация обработчиков команд
    application.add_handler(CommandHandler("start", start))
    application.add_handler(CommandHandler("help", help_command))
    application.add_handler(CommandHandler("apply", apply_command))
    application.add_handler(CommandHandler("status", my_application))
    application.add_handler(CommandHandler("info", info_command))
    application.add_handler(CommandHandler("stats", stats_command))
    application.add_handler(CommandHandler("support", support_command))
    application.add_handler(CommandHandler("admin", admin_panel))

    # Регистрация обработчика callback кнопок
    application.add_handler(CallbackQueryHandler(button_callback))

    # Регистрация обработчика для данных веб-приложения
    application.add_handler(MessageHandler(filters.StatusUpdate.WEB_APP_DATA, handle_web_app_data))

    # Добавляем диагностический обработчик для всех сообщений
    async def debug_all_messages(update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Диагностический обработчик для всех типов сообщений"""
        user_id = update.effective_user.id if update.effective_user else "Unknown"

        logger.info(f"🔍 ДИАГНОСТИКА: Получено сообщение от пользователя {user_id}")
        logger.info(f"🔍 Тип update: {type(update)}")

        if update.message:
            logger.info(f"🔍 Есть message: True")
            logger.info(f"🔍 Тип message: {type(update.message)}")

            if hasattr(update.message, 'web_app_data') and update.message.web_app_data:
                logger.info(f"🔍 НАЙДЕН WEB_APP_DATA: {update.message.web_app_data}")
                logger.info(f"🔍 WEB_APP_DATA.data: {update.message.web_app_data.data}")
                # Вызываем обработчик WebApp данных напрямую
                await handle_web_app_data(update, context)
                return

            if update.message.text:
                logger.info(f"🔍 Текст сообщения: {update.message.text}")
        else:
            logger.info(f"🔍 Нет message в update")

    # Добавляем диагностический обработчик с низким приоритетом
    application.add_handler(MessageHandler(filters.ALL, debug_all_messages), group=1)

    # Регистрация обработчика для текстовых сообщений
    application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_message))

    # Запуск бота
    logger.info("Бот запущен и готов к работе")
    try:
        application.run_polling()
    except Exception as e:
        if "Conflict" in str(e) and "getUpdates" in str(e):
            logger.warning("⚠️ Обнаружен конфликт с другим экземпляром бота. Остановка...")
            return
        else:
            logger.error(f"❌ Ошибка при запуске бота: {e}")
            raise

if __name__ == "__main__":
    main()
