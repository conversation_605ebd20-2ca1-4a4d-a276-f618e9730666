#!/usr/bin/env python3
"""
API для синхронизации между сайтом и Telegram ботом.
Обеспечивает единую систему тикетов и заявок.
"""

from flask import Blueprint, request, jsonify
from datetime import datetime, timezone
import hashlib
import hmac
import os
from .models import db, User, Application, Ticket, TicketMessage, News
import logging

# Создаем Blueprint для API
sync_api = Blueprint('sync_api', __name__)

# Секретный ключ для аутентификации между сайтом и ботом
SYNC_SECRET_KEY = os.environ.get('SYNC_SECRET_KEY', 'your-secret-key-here')

def verify_signature(data, signature):
    """Проверяет подпись запроса от Telegram бота"""
    expected_signature = hmac.new(
        SYNC_SECRET_KEY.encode(),
        data.encode(),
        hashlib.sha256
    ).hexdigest()
    return hmac.compare_digest(signature, expected_signature)

def require_auth(f):
    """Декоратор для проверки аутентификации"""
    def decorated_function(*args, **kwargs):
        signature = request.headers.get('X-Sync-Signature')
        if not signature:
            return jsonify({'error': 'Missing signature'}), 401
        
        data = request.get_data(as_text=True)
        if not verify_signature(data, signature):
            return jsonify({'error': 'Invalid signature'}), 401
        
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

@sync_api.route('/health', methods=['GET'])
def health_check():
    """Проверка работоспособности API"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now(timezone.utc).isoformat(),
        'service': 'sync_api'
    })

@sync_api.route('/user/link', methods=['POST'])
@require_auth
def link_telegram_user():
    """Связывает пользователя сайта с Telegram аккаунтом"""
    try:
        data = request.get_json()
        telegram_id = data.get('telegram_id')
        username = data.get('username')
        first_name = data.get('first_name')
        last_name = data.get('last_name')
        
        if not telegram_id:
            return jsonify({'error': 'telegram_id is required'}), 400
        
        # Ищем пользователя по Telegram ID
        user = User.query.filter_by(telegram_id=telegram_id).first()
        
        if not user:
            # Создаем нового пользователя
            user = User(
                telegram_id=telegram_id,
                username=username or f"tg_user_{telegram_id}",
                telegram=username,
                first_name=first_name,
                last_name=last_name,
                email=f"tg_{telegram_id}@telegram.local",  # Временный email
                is_activated=True
            )
            db.session.add(user)
        else:
            # Обновляем данные существующего пользователя
            if username:
                user.telegram = username
            if first_name:
                user.first_name = first_name
            if last_name:
                user.last_name = last_name
        
        user.last_seen = datetime.now(timezone.utc)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'user_id': user.id,
            'message': 'User linked successfully'
        })
        
    except Exception as e:
        db.session.rollback()
        logging.error(f"Error linking Telegram user: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@sync_api.route('/application/create', methods=['POST'])
@require_auth
def create_application():
    """Создает заявку от Telegram бота"""
    try:
        data = request.get_json()
        telegram_id = data.get('telegram_id')
        
        # Находим пользователя
        user = User.query.filter_by(telegram_id=telegram_id).first()
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Создаем заявку
        application = Application(
            user_id=user.id,
            minecraft_username=data.get('minecraft_username'),
            discord_tag=data.get('discord_tag'),
            email=data.get('email', user.email),
            age=data.get('age'),
            reason=data.get('reason'),
            status='pending'
        )
        
        db.session.add(application)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'application_id': application.id,
            'message': 'Application created successfully'
        })
        
    except Exception as e:
        db.session.rollback()
        logging.error(f"Error creating application: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@sync_api.route('/ticket/create', methods=['POST'])
@require_auth
def create_ticket():
    """Создает тикет от Telegram бота"""
    try:
        data = request.get_json()
        telegram_id = data.get('telegram_id')
        
        # Находим пользователя
        user = User.query.filter_by(telegram_id=telegram_id).first()
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Создаем тикет
        ticket = Ticket(
            title=data.get('title'),
            content=data.get('content'),
            user_id=user.id,
            application_type=data.get('application_type', 'support')
        )
        
        db.session.add(ticket)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'ticket_id': ticket.id,
            'message': 'Ticket created successfully'
        })
        
    except Exception as e:
        db.session.rollback()
        logging.error(f"Error creating ticket: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@sync_api.route('/ticket/<int:ticket_id>/message', methods=['POST'])
@require_auth
def add_ticket_message():
    """Добавляет сообщение в тикет"""
    try:
        ticket_id = request.view_args['ticket_id']
        data = request.get_json()
        
        telegram_id = data.get('telegram_id')
        user = User.query.filter_by(telegram_id=telegram_id).first()
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        ticket = Ticket.query.get(ticket_id)
        if not ticket:
            return jsonify({'error': 'Ticket not found'}), 404
        
        # Создаем сообщение
        message = TicketMessage(
            ticket_id=ticket_id,
            user_id=user.id,
            content=data.get('content'),
            is_admin_reply=data.get('is_admin_reply', False),
            source_platform='telegram',
            telegram_message_id=data.get('telegram_message_id')
        )
        
        db.session.add(message)
        
        # Обновляем статус тикета
        if data.get('is_admin_reply'):
            ticket.status = 'answered'
        else:
            ticket.status = 'open'
        
        ticket.updated_at = datetime.now(timezone.utc)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message_id': message.id,
            'message': 'Message added successfully'
        })
        
    except Exception as e:
        db.session.rollback()
        logging.error(f"Error adding ticket message: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@sync_api.route('/ticket/<int:ticket_id>/messages', methods=['GET'])
@require_auth
def get_ticket_messages():
    """Получает сообщения тикета для синхронизации"""
    try:
        ticket_id = request.view_args['ticket_id']
        
        ticket = Ticket.query.get(ticket_id)
        if not ticket:
            return jsonify({'error': 'Ticket not found'}), 404
        
        messages = TicketMessage.query.filter_by(ticket_id=ticket_id).order_by(TicketMessage.created_at).all()
        
        messages_data = []
        for msg in messages:
            messages_data.append({
                'id': msg.id,
                'content': msg.content,
                'user_id': msg.user_id,
                'user_name': msg.user.get_display_name(),
                'is_admin_reply': msg.is_admin_reply,
                'source_platform': msg.source_platform,
                'telegram_message_id': msg.telegram_message_id,
                'created_at': msg.created_at.isoformat(),
                'is_synced': msg.is_synced
            })
        
        return jsonify({
            'success': True,
            'ticket': {
                'id': ticket.id,
                'title': ticket.title,
                'status': ticket.status,
                'created_at': ticket.created_at.isoformat(),
                'updated_at': ticket.updated_at.isoformat()
            },
            'messages': messages_data
        })
        
    except Exception as e:
        logging.error(f"Error getting ticket messages: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@sync_api.route('/news/latest', methods=['GET'])
@require_auth
def get_latest_news():
    """Получает последние новости для отправки в Telegram"""
    try:
        limit = request.args.get('limit', 5, type=int)
        
        news = News.query.filter_by(is_published=True).order_by(News.created_at.desc()).limit(limit).all()
        
        news_data = []
        for article in news:
            news_data.append({
                'id': article.id,
                'title': article.title,
                'excerpt': article.excerpt,
                'content': article.content,
                'author': article.author.username if article.author else 'Unknown',
                'created_at': article.created_at.isoformat()
            })
        
        return jsonify({
            'success': True,
            'news': news_data
        })
        
    except Exception as e:
        logging.error(f"Error getting latest news: {e}")
        return jsonify({'error': 'Internal server error'}), 500

# Функция для регистрации Blueprint
def register_sync_api(app):
    """Регистрирует API синхронизации в Flask приложении"""
    app.register_blueprint(sync_api)
    
    # Настройка логирования
    logging.basicConfig(level=logging.INFO)
    
    return sync_api
