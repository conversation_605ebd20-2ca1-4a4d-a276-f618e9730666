# PlayLand Core - Git Ignore Rules

# ===== JAVA =====
*.class
*.jar
!paper-*.jar
!PlayLandCore*.jar
*.war
*.ear
*.nar
hs_err_pid*
replay_pid*

# ===== GRADLE =====
.gradle/
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

# ===== MINECRAFT SERVER =====
# Логи сервера
minecraft_server/*/logs/
logs/
*.log
*.log.gz

# Временные файлы сервера
minecraft_server/*/cache/
minecraft_server/*/crash-reports/
minecraft_server/*/debug/
minecraft_server/*/world/
minecraft_server/*/world_nether/
minecraft_server/*/world_the_end/
minecraft_server/*/plugins/.paper-remapped/
minecraft_server/*/libraries/
minecraft_server/*/versions/

# Конфигурационные файлы сервера (сохраняем только шаблоны)
minecraft_server/*/server.properties
minecraft_server/*/bukkit.yml
minecraft_server/*/spigot.yml
minecraft_server/*/config/paper-*.yml
minecraft_server/*/banned-*.json
minecraft_server/*/ops.json
minecraft_server/*/whitelist.json
minecraft_server/*/usercache.json
minecraft_server/*/version_history.json
minecraft_server/*/permissions.yml
minecraft_server/*/commands.yml
minecraft_server/*/help.yml
minecraft_server/*/.console_history

# ===== PLAYLAND CORE =====
# Временные файлы сборки
playland_core/build/
playland_core/.gradle/
playland_core/out/
playland_core/target/

# Скомпилированные JAR файлы (кроме финальных)
playland_core/*.jar
!playland_core/PlayLandCore-*.jar

# ===== БАЗА ДАННЫХ =====
# Локальные базы данных
*.db
*.sqlite
*.sqlite3
website/instance/

# Дампы базы данных
*.sql.backup
*.dump

# ===== PYTHON =====
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
.venv/

# ===== КОНФИГУРАЦИЯ =====
# Секретные файлы
.env
.env.local
.env.production
config/secrets.yml
config/database.yml
config/production.yml

# Локальные конфигурации
config/local.yml
config/development.yml

# ===== IDE =====
# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr
out/

# Eclipse
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

# VS Code
.vscode/
*.code-workspace

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# ===== ОПЕРАЦИОННАЯ СИСТЕМА =====
# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===== МОНИТОРИНГ И ЛОГИ =====
# Логи приложений
*.log
*.log.*
logs/
log/

# Метрики и статистика
metrics/
stats/
monitoring/data/

# ===== ВРЕМЕННЫЕ ФАЙЛЫ =====
# Временные файлы разработки
tmp/
temp/
.tmp/
.temp/

# Бэкапы
*.backup
*.bak
*.old
*.orig

# ===== DOCKER =====
# Docker файлы (сохраняем только основные)
.dockerignore
docker-compose.override.yml

# ===== ТЕСТИРОВАНИЕ =====
# Результаты тестов
test-results/
test-output/
coverage/
.nyc_output/

# ===== ДОКУМЕНТАЦИЯ =====
# Сгенерированная документация
docs/build/
docs/_build/
site/

# ===== СПЕЦИФИЧНЫЕ ДЛЯ PLAYLAND =====
# Патчи Patina (клонированный репозиторий)
minecraft_server/patina_analysis/.git/
minecraft_server/patina_analysis/build/
minecraft_server/patina_analysis/patches/server/.git/

# Кэш загрузок
cache/
downloads/

# Локальные настройки разработчика
.local/
local/
developer.yml

# ===== БЕЗОПАСНОСТЬ =====
# Ключи и сертификаты
*.key
*.pem
*.crt
*.csr
*.p12
*.pfx
keystore
truststore

# Токены и пароли
tokens.txt
passwords.txt
secrets.txt
