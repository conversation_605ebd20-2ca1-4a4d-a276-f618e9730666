{% extends "base.html" %}

{% block title %}Подключение Telegram - PlayLand{% endblock %}

{% block head_extra %}
<style>
    :root {
        --telegram-color: #0088CC;
        --telegram-hover: #0077B5;
    }
    
    .connect-page {
        max-width: 700px;
        margin: 30px auto;
        padding: 0;
        animation: fadeIn 0.8s ease-out forwards;
        position: relative;
    }
    
    .connect-header {
        background-color: rgba(0, 0, 0, 0.7);
        padding: 25px;
        position: relative;
        border: 4px solid var(--minecraft-stone);
        margin-bottom: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        overflow: hidden;
        text-align: center;
    }
    
    .connect-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: linear-gradient(90deg, transparent, var(--telegram-color), transparent);
        animation: headerGlow 3s infinite;
    }
    
    @keyframes headerGlow {
        0%, 100% { opacity: 0.5; }
        50% { opacity: 1; }
    }
    
    .connect-title {
        font-family: var(--pixel-font);
        font-size: 2em;
        color: var(--telegram-color);
        text-shadow: 2px 2px 0 #000, 0 0 10px var(--telegram-color);
        margin-bottom: 15px;
        text-transform: uppercase;
        letter-spacing: 2px;
    }
    
    .connect-content {
        background-color: rgba(0, 0, 0, 0.6);
        border: 4px solid var(--minecraft-dirt);
        padding: 25px;
        position: relative;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        overflow: hidden;
    }
    
    .connect-content::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: repeating-linear-gradient(
            45deg,
            rgba(255,255,255,0.03),
            rgba(255,255,255,0.03) 10px,
            rgba(0,0,0,0.05) 10px,
            rgba(0,0,0,0.05) 20px
        );
        pointer-events: none;
    }
    
    .connect-info {
        margin-bottom: 30px;
        position: relative;
        z-index: 1;
    }
    
    .connect-info p {
        margin-bottom: 15px;
        line-height: 1.6;
    }
    
    .telegram-benefits {
        list-style: none;
        padding: 0;
        margin-bottom: 30px;
    }
    
    .telegram-benefits li {
        padding: 10px 0 10px 30px;
        position: relative;
        border-bottom: 1px dashed rgba(0, 136, 204, 0.2);
    }
    
    .telegram-benefits li:before {
        content: '\f00c';
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        color: var(--telegram-color);
        position: absolute;
        left: 0;
        top: 10px;
    }
    
    .telegram-btn {
        display: inline-block;
        padding: 15px 25px;
        background-color: var(--telegram-color);
        color: white;
        border: 3px solid rgba(0, 0, 0, 0.8);
        text-decoration: none;
        font-family: var(--pixel-font);
        font-size: 0.9rem;
        position: relative;
        text-align: center;
        text-shadow: 2px 2px 0px rgba(0, 0, 0, 0.8);
        box-shadow: inset -3px -3px 0px 0px rgba(0, 0, 0, 0.3), 
                    inset 3px 3px 0px 0px rgba(255, 255, 255, 0.3);
        cursor: pointer;
        transition: all 0.1s;
        transform-style: preserve-3d;
        letter-spacing: 1px;
        overflow: hidden;
    }
    
    .telegram-btn::before {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        top: 6px;
        left: 0;
        background-color: rgba(0, 0, 0, 0.3);
        z-index: -1;
        transform: translateZ(-1px);
        transition: all 0.2s;
    }
    
    .telegram-btn:hover {
        transform: translate(0, -3px);
        background-color: var(--telegram-hover);
    }
    
    .telegram-btn:hover::before {
        top: 9px;
    }
    
    .telegram-btn:active {
        transform: translate(0, 3px);
    }
    
    .telegram-btn:active::before {
        top: 3px;
    }
    
    .telegram-btn i {
        margin-right: 10px;
    }
    
    .telegram-status {
        margin-top: 20px;
        padding: 15px;
        border-radius: 3px;
        background-color: rgba(0, 0, 0, 0.3);
        border-left: 3px solid var(--telegram-color);
    }
    
    .telegram-status.connected {
        border-left-color: #43B581;
    }
    
    .telegram-status.error {
        border-left-color: #F04747;
    }
    
    .telegram-username {
        display: flex;
        align-items: center;
        margin-top: 10px;
    }
    
    .telegram-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 10px;
        border: 2px solid var(--telegram-color);
    }
    
    .back-link {
        display: block;
        margin-top: 20px;
        color: white;
        text-decoration: none;
        font-family: var(--pixel-font);
        font-size: 0.8em;
        transition: all 0.3s;
        position: relative;
        z-index: 1;
        text-align: center;
    }
    
    .back-link:hover {
        color: var(--telegram-color);
        text-shadow: 0 0 10px var(--telegram-color);
    }
    
    .telegram-widget-container {
        margin: 20px 0;
        text-align: center;
        background: rgba(0, 0, 0, 0.3);
        padding: 20px;
        border-radius: 8px;
        position: relative;
        z-index: 1;
    }
    
    .telegram-qr {
        max-width: 200px;
        margin: 0 auto 15px;
        border: 4px solid white;
        border-radius: 5px;
    }
    
    .telegram-instructions {
        background-color: rgba(0, 0, 0, 0.3);
        border-left: 3px solid var(--telegram-color);
        padding: 15px;
        margin-top: 20px;
        font-size: 0.9em;
    }
    
    .telegram-instructions ol {
        padding-left: 25px;
        margin-top: 10px;
    }
    
    .telegram-instructions li {
        margin-bottom: 8px;
    }
</style>
{% endblock %}

{% block content %}
<div class="connect-page">
    <div class="connect-header">
        <h1 class="connect-title">Подключение Telegram</h1>
    </div>
    
    <div class="connect-content">
        <div class="connect-info">
            <p>Подключите свой аккаунт Telegram к профилю на PlayLand для получения дополнительных возможностей и удобства использования сервиса.</p>
            
            <h3>Преимущества подключения Telegram:</h3>
            <ul class="telegram-benefits">
                <li>Мгновенные уведомления об игровых событиях</li>
                <li>Быстрый вход на сайт без пароля</li>
                <li>Управление своим аккаунтом через бота</li>
                <li>Моментальный вайтлист на сервере</li>
                <li>Получение важных новостей и обновлений</li>
            </ul>
            
            {% if current_user.telegram_id and current_user.telegram_id[:7] != 'pending_' %}
                <div class="telegram-status connected">
                    <p><i class="fas fa-check-circle"></i> Ваш аккаунт Telegram успешно подключен!</p>
                    {% if current_user.avatar_url %}
                    <div class="telegram-username">
                        <img src="{{ current_user.avatar_url }}" alt="Telegram Avatar" class="telegram-avatar">
                        <span>{{ current_user.username }}</span>
                    </div>
                    {% endif %}
                    <a href="{{ url_for('disconnect_telegram') }}" class="telegram-btn" style="margin-top: 15px; background-color: #F04747;">
                        <i class="fas fa-unlink"></i> Отключить Telegram
                    </a>
                </div>
            {% else %}
                <div class="telegram-widget-container">
                    <img src="https://chart.googleapis.com/chart?cht=qr&chl=https%3A%2F%2Ft.me%2FPlayLandBot%3Fstart%3D{{ current_user.id }}&chs=250x250&chld=L|0" alt="Telegram Bot QR" class="telegram-qr">
                    <p>Сканируйте QR-код или нажмите кнопку ниже:</p>
                    <a href="https://t.me/PlayLandBot?start={{ current_user.id }}" class="telegram-btn" target="_blank">
                        <i class="fab fa-telegram-plane"></i> Подключить через Telegram
                    </a>
                </div>
                
                <div class="telegram-instructions">
                    <p><i class="fas fa-info-circle"></i> <strong>Как подключить Telegram:</strong></p>
                    <ol>
                        <li>Нажмите на кнопку "Подключить через Telegram" или отсканируйте QR-код</li>
                        <li>Откройте чат с нашим ботом @PlayLandBot</li>
                        <li>Нажмите кнопку "СТАРТ" или отправьте команду /start</li>
                        <li>Следуйте инструкциям бота для завершения процесса</li>
                        <li>После успешного подключения вернитесь на эту страницу и обновите её</li>
                    </ol>
                </div>
                
                {% if error %}
                <div class="telegram-status error">
                    <p><i class="fas fa-exclamation-triangle"></i> {{ error }}</p>
                </div>
                {% endif %}
            {% endif %}
        </div>
        
        <a href="{{ url_for('profile.view_profile') }}" class="back-link">⬅ Вернуться в профиль</a>
    </div>
</div>
{% endblock %} 