# Основные настройки Flask
SECRET_KEY=your-very-secure-secret-key-change-this-in-production
FLASK_ENV=development
FLASK_DEBUG=True

# База данных
DATABASE_URL=sqlite:///instance/playland_unified.db

# Безопасность
SECURITY_PASSWORD_SALT=your-unique-salt-for-passwords
API_SECRET_KEY=your-secure-api-key-for-telegram-integration

# Email настройки (для сброса паролей)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Telegram бот
TELEGRAM_BOT_TOKEN=your-telegram-bot-token-here
TELEGRAM_WEBHOOK_URL=https://yourdomain.com/webhook

# Discord интеграция
DISCORD_CLIENT_ID=your-discord-client-id
DISCORD_CLIENT_SECRET=your-discord-client-secret
DISCORD_BOT_TOKEN=your-discord-bot-token
DISCORD_GUILD_ID=your-discord-server-id
DISCORD_LOG_CHANNEL_ID=your-log-channel-id
DISCORD_REDIRECT_URI=http://localhost:5000/auth/discord/callback

# Rate Limiting (опционально, для Redis)
RATE_LIMIT_STORAGE_URL=redis://localhost:6379/0

# Minecraft сервер
MINECRAFT_SERVER_HOST=localhost
MINECRAFT_SERVER_PORT=25565
MINECRAFT_RCON_HOST=localhost
MINECRAFT_RCON_PORT=25575
MINECRAFT_RCON_PASSWORD=your-rcon-password

# Логирование
LOG_LEVEL=INFO
LOG_FILE=logs/playland.log

# Дополнительные настройки
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=16777216
ALLOWED_EXTENSIONS=png,jpg,jpeg,gif
