from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, TextAreaField, BooleanField, SubmitField, HiddenField
from wtforms.validators import DataRequired, Length, Email, EqualTo, Optional, ValidationError, Regexp
import re

class LoginForm(FlaskForm):
    """Форма для входа в систему"""
    username = StringField('Имя пользователя', validators=[
        DataRequired(message="Необходимо указать имя пользователя"),
        Length(min=3, max=100, message="Имя пользователя должно содержать от 3 до 100 символов")
    ])
    password = PasswordField('Пароль', validators=[
        DataRequired(message="Необходимо указать пароль")
    ])
    remember_me = BooleanField('За<PERSON>омнить меня')
    submit = SubmitField('Войти')

class RegisterForm(FlaskForm):
    """Форма для регистрации нового пользователя"""
    username = StringField('Имя пользователя', validators=[
        DataRequired(message="Необходимо указать имя пользователя"),
        Length(min=3, max=16, message="Имя пользователя должно содержать от 3 до 16 символов")
    ])
    email = StringField('Email', validators=[
        DataRequired(message="Необходимо указать email"),
        Email(message="Введите корректный email адрес"),
        Length(max=100, message="Email не должен превышать 100 символов")
    ])
    password = PasswordField('Пароль', validators=[
        DataRequired(message="Необходимо указать пароль"),
        Length(min=8, message="Пароль должен содержать не менее 8 символов")
    ])
    confirm_password = PasswordField('Подтверждение пароля', validators=[
        DataRequired(message="Необходимо подтвердить пароль"),
        EqualTo('password', message="Пароли не совпадают")
    ])
    submit = SubmitField('Зарегистрироваться')

    def validate_username(self, field):
        """Валидация формата никнейма"""
        pattern = re.compile(r'^[a-zA-Z0-9_]{3,16}$')
        if not pattern.match(field.data):
            raise ValidationError('Никнейм может содержать только латинские буквы, цифры и символ подчеркивания')

class ApplicationForm(FlaskForm):
    """Форма для подачи заявки на вайтлист"""
    nickname = StringField('Игровой никнейм (Minecraft)*', validators=[
        DataRequired(message="Необходимо указать игровой никнейм"),
        Length(min=3, max=16, message="Никнейм должен содержать от 3 до 16 символов")
    ])
    discord = StringField('Discord ID', validators=[
        Optional(),
        Length(max=100, message="Discord ID не должен превышать 100 символов")
    ])
    reason = TextAreaField('Почему вы хотите присоединиться к нашему серверу?', validators=[
        Optional(),
        Length(max=1000, message="Текст не должен превышать 1000 символов")
    ])
    submit = SubmitField('Отправить заявку')

    def validate_nickname(self, field):
        """Валидация формата никнейма"""
        pattern = re.compile(r'^[a-zA-Z0-9_]{3,16}$')
        if not pattern.match(field.data):
            raise ValidationError('Никнейм может содержать только латинские буквы, цифры и символ подчеркивания')

    def validate_discord(self, field):
        """Валидация формата Discord ID"""
        if field.data:
            # Проверка старого формата Discord (username#1234)
            old_format = re.compile(r'^[a-zA-Z0-9_\.]{2,32}#[0-9]{4}$')
            # Проверка нового формата Discord (@username)
            new_format = re.compile(r'^@[a-zA-Z0-9_\.]{2,32}$')

            if not (old_format.match(field.data) or new_format.match(field.data)):
                raise ValidationError('Введите корректный Discord ID (например, username#1234 или @username)')

class ProfileForm(FlaskForm):
    """Форма для редактирования профиля"""
    email = StringField('Email', validators=[
        DataRequired(message="Необходимо указать email"),
        Email(message="Введите корректный email адрес"),
        Length(max=100, message="Email не должен превышать 100 символов")
    ])

    nickname = StringField('Никнейм в игре', validators=[
        DataRequired(message="Необходимо указать никнейм"),
        Length(min=3, max=16, message="Никнейм должен содержать от 3 до 16 символов")
    ])

    bio = TextAreaField('О себе', validators=[
        Optional(),
        Length(max=500, message="Биография не должна превышать 500 символов")
    ])

    minecraft_experience = StringField('Опыт в Minecraft', validators=[
        Optional(),
        Length(max=50, message="Опыт не должен превышать 50 символов")
    ])

    interests = TextAreaField('Интересы (через запятую)', validators=[
        Optional(),
        Length(max=300, message="Интересы не должны превышать 300 символов")
    ])

    # Социальные сети
    discord = StringField('Discord', validators=[
        Optional(),
        Length(max=100, message="Discord ID не должен превышать 100 символов")
    ])

    youtube = StringField('YouTube', validators=[
        Optional(),
        Length(max=200, message="Ссылка не должна превышать 200 символов")
    ])

    twitch = StringField('Twitch', validators=[
        Optional(),
        Length(max=200, message="Ссылка не должна превышать 200 символов")
    ])

    vk = StringField('VK', validators=[
        Optional(),
        Length(max=200, message="Ссылка не должна превышать 200 символов")
    ])

    steam = StringField('Steam', validators=[
        Optional(),
        Length(max=200, message="Ссылка не должна превышать 200 символов")
    ])

    # Безопасность
    current_password = PasswordField('Текущий пароль', validators=[
        Optional()
    ])

    new_password = PasswordField('Новый пароль', validators=[
        Optional(),
        Length(min=8, message="Пароль должен содержать не менее 8 символов")
    ])

    confirm_new_password = PasswordField('Подтверждение нового пароля', validators=[
        EqualTo('new_password', message="Пароли не совпадают")
    ])

    submit = SubmitField('Сохранить изменения')

    def validate_discord(self, field):
        """Валидация формата Discord ID"""
        if field.data:
            # Проверка старого формата Discord (username#1234)
            old_format = re.compile(r'^[a-zA-Z0-9_\.]{2,32}#[0-9]{4}$')
            # Проверка нового формата Discord (@username)
            new_format = re.compile(r'^@?[a-zA-Z0-9_\.]{2,32}$')

            if not (old_format.match(field.data) or new_format.match(field.data)):
                raise ValidationError('Введите корректный Discord ID (например, username#1234 или @username)')

    def validate_youtube(self, field):
        """Валидация формата ссылки YouTube"""
        if field.data and not (
            field.data.startswith('https://www.youtube.com/') or
            field.data.startswith('https://youtube.com/') or
            field.data.startswith('https://youtu.be/')
        ):
            raise ValidationError('Введите корректную ссылку на YouTube канал')

    def validate_twitch(self, field):
        """Валидация формата ссылки Twitch"""
        if field.data and not (
            field.data.startswith('https://www.twitch.tv/') or
            field.data.startswith('https://twitch.tv/')
        ):
            raise ValidationError('Введите корректную ссылку на Twitch канал')

class AdminApplicationForm(FlaskForm):
    """Форма для администратора по обработке заявок"""
    status = StringField('Статус', validators=[
        DataRequired(message="Необходимо указать статус")
    ])
    admin_comment = TextAreaField('Комментарий администратора', validators=[
        Optional(),
        Length(max=1000, message="Комментарий не должен превышать 1000 символов")
    ])
    submit = SubmitField('Сохранить')

# Формы для безопасности и аутентификации

class PasswordResetRequestForm(FlaskForm):
    """Форма запроса сброса пароля"""
    email = StringField('Email', validators=[
        DataRequired(message="Необходимо указать email"),
        Email(message="Введите корректный email адрес")
    ])
    submit = SubmitField('Отправить ссылку для сброса')

class PasswordResetForm(FlaskForm):
    """Форма сброса пароля"""
    password = PasswordField('Новый пароль', validators=[
        DataRequired(message="Необходимо указать пароль"),
        Length(min=8, message="Пароль должен содержать не менее 8 символов")
    ])
    confirm_password = PasswordField('Подтверждение пароля', validators=[
        DataRequired(message="Необходимо подтвердить пароль"),
        EqualTo('password', message="Пароли не совпадают")
    ])
    submit = SubmitField('Сбросить пароль')

class TwoFactorSetupForm(FlaskForm):
    """Форма настройки двухфакторной аутентификации"""
    token = StringField('Код из приложения', validators=[
        DataRequired(message="Необходимо указать код"),
        Length(min=6, max=6, message="Код должен содержать 6 цифр")
    ])
    submit = SubmitField('Подтвердить настройку')

class TwoFactorForm(FlaskForm):
    """Форма ввода кода двухфакторной аутентификации"""
    token = StringField('Код двухфакторной аутентификации', validators=[
        DataRequired(message="Необходимо указать код"),
        Length(min=6, max=8, message="Код должен содержать 6-8 символов")
    ])
    remember_device = BooleanField('Запомнить это устройство на 30 дней')
    submit = SubmitField('Подтвердить')

class ChangePasswordForm(FlaskForm):
    """Форма изменения пароля"""
    current_password = PasswordField('Текущий пароль', validators=[
        DataRequired(message="Необходимо указать текущий пароль")
    ])
    new_password = PasswordField('Новый пароль', validators=[
        DataRequired(message="Необходимо указать новый пароль"),
        Length(min=8, message="Пароль должен содержать не менее 8 символов")
    ])
    confirm_password = PasswordField('Подтверждение нового пароля', validators=[
        DataRequired(message="Необходимо подтвердить новый пароль"),
        EqualTo('new_password', message="Пароли не совпадают")
    ])
    submit = SubmitField('Изменить пароль')

class DeleteAccountForm(FlaskForm):
    """Форма удаления аккаунта"""
    password = PasswordField('Пароль для подтверждения', validators=[
        DataRequired(message="Необходимо указать пароль для подтверждения")
    ])
    confirmation = StringField('Введите "УДАЛИТЬ" для подтверждения', validators=[
        DataRequired(message="Необходимо ввести подтверждение")
    ])
    submit = SubmitField('Удалить аккаунт')

    def validate_confirmation(self, field):
        if field.data.upper() != 'УДАЛИТЬ':
            raise ValidationError('Для подтверждения удаления введите "УДАЛИТЬ"')

class EmailVerificationForm(FlaskForm):
    """Форма подтверждения email"""
    submit = SubmitField('Отправить письмо подтверждения')

class SecuritySettingsForm(FlaskForm):
    """Форма настроек безопасности"""
    email_notifications = BooleanField('Уведомления о входе на email')
    login_alerts = BooleanField('Уведомления о подозрительной активности')
    session_timeout = BooleanField('Автоматический выход через 30 минут неактивности')
    submit = SubmitField('Сохранить настройки')