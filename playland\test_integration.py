#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Скрипт для тестирования интеграции между Telegram ботом и веб-сайтом PlayLand.
"""

import asyncio
import aiohttp
import json
import hmac
import hashlib
import os
import sys

# Добавляем пути для импорта
sys.path.append(os.path.join(os.path.dirname(__file__), 'tg_bot'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'website'))

async def test_website_api():
    """Тестирует API сайта"""
    print("🔍 Тестирование API сайта...")
    
    api_url = "http://localhost:5555/api/sync"
    secret_key = "sync-secret-key-change-in-production"
    
    # Отключаем SSL для локального тестирования
    connector = aiohttp.TCPConnector(ssl=False)
    async with aiohttp.ClientSession(connector=connector) as session:
        # Тест 1: Проверка здоровья API
        try:
            async with session.get(f"{api_url}/health") as response:
                if response.status == 200:
                    print("✅ API сайта доступен")
                else:
                    print(f"❌ API сайта недоступен: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ Ошибка подключения к API: {e}")
            return False
        
        # Тест 2: Создание пользователя
        try:
            data = {
                'telegram_id': 123456789,
                'username': 'test_user',
                'first_name': 'Test',
                'last_name': 'User'
            }
            
            json_data = json.dumps(data)
            signature = hmac.new(
                secret_key.encode(),
                json_data.encode(),
                hashlib.sha256
            ).hexdigest()
            
            headers = {
                'Content-Type': 'application/json',
                'X-Sync-Signature': signature
            }
            
            async with session.post(f"{api_url}/user/link", data=json_data, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get('success'):
                        print("✅ Создание пользователя работает")
                    else:
                        print(f"❌ Ошибка создания пользователя: {result}")
                else:
                    print(f"❌ Ошибка API создания пользователя: {response.status}")
        except Exception as e:
            print(f"❌ Ошибка тестирования создания пользователя: {e}")
        
        # Тест 3: Создание тикета
        try:
            data = {
                'telegram_id': 123456789,
                'title': 'Тестовый тикет',
                'content': 'Это тестовое сообщение для проверки интеграции',
                'application_type': 'support'
            }
            
            json_data = json.dumps(data)
            signature = hmac.new(
                secret_key.encode(),
                json_data.encode(),
                hashlib.sha256
            ).hexdigest()
            
            headers = {
                'Content-Type': 'application/json',
                'X-Sync-Signature': signature
            }
            
            async with session.post(f"{api_url}/ticket/create", data=json_data, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get('success'):
                        ticket_id = result.get('ticket_id')
                        print(f"✅ Создание тикета работает (ID: {ticket_id})")
                        return ticket_id
                    else:
                        print(f"❌ Ошибка создания тикета: {result}")
                else:
                    print(f"❌ Ошибка API создания тикета: {response.status}")
        except Exception as e:
            print(f"❌ Ошибка тестирования создания тикета: {e}")
    
    return True

async def test_bot_sync():
    """Тестирует модуль синхронизации бота"""
    print("\n🤖 Тестирование модуля синхронизации бота...")
    
    try:
        # Импортируем модуль синхронизации
        from website_sync import (
            sync_user_to_website,
            create_support_ticket,
            test_website_connection
        )
        
        print("✅ Модуль синхронизации импортирован")
        
        # Тест подключения
        if await test_website_connection():
            print("✅ Подключение к API работает")
        else:
            print("❌ Подключение к API не работает")
        
        # Тест синхронизации пользователя
        if await sync_user_to_website(123456789, 'test_user', 'Test', 'User'):
            print("✅ Синхронизация пользователя работает")
        else:
            print("❌ Синхронизация пользователя не работает")
        
        # Тест создания тикета
        ticket_id = await create_support_ticket(
            123456789, 
            'Тест из бота', 
            'Тестовое сообщение из модуля синхронизации бота'
        )
        
        if ticket_id:
            print(f"✅ Создание тикета из бота работает (ID: {ticket_id})")
        else:
            print("❌ Создание тикета из бота не работает")
            
    except ImportError as e:
        print(f"❌ Ошибка импорта модуля синхронизации: {e}")
    except Exception as e:
        print(f"❌ Ошибка тестирования бота: {e}")

def test_database():
    """Тестирует базу данных"""
    print("\n💾 Тестирование базы данных...")
    
    try:
        # Тест базы данных сайта
        import sqlite3
        
        # Проверяем базу данных сайта
        website_db = os.path.join(os.path.dirname(__file__), 'website', 'playland.db')
        if os.path.exists(website_db):
            print("✅ База данных сайта найдена")
            
            conn = sqlite3.connect(website_db)
            cursor = conn.cursor()
            
            # Проверяем таблицы
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            required_tables = ['users', 'applications', 'tickets', 'ticket_messages']
            for table in required_tables:
                if table in tables:
                    print(f"✅ Таблица {table} существует")
                else:
                    print(f"❌ Таблица {table} не найдена")
            
            conn.close()
        else:
            print("❌ База данных сайта не найдена")
        
        # Проверяем базу данных бота
        bot_db = os.path.join(os.path.dirname(__file__), 'tg_bot', 'playland_bot.db')
        if os.path.exists(bot_db):
            print("✅ База данных бота найдена")
        else:
            print("⚠️ База данных бота не найдена (будет создана при запуске)")
            
    except Exception as e:
        print(f"❌ Ошибка тестирования базы данных: {e}")

def check_configuration():
    """Проверяет конфигурацию"""
    print("\n⚙️ Проверка конфигурации...")
    
    # Проверяем переменные окружения
    required_vars = [
        'BOT_TOKEN',
        'ADMIN_IDS',
        'WEBAPP_URL',
        'SYNC_SECRET_KEY'
    ]
    
    env_file = os.path.join(os.path.dirname(__file__), 'tg_bot', '.env')
    if os.path.exists(env_file):
        print("✅ Файл .env найден")
    else:
        print("⚠️ Файл .env не найден, используйте .env.example как шаблон")
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            if var == 'BOT_TOKEN':
                print(f"✅ {var}: {'*' * 20}...{value[-5:]}")
            else:
                print(f"✅ {var}: {value}")
        else:
            print(f"❌ {var}: не установлена")

async def main():
    """Основная функция тестирования"""
    print("🚀 Тестирование интеграции PlayLand")
    print("=" * 50)
    
    # Проверка конфигурации
    check_configuration()
    
    # Тестирование базы данных
    test_database()
    
    # Тестирование API сайта
    await test_website_api()
    
    # Тестирование синхронизации бота
    await test_bot_sync()
    
    print("\n" + "=" * 50)
    print("✅ Тестирование завершено!")
    print("\n💡 Рекомендации:")
    print("1. Убедитесь, что сайт запущен на http://localhost:5555")
    print("2. Настройте переменные окружения в файле .env")
    print("3. Запустите бота для полного тестирования")

if __name__ == "__main__":
    asyncio.run(main())
