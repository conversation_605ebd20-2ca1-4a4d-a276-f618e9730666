{% extends "base.html" %}

{% block title %}{{ page_title }} - Требуется авторизация{% endblock %}

{% block content %}
<div class="auth-required-container">
    <div class="auth-required-card">
        <div class="auth-icon">
            <i class="fas fa-lock"></i>
        </div>
        
        <h1 class="auth-title">Требуется авторизация</h1>
        
        <p class="auth-message">{{ message }}</p>
        
        <div class="auth-actions">
            <a href="{{ url_for('auth.enhanced_login') }}" class="btn btn-primary">
                <i class="fas fa-sign-in-alt"></i>
                Войти в систему
            </a>
            <a href="{{ url_for('auth.enhanced_register') }}" class="btn btn-secondary">
                <i class="fas fa-user-plus"></i>
                Зарегистрироваться
            </a>
        </div>
        
        <div class="auth-info">
            <h3>Зачем нужна регистрация?</h3>
            <ul>
                <li><i class="fas fa-ticket-alt"></i> Создание и отслеживание тикетов поддержки</li>
                <li><i class="fas fa-file-alt"></i> Подача заявок на сервер</li>
                <li><i class="fas fa-user"></i> Персональный профиль игрока</li>
                <li><i class="fas fa-history"></i> История всех обращений</li>
                <li><i class="fas fa-bell"></i> Уведомления о статусе заявок</li>
            </ul>
        </div>
        
        <div class="auth-alternative">
            <p><strong>Есть Telegram?</strong></p>
            <p>Вы также можете обратиться в поддержку через наш Telegram бот:</p>
            <a href="https://t.me/playland_bot" class="btn btn-telegram" target="_blank">
                <i class="fab fa-telegram"></i>
                Написать в Telegram
            </a>
        </div>
    </div>
</div>

<style>
/* Добавляем фон для всей страницы */
body {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
    background-attachment: fixed;
    position: relative;
    overflow-x: hidden;
}

/* Анимированный фон с частицами */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(0, 255, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(0, 255, 0, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(0, 255, 0, 0.03) 0%, transparent 50%);
    animation: backgroundShift 20s ease-in-out infinite;
    z-index: -1;
}

@keyframes backgroundShift {
    0%, 100% {
        transform: translateX(0) translateY(0);
    }
    25% {
        transform: translateX(-10px) translateY(-10px);
    }
    50% {
        transform: translateX(10px) translateY(10px);
    }
    75% {
        transform: translateX(-5px) translateY(5px);
    }
}

.auth-required-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    position: relative;
}

.auth-required-card {
    background: rgba(0, 0, 0, 0.95);
    border: 2px solid rgba(0, 255, 0, 0.4);
    border-radius: 20px;
    padding: 50px;
    max-width: 650px;
    width: 100%;
    text-align: center;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.7),
        0 0 50px rgba(0, 255, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    animation: fadeInUp 0.8s ease;
    position: relative;
    backdrop-filter: blur(10px);
}

.auth-required-card::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg,
        rgba(0, 255, 0, 0.3),
        rgba(0, 255, 0, 0.1),
        rgba(0, 255, 0, 0.3));
    border-radius: 22px;
    z-index: -1;
    animation: borderGlow 3s ease-in-out infinite;
}

@keyframes borderGlow {
    0%, 100% {
        opacity: 0.3;
    }
    50% {
        opacity: 0.6;
    }
}

.auth-icon {
    font-size: 4rem;
    color: #00ff00;
    margin-bottom: 20px;
    animation: pulse 2s infinite;
}

.auth-title {
    color: #00ff00;
    font-size: 2rem;
    margin-bottom: 15px;
    font-family: var(--pixel-font);
}

.auth-message {
    color: #ccc;
    font-size: 1.1rem;
    margin-bottom: 30px;
    line-height: 1.5;
}

.auth-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.auth-info {
    background: rgba(0, 255, 0, 0.1);
    border: 1px solid rgba(0, 255, 0, 0.2);
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 30px;
    text-align: left;
}

.auth-info h3 {
    color: #00ff00;
    margin-bottom: 15px;
    text-align: center;
}

.auth-info ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.auth-info li {
    color: #fff;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.auth-info li i {
    color: #00ff00;
    width: 20px;
    text-align: center;
}

.auth-alternative {
    background: rgba(0, 123, 255, 0.1);
    border: 1px solid rgba(0, 123, 255, 0.2);
    border-radius: 10px;
    padding: 20px;
}

.auth-alternative p {
    color: #ccc;
    margin-bottom: 10px;
}

.btn-telegram {
    background: linear-gradient(45deg, #0088cc, #0099dd);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    margin-top: 10px;
}

.btn-telegram:hover {
    background: linear-gradient(45deg, #0099dd, #00aaee);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 136, 204, 0.4);
    color: white;
    text-decoration: none;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

@media (max-width: 768px) {
    .auth-required-container {
        padding: 20px 10px;
        min-height: 100vh;
    }

    .auth-required-card {
        padding: 40px 25px;
        margin: 0;
        max-width: 100%;
        border-radius: 15px;
    }

    .auth-required-card::before {
        border-radius: 17px;
    }

    .auth-actions {
        flex-direction: column;
        align-items: center;
        gap: 12px;
    }

    .auth-actions .btn {
        width: 100%;
        max-width: 280px;
        padding: 15px 20px;
        font-size: 1rem;
    }

    .auth-title {
        font-size: 1.6rem;
        margin-bottom: 20px;
    }

    .auth-icon {
        font-size: 3.5rem;
        margin-bottom: 25px;
    }

    .auth-message {
        font-size: 1rem;
        margin-bottom: 25px;
    }

    .auth-info {
        padding: 20px;
        margin-bottom: 25px;
    }

    .auth-alternative {
        padding: 15px;
    }
}

/* Дополнительные стили для кнопок */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: linear-gradient(45deg, #00ff00, #32ff32);
    color: #000;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #32ff32, #00ff00);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 255, 0, 0.4);
    color: #000;
    text-decoration: none;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    color: #fff;
    text-decoration: none;
}
</style>
{% endblock %}
