#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Discord интеграция для PlayLand.
Обеспечивает синхронизацию между веб-сайтом и Discord сервером.
"""

import logging
import requests
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

class DiscordIntegration:
    """Класс для интеграции с Discord"""
    
    def __init__(self, app=None):
        self.app = app
        self.bot_token = None
        self.guild_id = None
        self.log_channel_id = None
        self.client_id = None
        self.client_secret = None
        self.enabled = False
        
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """Инициализация с Flask приложением"""
        self.app = app
        self.bot_token = app.config.get('DISCORD_BOT_TOKEN')
        self.guild_id = app.config.get('DISCORD_GUILD_ID')
        self.log_channel_id = app.config.get('DISCORD_LOG_CHANNEL_ID')
        self.client_id = app.config.get('DISCORD_CLIENT_ID')
        self.client_secret = app.config.get('DISCORD_CLIENT_SECRET')
        
        if self.bot_token and self.guild_id:
            self.enabled = True
            logger.info("Discord integration enabled")
        else:
            logger.warning("Discord integration not fully configured")
    
    def send_message(self, channel_id: str, content: str = None, 
                    embed: Dict[str, Any] = None) -> bool:
        """Отправка сообщения в Discord канал"""
        if not self.enabled:
            logger.warning("Discord integration disabled")
            return False
        
        try:
            url = f"https://discord.com/api/v10/channels/{channel_id}/messages"
            headers = {
                'Authorization': f'Bot {self.bot_token}',
                'Content-Type': 'application/json'
            }
            
            data = {}
            if content:
                data['content'] = content
            if embed:
                data['embeds'] = [embed]
            
            response = requests.post(url, json=data, headers=headers, timeout=10)
            response.raise_for_status()
            
            logger.info(f"Message sent to Discord channel {channel_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send Discord message: {e}")
            return False
    
    def send_log_message(self, content: str = None, embed: Dict[str, Any] = None) -> bool:
        """Отправка сообщения в лог канал"""
        if not self.log_channel_id:
            logger.warning("Discord log channel not configured")
            return False
        
        return self.send_message(self.log_channel_id, content, embed)
    
    def create_application_embed(self, application_data: Dict[str, Any]) -> Dict[str, Any]:
        """Создание embed для заявки"""
        embed = {
            "title": "🎮 Новая заявка на сервер",
            "color": 0x00ff00,  # Зеленый цвет
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "fields": [
                {
                    "name": "👤 Никнейм",
                    "value": f"`{application_data.get('minecraft_nickname', 'Не указан')}`",
                    "inline": True
                },
                {
                    "name": "🔖 Discord",
                    "value": f"`{application_data.get('discord_tag', 'Не указан')}`",
                    "inline": True
                },
                {
                    "name": "👶 Возраст",
                    "value": str(application_data.get('age', 'Не указан')),
                    "inline": True
                },
                {
                    "name": "📧 Email",
                    "value": f"`{application_data.get('email', 'Не указан')}`",
                    "inline": False
                },
                {
                    "name": "📝 О себе",
                    "value": application_data.get('about_yourself', 'Не указано')[:1000],
                    "inline": False
                },
                {
                    "name": "🔍 Как нашел сервер",
                    "value": application_data.get('find_server', 'Не указано'),
                    "inline": False
                }
            ],
            "footer": {
                "text": "PlayLand Server Application"
            }
        }
        
        return embed
    
    def create_team_application_embed(self, application_data: Dict[str, Any]) -> Dict[str, Any]:
        """Создание embed для заявки в команду"""
        embed = {
            "title": "📧 Новая заявка в команду",
            "color": 0xff9900,  # Оранжевый цвет
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "fields": [
                {
                    "name": "👤 Никнейм",
                    "value": f"`{application_data.get('minecraft_nickname', 'Не указан')}`",
                    "inline": True
                },
                {
                    "name": "🔖 Discord",
                    "value": f"`{application_data.get('discord_tag', 'Не указан')}`",
                    "inline": True
                },
                {
                    "name": "👶 Возраст",
                    "value": str(application_data.get('age', 'Не указан')),
                    "inline": True
                },
                {
                    "name": "🎯 Желаемая должность",
                    "value": application_data.get('position', 'Не указана'),
                    "inline": False
                },
                {
                    "name": "💼 Опыт",
                    "value": application_data.get('experience', 'Не указан')[:1000],
                    "inline": False
                },
                {
                    "name": "💡 Мотивация",
                    "value": application_data.get('motivation', 'Не указана')[:1000],
                    "inline": False
                }
            ],
            "footer": {
                "text": "PlayLand Team Application"
            }
        }
        
        return embed
    
    def send_application_notification(self, application_data: Dict[str, Any]) -> bool:
        """Отправка уведомления о новой заявке"""
        try:
            embed = self.create_application_embed(application_data)
            return self.send_log_message(embed=embed)
            
        except Exception as e:
            logger.error(f"Failed to send Discord application notification: {e}")
            return False
    
    def send_team_application_notification(self, application_data: Dict[str, Any]) -> bool:
        """Отправка уведомления о заявке в команду"""
        try:
            embed = self.create_team_application_embed(application_data)
            return self.send_log_message(embed=embed)
            
        except Exception as e:
            logger.error(f"Failed to send Discord team application notification: {e}")
            return False
    
    def get_guild_info(self) -> Optional[Dict[str, Any]]:
        """Получение информации о сервере Discord"""
        if not self.enabled:
            return None
        
        try:
            url = f"https://discord.com/api/v10/guilds/{self.guild_id}"
            headers = {
                'Authorization': f'Bot {self.bot_token}'
            }
            
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            
            return response.json()
            
        except Exception as e:
            logger.error(f"Failed to get guild info: {e}")
            return None
    
    def get_user_info(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Получение информации о пользователе Discord"""
        if not self.enabled:
            return None
        
        try:
            url = f"https://discord.com/api/v10/users/{user_id}"
            headers = {
                'Authorization': f'Bot {self.bot_token}'
            }
            
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            
            return response.json()
            
        except Exception as e:
            logger.error(f"Failed to get user info: {e}")
            return None
    
    def verify_user_in_guild(self, user_id: str) -> bool:
        """Проверка, состоит ли пользователь в Discord сервере"""
        if not self.enabled:
            return False
        
        try:
            url = f"https://discord.com/api/v10/guilds/{self.guild_id}/members/{user_id}"
            headers = {
                'Authorization': f'Bot {self.bot_token}'
            }
            
            response = requests.get(url, headers=headers, timeout=10)
            return response.status_code == 200
            
        except Exception as e:
            logger.error(f"Failed to verify user in guild: {e}")
            return False

# Глобальный экземпляр интеграции
discord_integration = DiscordIntegration()

def init_discord_integration(app):
    """Инициализация Discord интеграции"""
    discord_integration.init_app(app)
    
    # Добавляем маршруты для OAuth
    @app.route('/api/discord/oauth/callback')
    def discord_oauth_callback():
        """Callback для Discord OAuth"""
        from flask import request, jsonify, redirect, url_for
        
        try:
            code = request.args.get('code')
            if not code:
                return jsonify({'error': 'No authorization code'}), 400
            
            # Здесь будет логика обмена кода на токен
            logger.info(f"Discord OAuth callback with code: {code}")
            
            return redirect(url_for('index'))
            
        except Exception as e:
            logger.error(f"Discord OAuth callback error: {e}")
            return jsonify({'error': 'OAuth error'}), 500
    
    logger.info("Discord integration initialized successfully")

# Функции для использования в других модулях
def send_application_notification(application_data: Dict[str, Any]) -> bool:
    """Отправка уведомления о новой заявке"""
    return discord_integration.send_application_notification(application_data)

def send_team_application_notification(application_data: Dict[str, Any]) -> bool:
    """Отправка уведомления о заявке в команду"""
    return discord_integration.send_team_application_notification(application_data)
