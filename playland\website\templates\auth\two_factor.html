{% extends "base.html" %}

{% block title %}Двухфакторная аутентификация - PlayLand{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">Двухфакторная аутентификация</h4>
                </div>
                <div class="card-body">
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    <div class="text-center mb-4">
                        <i class="fas fa-shield-alt fa-3x text-primary mb-3"></i>
                        <p class="text-muted">Введите код из приложения аутентификации или используйте резервный код</p>
                    </div>

                    <form method="POST" action="{{ url_for('auth.two_factor') }}">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-3">
                            {{ form.token.label(class="form-label") }}
                            {{ form.token(class="form-control text-center", style="font-size: 1.2em; letter-spacing: 0.2em;", placeholder="000000", maxlength="8") }}
                            {% if form.token.errors %}
                                <div class="text-danger">
                                    {% for error in form.token.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">
                                Введите 6-значный код из приложения или 8-значный резервный код
                            </small>
                        </div>

                        <div class="mb-3 form-check">
                            {{ form.remember_device(class="form-check-input") }}
                            {{ form.remember_device.label(class="form-check-label") }}
                            <small class="form-text text-muted d-block">
                                Не рекомендуется на общих компьютерах
                            </small>
                        </div>

                        <div class="d-grid">
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </form>

                    <hr>

                    <div class="text-center">
                        <p class="mb-2">Проблемы с входом?</p>
                        <a href="{{ url_for('auth.enhanced_login') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Вернуться к входу
                        </a>
                    </div>

                    <div class="mt-4">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> Подсказки:</h6>
                            <ul class="mb-0">
                                <li>Используйте приложение Google Authenticator, Authy или аналогичное</li>
                                <li>Если потеряли доступ к приложению, используйте резервный код</li>
                                <li>Резервные коды можно найти в настройках безопасности</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

.alert {
    border-radius: 0.375rem;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.text-primary {
    color: #007bff !important;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

.btn-outline-secondary {
    color: #6c757d;
    border-color: #6c757d;
}

.btn-outline-secondary:hover {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    color: #0056b3;
    text-decoration: underline;
}
</style>

<script>
// Автофокус на поле ввода кода
document.addEventListener('DOMContentLoaded', function() {
    const tokenInput = document.getElementById('token');
    if (tokenInput) {
        tokenInput.focus();
        
        // Автоматическая отправка формы при вводе 6 цифр
        tokenInput.addEventListener('input', function() {
            if (this.value.length === 6 && /^\d{6}$/.test(this.value)) {
                // Небольшая задержка для лучшего UX
                setTimeout(() => {
                    this.form.submit();
                }, 500);
            }
        });
    }
});
</script>
{% endblock %}
