{% extends "base.html" %}

{% block title %}Редактирование профиля - PlayLand{% endblock %}

{% block head_extra %}
<style>
    :root {
        --minecraft-grass: #5B9C3B;
        --minecraft-dirt: #8C5E39;
        --minecraft-stone: #919191;
        --minecraft-wood: #73553C;
        --minecraft-planks: #B88E5F;
    }

    .edit-profile-page {
        max-width: 700px;
        margin: 30px auto;
        padding: 0;
        animation: fadeIn 0.8s ease-out forwards;
        position: relative;
    }

    .edit-header {
        background-color: rgba(0, 0, 0, 0.7);
        padding: 25px;
        position: relative;
        border: 4px solid var(--minecraft-stone);
        margin-bottom: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        overflow: hidden;
        text-align: center;
    }

    .edit-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: linear-gradient(90deg, transparent, var(--glow-green), transparent);
        animation: headerGlow 3s infinite;
    }

    @keyframes headerGlow {
        0%, 100% { opacity: 0.5; }
        50% { opacity: 1; }
    }

    .edit-profile-page h1 {
        font-family: var(--pixel-font);
        font-size: 2em;
        color: var(--glow-green);
        text-shadow: 2px 2px 0 #000, 0 0 10px var(--glow-green);
        margin-bottom: 15px;
        text-transform: uppercase;
        letter-spacing: 2px;
    }

    .minecraft-blocks {
        display: flex;
        justify-content: center;
        gap: 15px;
        margin: 20px 0;
    }

    .edit-form {
        background-color: rgba(0, 0, 0, 0.6);
        border: 4px solid var(--minecraft-dirt);
        padding: 25px;
        position: relative;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        overflow: hidden;
    }

    .edit-form::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: repeating-linear-gradient(
            45deg,
            rgba(255,255,255,0.03),
            rgba(255,255,255,0.03) 10px,
            rgba(0,0,0,0.05) 10px,
            rgba(0,0,0,0.05) 20px
        );
        pointer-events: none;
        z-index: 0;
    }

    .form-group {
        margin-bottom: 25px;
        position: relative;
        z-index: 1;
    }

    .form-group label {
        display: block;
        margin-bottom: 10px;
        font-family: var(--pixel-font);
        font-size: 0.9em;
        color: var(--glow-green);
        text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.8);
    }

    .form-group input[type="text"],
    .form-group input[type="email"],
    .form-group input[type="password"],
    .form-group textarea {
        width: 100%;
        padding: 12px 15px;
        background-color: rgba(0, 0, 0, 0.5);
        border: 2px solid var(--minecraft-wood);
        color: white;
        border-radius: 0;
        font-family: var(--main-font);
        font-size: 1em;
        transition: all 0.3s ease;
        box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.5);
    }

    .form-group input:focus,
    .form-group textarea:focus {
        outline: none;
        box-shadow: 0 0 15px rgba(0, 255, 0, 0.3), inset 0 0 10px rgba(0, 0, 0, 0.5);
        border-color: var(--glow-green);
    }

    .form-error {
        color: #ff4136;
        font-size: 0.9em;
        margin-top: 8px;
        text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
    }

    .avatar-section {
        display: flex;
        align-items: center;
        margin-bottom: 30px;
        background-color: rgba(0, 0, 0, 0.3);
        padding: 15px;
        border-left: 3px solid var(--minecraft-grass);
    }

    .current-avatar {
        width: 100px;
        height: 100px;
        object-fit: cover;
        border: 3px solid var(--glow-green);
        margin-right: 20px;
        transition: all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
        image-rendering: pixelated;
    }

    .current-avatar:hover {
        transform: scale(1.1) rotate(5deg);
        box-shadow: 0 0 20px rgba(0, 255, 0, 0.5);
    }

    .avatar-upload {
        flex: 1;
    }

    .submit-btn {
        display: inline-block;
        width: 100%;
        padding: 15px 25px;
        background-color: var(--minecraft-grass);
        color: white;
        border: 4px solid rgba(0, 0, 0, 0.8);
        text-decoration: none;
        font-family: var(--pixel-font);
        font-size: 1rem;
        position: relative;
        text-align: center;
        text-shadow: 2px 2px 0px rgba(0, 0, 0, 0.8);
        box-shadow: inset -4px -4px 0px 0px rgba(0, 0, 0, 0.3), 
                    inset 4px 4px 0px 0px rgba(255, 255, 255, 0.3);
        cursor: pointer;
        transition: all 0.1s;
        transform-style: preserve-3d;
        image-rendering: pixelated;
        letter-spacing: 1px;
        overflow: hidden;
        margin-top: 30px;
        border: none;
    }

    .submit-btn::before {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        top: 8px;
        left: 0;
        background-color: rgba(0, 0, 0, 0.3);
        z-index: -1;
        transform: translateZ(-1px);
        transition: all 0.2s;
    }

    .submit-btn:hover {
        transform: translate(0, -3px);
    }

    .submit-btn:hover::before {
        top: 11px;
    }

    .submit-btn:active {
        transform: translate(0, 5px);
    }

    .submit-btn:active::before {
        top: 3px;
    }

    .cancel-link {
        display: block;
        text-align: center;
        margin-top: 20px;
        color: white;
        text-decoration: none;
        font-family: var(--pixel-font);
        font-size: 0.8em;
        transition: all 0.3s;
        position: relative;
        z-index: 1;
    }

    .cancel-link:hover {
        color: var(--glow-green);
        text-shadow: 0 0 10px var(--glow-green);
    }

    @media (max-width: 768px) {
        .edit-profile-page {
            padding: 0 15px;
            margin: 20px 15px;
        }
        .avatar-section {
            flex-direction: column;
            align-items: flex-start;
        }
        .current-avatar {
            margin-bottom: 15px;
        }
        .minecraft-blocks {
            flex-wrap: wrap;
        }
    }

    /* Стилизация input[type="file"] */
    input[type="file"] {
        width: 0.1px;
        height: 0.1px;
        opacity: 0;
        overflow: hidden;
        position: absolute;
        z-index: -1;
    }

    .file-input-label {
        display: inline-block;
        padding: 10px 15px;
        background-color: var(--minecraft-wood);
        color: white;
        font-family: var(--pixel-font);
        font-size: 0.8rem;
        cursor: pointer;
        transition: all 0.2s;
        text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.8);
        border: 2px solid rgba(0, 0, 0, 0.5);
        box-shadow: inset -2px -2px 0 rgba(0, 0, 0, 0.3), 
                    inset 2px 2px 0 rgba(255, 255, 255, 0.2);
    }

    .file-input-label:hover {
        background-color: var(--minecraft-planks);
        transform: translateY(-2px);
    }

    .file-input-label:active {
        transform: translateY(1px);
    }

    .file-name {
        margin-top: 5px;
        font-size: 0.8em;
        color: #aaa;
        font-style: italic;
    }
    
    /* Добавляем стили для секций */
    .profile-section {
        border-left: 3px solid var(--minecraft-grass);
        margin-bottom: 30px;
        padding: 15px;
        background-color: rgba(0, 0, 0, 0.3);
        position: relative;
    }
    
    .profile-section h3 {
        font-family: var(--pixel-font);
        color: white;
        margin-top: 0;
        margin-bottom: 20px;
        font-size: 1.3em;
        text-shadow: 1px 1px 0 #000;
    }
    
    .social-links-section {
        border-color: var(--minecraft-planks);
    }
    
    .security-section {
        border-color: var(--minecraft-stone);
    }
    
    .social-icon {
        width: 24px;
        height: 24px;
        margin-right: 10px;
        vertical-align: middle;
    }
</style>
{% endblock %}

{% block content %}
<div class="edit-profile-page">
    <div class="edit-header">
        <h1>Редактирование профиля</h1>
        <div class="minecraft-blocks">
            <div class="minecraft-block grass-block"></div>
            <div class="minecraft-block wood-block"></div>
        </div>
    </div>
    
    <div class="edit-form">
        <form method="POST" action="{{ url_for('profile.edit_profile') }}" enctype="multipart/form-data">
            {{ form.csrf_token }}
            
            <div class="avatar-section">
                <img src="{{ user.avatar_url or url_for('static', filename='images/default_avatar.png') }}" 
                     alt="Аватар {{ user.nickname or user.username }}" class="current-avatar">
                
                <div class="avatar-upload">
                    <div class="form-group">
                        <label for="avatar">Изменить аватар</label>
                        <input type="file" id="avatar" name="avatar" accept="image/*">
                        <label for="avatar" class="file-input-label">
                            <i class="fas fa-upload"></i> Выбрать файл
                        </label>
                        <div class="file-name" id="file-name">Файл не выбран</div>
                    </div>
                </div>
            </div>
            
            <div class="profile-section">
                <h3><i class="fas fa-user"></i> Основная информация</h3>
                
                <div class="form-group">
                    {{ form.nickname.label }}
                    {{ form.nickname(class_="form-control") }}
                    {% if form.nickname.errors %}
                        {% for error in form.nickname.errors %}
                            <div class="form-error">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                </div>
                
                <div class="form-group">
                    {{ form.email.label }}
                    {{ form.email(class_="form-control") }}
                    {% if form.email.errors %}
                        {% for error in form.email.errors %}
                            <div class="form-error">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                </div>
                
                <div class="form-group">
                    {{ form.discord.label }}
                    {{ form.discord(class_="form-control", placeholder="username#0000 или @username") }}
                    {% if form.discord.errors %}
                        {% for error in form.discord.errors %}
                            <div class="form-error">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                </div>
            </div>

            <div class="profile-section">
                <h3><i class="fas fa-gamepad"></i> Игровой профиль</h3>

                <div class="form-group">
                    {{ form.minecraft_experience.label }}
                    {{ form.minecraft_experience(class_="form-control", placeholder="Например: Новичок, Опытный, Эксперт") }}
                    {% if form.minecraft_experience.errors %}
                        {% for error in form.minecraft_experience.errors %}
                            <div class="form-error">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                </div>
                
                <div class="form-group">
                    {{ form.bio.label }}
                    {{ form.bio(class_="form-control", rows="4", placeholder="Расскажите немного о себе...") }}
                    {% if form.bio.errors %}
                        {% for error in form.bio.errors %}
                            <div class="form-error">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                </div>
                
                <div class="form-group">
                    {{ form.interests.label }}
                    {{ form.interests(class_="form-control", placeholder="PvP, строительство, редстоун, фермы...") }}
                    {% if form.interests.errors %}
                        {% for error in form.interests.errors %}
                            <div class="form-error">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                </div>
            </div>
                
            <div class="profile-section social-links-section">
                <h3><i class="fas fa-share-alt"></i> Социальные сети</h3>
                
                <div class="form-group">
                    <label for="{{ form.youtube.id }}">
                        <i class="fab fa-youtube" style="color: #FF0000"></i> {{ form.youtube.label }}
                    </label>
                    {{ form.youtube(class_="form-control", placeholder="https://youtube.com/channel/...") }}
                    {% if form.youtube.errors %}
                        {% for error in form.youtube.errors %}
                            <div class="form-error">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.twitch.id }}">
                        <i class="fab fa-twitch" style="color: #9146FF"></i> {{ form.twitch.label }}
                    </label>
                    {{ form.twitch(class_="form-control", placeholder="https://twitch.tv/...") }}
                    {% if form.twitch.errors %}
                        {% for error in form.twitch.errors %}
                            <div class="form-error">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.vk.id }}">
                        <i class="fab fa-vk" style="color: #4C75A3"></i> {{ form.vk.label }}
                    </label>
                    {{ form.vk(class_="form-control", placeholder="https://vk.com/...") }}
                    {% if form.vk.errors %}
                        {% for error in form.vk.errors %}
                            <div class="form-error">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.steam.id }}">
                        <i class="fab fa-steam" style="color: #1b2838"></i> {{ form.steam.label }}
                    </label>
                    {{ form.steam(class_="form-control", placeholder="https://steamcommunity.com/id/...") }}
                    {% if form.steam.errors %}
                        {% for error in form.steam.errors %}
                            <div class="form-error">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                </div>
            </div>
            
            <div class="profile-section security-section">
                <h3><i class="fas fa-shield-alt"></i> Безопасность</h3>
                <p style="color: #aaa; font-size: 0.9em; margin-bottom: 20px;">
                    Заполните эти поля, только если хотите изменить пароль или email.
                </p>
                
                <div class="form-group">
                    {{ form.current_password.label }}
                    {{ form.current_password(class_="form-control") }}
                    {% if form.current_password.errors %}
                        {% for error in form.current_password.errors %}
                            <div class="form-error">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                </div>
                
                <div class="form-group">
                    {{ form.new_password.label }}
                    {{ form.new_password(class_="form-control") }}
                    {% if form.new_password.errors %}
                        {% for error in form.new_password.errors %}
                            <div class="form-error">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                </div>
                
                <div class="form-group">
                    {{ form.confirm_new_password.label }}
                    {{ form.confirm_new_password(class_="form-control") }}
                    {% if form.confirm_new_password.errors %}
                        {% for error in form.confirm_new_password.errors %}
                            <div class="form-error">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                </div>
            </div>
            
            <button type="submit" class="submit-btn">Сохранить изменения</button>
            <a href="{{ url_for('profile.view_profile') }}" class="cancel-link">⬅ Вернуться в профиль</a>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Отображение имени выбранного файла
    document.getElementById('avatar').addEventListener('change', function() {
        var fileName = this.files[0] ? this.files[0].name : 'Файл не выбран';
        document.getElementById('file-name').textContent = fileName;
    });
    
    // Анимация для блоков Minecraft
    const blocks = document.querySelectorAll('.minecraft-block');
    blocks.forEach((block, index) => {
        block.addEventListener('click', function() {
            this.style.animation = 'blockDestroy 1.5s steps(10)';
            setTimeout(() => {
                this.style.animation = '';
            }, 1500);
        });
        
        // Добавляем задержку для анимации появления
        block.style.opacity = '0';
        block.style.transform = 'scale(0)';
        setTimeout(() => {
            block.style.transition = 'all 0.5s cubic-bezier(0.68, -0.55, 0.27, 1.55)';
            block.style.opacity = '1';
            block.style.transform = 'scale(1)';
        }, 100 + index * 150);
    });
});
</script>
{% endblock %} 